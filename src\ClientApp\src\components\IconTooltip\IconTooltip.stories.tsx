import { Flex } from '@chakra-ui/react';
import { Meta, StoryFn } from '@storybook/react';
import { FiClipboard, FiEdit, FiSave } from 'react-icons/fi';

import { IconTooltip as Icon, IconTooltipProps } from '.';

export default {
  title: 'Components/IconTooltip',
  args: {
    labelTooltip: 'Texto do tooltip',
    placement: 'bottom',
    hasArrow: true,
  },
} as Meta;

export const IconTooltip: StoryFn<IconTooltipProps> = (props) => {
  return (
    <Flex alignItems="center" gap="6px" justifyContent="center">
      <Icon {...props} icon={FiEdit} boxSize="18" />
      <Icon {...props} icon={FiClipboard} boxSize="18" labelTooltip="Copiar" />
      <Icon {...props} icon={FiSave} boxSize="18" labelTooltip="Salvar" />
    </Flex>
  );
};
