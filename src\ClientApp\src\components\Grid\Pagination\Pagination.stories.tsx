import { Meta } from '@storybook/react';
import { useCallback, useState } from 'react';
import { Box, Flex, Td, Tr } from '@chakra-ui/react';

import { dataPagination } from 'services/dataStorybook';

import { Pagination as PaginationDefault } from '.';

export default {
  title: 'Components/Paginação',
  component: PaginationDefault,
} as Meta;

type ListPaginationProps = {
  name: string;
  number: string;
};

export const Pagination = () => {
  const [listPagination, setListPagination] = useState<ListPaginationProps[]>(
    []
  );
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);

  const paginationHandle = useCallback(async () => {
    setIsLoading(true);

    setTotalRegistros(10);
    setListPagination(dataPagination);

    setIsLoading(false);
  }, []);

  // o valor da paginação ficou errado porque não tem uma integração com o back end
  // essa paginação recebe os dados do back e trata eles
  // como aqui foi feito para exibir o componente o seu valor final fica errado
  // mas entrando na pasta pages > cliente > Listar > index. Você já entende como a função paginationHandle é feita

  return (
    <Box>
      <PaginationDefault
        loadColumnsData={paginationHandle}
        defaultKeyOrdered="name"
        isLoading={isLoading}
        defaultOrderDirection="desc"
        setCurrentPage={setPage}
        nPages={totalRegistros}
        currentPage={page}
        tableHeaders={[
          {
            key: 'name',
            content: 'Nome',
            width: '50%',
            justifyContent: 'left',
          },
          {
            key: 'number',
            content: 'Numero',
            isNumeric: true,
          },
        ]}
        renderTableRows={listPagination.map((item) => (
          <>
            <Tr
              sx={{
                '& > td': {
                  height: '55px',
                  whiteSpace: 'nowrap',
                },
              }}
            >
              <Td pt="0" pb="0" w="40%" color="black">
                <Flex justifyContent="left">{item.name}</Flex>
              </Td>

              <Td pt="0" pb="2px" isNumeric w="30%" color="black">
                <Flex minW="200px" justifyContent="right">
                  {item.number}
                </Flex>
              </Td>
            </Tr>
          </>
        ))}
      />
    </Box>
  );
};
