import React, { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import { LayoutFormPage } from 'components/Layout/FormPage';

import FormTema from '..';
import { yupResolver, formDefaultValues, FormData } from '../validationForm';

const CadastrarTema = () => {
  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, reset } = formMethods;
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(false);

  const handleCadastrar = async (data: FormData, resetAfterSuccess = false) => {
    setIsLoading(true);
    const response = await api.post<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.CADASTRAR_TEMA,
      data
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso) {
        toast.success('Cadastro realizado com sucesso');
        if (resetAfterSuccess) {
          reset(formDefaultValues);
        } else {
          navigate(ConstanteRotas.TEMAS);
        }
      }
    }
    setIsLoading(false);
  };

  const handleCadastrarTema = handleSubmit((data) => {
    handleCadastrar(data);
  });

  const handleCadastrarInserirNovoTema = handleSubmit((data) => {
    handleCadastrar(data, true);
  });

  return (
    <LayoutFormPage
      onSubmit={() => handleCadastrarTema()}
      onResetSubmit={() => handleCadastrarInserirNovoTema()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormTema />
      </FormProvider>
    </LayoutFormPage>
  );
};

export default CadastrarTema;
