import { EnumStatus } from 'constants/Enum/enumStatus';

export type FormData = {
  dominioRazaoSocialFantasiaCnpj: string;
  status: number;
  dominio?: string;
};

export const formDefaultValues = {
  dominioRazaoSocialFantasiaCnpj: '',
  status: EnumStatus.ATIVO.value,
};

export type Lojas = {
  assinaturaId: string;
  razaoSocial: string;
  fantasia: string;
  cnpj: string;
  diaVencimento: number;
  dataCancelamento: Date | null;
  dataExpiracao: Date;
  dataUltimaValidacao: Date;
  validacaoProvisoria: boolean;
  plano: string;
};

export type AssinaturaProps = {
  id: string;
  dominio: string;
  hostURL: string;
  status: number;
  lojas: Lojas[];
};
