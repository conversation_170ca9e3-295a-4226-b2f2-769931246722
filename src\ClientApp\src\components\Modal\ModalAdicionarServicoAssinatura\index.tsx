import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';
import {
  Flex,
  GridItem,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalProps,
  Text,
  useDisclosure,
  useMediaQuery,
} from '@chakra-ui/react';

import { enumTipoServico } from 'constants/Enum/enumTipoServicos';
import { useEffectDefault } from 'hook/useEffectDefault';
import api, { ResponseApi } from 'services/api';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import { ButtonDefault } from 'components/Button';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { SelectDefault, SelectOptions } from 'components/Select/SelectDefault';
import { InputNumber } from 'components/Input/InputChakra/InputNumber';
import { ComboFloating } from 'components/ComboFloating';
import { ServicosAdicionais } from 'pages/Assinaturas/Formulario/validationForms';
import { LoadingDefault } from 'components/Loading';

import { ModalDefaultChakra } from '../ModalDefaultChakra';
import { yupResolver, formDefaultValues, Servicos } from './validationForms';

type ServicoAdicionalProps = ServicosAdicionais & {
  serviceIndex?: number;
};

type FormData = {
  quantidade: number;
  servico: Servicos | null | string;
  valorUnitarioRepasse: number;
  valorTotalRepasse: number;
};

type ModalAdicionarServicoAssinaturaProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> &
  InstanceProps<FormData> & {
    handleAdicionarNovoServico: (itensServico: ServicoAdicionalProps) => void;
    tabelaPrecoId: string;
    isEditable?: ServicosAdicionais;
    listServicos: ServicosAdicionais[];
    serviceIndex?: number;
  };

export const ModalAdicionarServicoAssinatura = create<
  ModalAdicionarServicoAssinaturaProps,
  FormData
>(
  ({
    onResolve,
    onReject,
    tabelaPrecoId,
    isEditable,
    listServicos,
    serviceIndex,
    handleAdicionarNovoServico,
    ...rest
  }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [updateValues, setUpdateValues] = useState(true);
    const [listarServicos, setListarServicos] = useState<Servicos[]>([]);

    const formMethods = useForm<FormData>({
      resolver: yupResolver,
      defaultValues: formDefaultValues,
    });

    const { handleSubmit, reset, watch, setValue } = formMethods;

    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });
    const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

    const [servico, quantidade, valorUnitarioRepasse] = watch(['servico', 'quantidade', 'valorUnitarioRepasse']);

    const servicoWatch = servico as Servicos;

    // Calculate total value when quantity or unit value changes
    useEffect(() => {
      if (quantidade && valorUnitarioRepasse) {
        const total = Number(quantidade) * Number(valorUnitarioRepasse);
        setValue('valorTotalRepasse', total);
      }
    }, [quantidade, valorUnitarioRepasse, setValue]);

    const typeOfServiceIsPlan = isEditable?.tipo === enumTipoServico.PLANO;

    const getServicos = useCallback(async () => {
      setIsLoading(true);
      const response = await api.get<void, ResponseApi<Servicos[]>>(
        ConstantEnderecoWebservice.OBTER_SERVICO_TABELA_PRECO,
        {
          params: {
            tabelaPrecoId,
          },
        }
      );

      if (response.sucesso) {
        const { dados } = response;

        if (listServicos.length > 0 && !isEditable) {
          let newListServicos: Servicos[] = [];

          const existePlanoJaCadastrado = listServicos.some(
            (itemServico) => itemServico.tipo === enumTipoServico.PLANO
          );

          newListServicos = dados.filter((servicoItem) => {
            if (existePlanoJaCadastrado) {
              return servicoItem.tipo !== enumTipoServico.PLANO;
            } else {
              return servicoItem.tipo === enumTipoServico.PLANO;
            }
          });

          newListServicos.forEach((itemServico) => {
            const servicoJaFoiAdicionado = listServicos.some(
              (servico) => servico.servicoId === itemServico.servicoId
            );

            if (!servicoJaFoiAdicionado) {
              setListarServicos((prev) => [...prev, itemServico]);
            }
          });
        } else if (isEditable && !typeOfServiceIsPlan) {
          setListarServicos(dados);
        } else {
          setListarServicos(
            dados.filter(
              (servicoItem) => servicoItem.tipo === enumTipoServico.PLANO
            )
          );
        }
      }

      setIsLoading(false);
    }, [listServicos]);

    const handleAdicionar = handleSubmit((data) => {
      setIsLoading(true);
      const { quantidade, servico, valorUnitarioRepasse, valorTotalRepasse } = data;

      if (quantidade) {
        const valueServico = servico as Servicos;

        const valuesData = {
          ...data,
          ...valueServico,
          servicoNome: valueServico?.nome,
          servicoId: valueServico?.servicoId,
          serviceIndex,
          valorUnitarioRepasse: valorUnitarioRepasse,
          valorTotalRepasse: valorTotalRepasse || (quantidade * valorUnitarioRepasse),
        };

        handleAdicionarNovoServico(valuesData);
        onClose();
        setIsLoading(false);
      }
      setIsLoading(false);
    });

    useEffect(() => {
      if (isEditable) {
        const {
          quantidade: valueQuantidade,
          servicoId,
          servicoNome,
        } = isEditable;

        const valueServico = listarServicos.find(
          (servico) => servico.servicoId === servicoId
        );

        reset({
          quantidade: typeof valueQuantidade === 'string' ? 1 : valueQuantidade,
          servico: {
            ...valueServico,
            valorUnitarioRepasse: isEditable.valorUnitarioRepasse,
            valorTotalRepasse: isEditable.valorTotalRepasse,
            label: servicoNome,
            value: servicoId,
          },
        });
        setUpdateValues(false);
      }
    }, [isEditable, listarServicos]);

    useEffectDefault(() => {
      getServicos();
    }, [getServicos]);

    useEffect(() => {
      setValue('valorUnitarioRepasse', servicoWatch?.valorRepasse);
      setValue('valorTotalRepasse', servicoWatch?.valorTotalRepasse);
    }, [servicoWatch, setValue, updateValues]);

    return (
      <ModalDefaultChakra
        {...rest}
        isCentered
        size={isLargerThan900 ? '3xl' : 'full'}
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <FormProvider {...formMethods}>
          <ModalContent
            h={['full', '400px']}
            w={['full', '850px', '850px']}
            bg="gray.50"
          >
            {isLoading && <LoadingDefault />}
            <ModalHeader borderTopRadius="5px" bg="primary.600" pb="15px">
              <Flex
                color="white"
                pt="5px"
                justifyContent="space-between"
                alignItems="center"
              >
                <Text pl="2px">Adicionando serviços</Text>
                <ModalCloseButton id="closeButton" mt="13px" mr="10px" />
              </Flex>
            </ModalHeader>
            <ModalBody pt="20px">
              <SimpleGridForm>
                <GridItem colSpan={12}>
                  <SelectDefault
                    label="Serviços"
                    name="servico"
                    isRequired
                    onSelect={() => {
                      setUpdateValues(true);
                    }}
                    isDisabled={!!isEditable && !typeOfServiceIsPlan}
                    options={listarServicos.map((servicoItem) => ({
                      ...servicoItem,
                      label: servicoItem.nome,
                      value: servicoItem.servicoId,
                    }))}
                  />
                </GridItem>

                <GridItem colSpan={[12, 12, 4, 4]}>
                  <InputNumber
                    name="quantidade"
                    label="Quantidade"
                    id="quantidade"
                    isRequired
                    isDisabled={!servicoWatch?.quantitativo}
                    scale={0}
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 4, 4]}>
                  <ComboFloating
                    w="full"
                    name="valorUnitarioRepasse"
                    bgLabelFloating="gray.50"
                    isNumeric
                    labelFloating="Valor unitário de repasse"
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 4, 4]}>
                  <ComboFloating
                    w="full"
                    name="valorTotalRepasse"
                    bgLabelFloating="gray.50"
                    isNumeric
                    labelFloating="Valor total de repasse"
                  />
                </GridItem>
              </SimpleGridForm>
            </ModalBody>
            <ModalFooter>
              <Flex
                w="full"
                justifyContent={['center', 'center', 'right']}
                flexDirection={['column', 'row', 'row']}
              >
                <ButtonDefault
                  width={['full', '120px', '120px', '120px']}
                  colorScheme="gray"
                  mr="20px"
                  mb={['20px', 'undefined', 'undefined']}
                  variant="outlinePill"
                  onClick={() => onClose()}
                  possuiFuncionalidade={true}
                >
                  Cancelar
                </ButtonDefault>
                <ButtonDefault
                  width={['full', '160px', '160px', '160px']}
                  colorScheme="blue"
                  color="white"
                  id="buttonSalvar"
                  onClick={() => handleAdicionar()}
                  possuiFuncionalidade={true}
                >
                  Adicionar
                </ButtonDefault>
              </Flex>
            </ModalFooter>
          </ModalContent>
        </FormProvider>
      </ModalDefaultChakra>
    );
  }
);
