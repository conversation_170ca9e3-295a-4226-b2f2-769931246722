import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import { LayoutFormPage } from 'components/Layout/FormPage';

import FormTreinamento from '..';
import { formDefaultValues, yupResolver, FormData } from '../validationForm';
import { videoAdapter } from '../VideoAdapter';

const CadastrarTreinamento = () => {
  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, reset } = formMethods;
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(false);

  const submitCadastro = useCallback(async (data: FormData) => {
    const video = videoAdapter(data);

    const response = await api.post<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.CADASTRAR_VIDEO,
      { ...video }
    );

    return response;
  }, []);

  const handleCadastrar = async (data: FormData, resetAfterSuccess = false) => {
    setIsLoading(true);
    const response = await submitCadastro(data);

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso) {
        toast.success('Cadastro realizado com sucesso');

        if (resetAfterSuccess) {
          reset(formDefaultValues);
        } else {
          navigate(ConstanteRotas.VIDEOS);
        }
      }
    }
    setIsLoading(false);
  };

  const handleCadastrarVideo = handleSubmit((data) => {
    handleCadastrar(data);
  });

  const handleCadastrarInserirNovoVideo = handleSubmit((data) => {
    handleCadastrar(data, true);
  });

  return (
    <LayoutFormPage
      onSubmit={() => handleCadastrarVideo()}
      onResetSubmit={() => handleCadastrarInserirNovoVideo()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormTreinamento />
      </FormProvider>
    </LayoutFormPage>
  );
};

export default CadastrarTreinamento;
