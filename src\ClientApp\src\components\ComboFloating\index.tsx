import { Box, BoxProps, Flex, Text } from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import { useEffect } from 'react';

import { moneyMask } from 'helpers/format/fieldsMasks';

import { InputDefault } from 'components/Input/InputChakra/InputDefault';

export interface ComboFloatingProps extends BoxProps {
  fontSizeLabelFloating?: string;
  bgLabelFloating?: string;
  labelFloating: string;
  name: string;
  isNumeric?: boolean;
  value?: number; // Adicionar propriedade para valor direto
  isReadOnly?: boolean; // Adicionar propriedade para somente leitura
}

export const ComboFloating = ({
  w = '150px',
  h = '60px',
  isNumeric = false,
  borderColor = 'gray.200',
  fontSizeLabelFloating = 'xs',
  bgLabelFloating = 'white',
  labelFloating,
  fontSize = '2xl',
  name,
  value, // Usar o valor direto se fornecido
  isReadOnly = false, // Usar a propriedade somente leitura
}: ComboFloatingProps) => {
  const { watch, setValue } = useFormContext();

  // Usar o valor fornecido diretamente ou o valor do formulário
  const valueCamboFloating = value !== undefined ? value : watch(`${name}`);

  // Atualizar o valor no formulário se for fornecido diretamente e não for somente leitura
  useEffect(() => {
    if (value !== undefined && !isReadOnly) {
      setValue(name, value);
    }
  }, [value, name, setValue, isReadOnly]);

  return (
    <Box position="relative" w={w} h={h}>
      <Box
        w="full"
        h="full"
        borderWidth="3px"
        borderStyle="solid"
        borderColor={borderColor}
        opacity={isReadOnly ? 0.7 : 1} // Adicionar opacidade se for somente leitura
      >
        <Flex w="full" justifyContent="center">
          <Text
            pl="5px"
            pr="5px"
            fontSize={fontSizeLabelFloating}
            top="-9px"
            position="absolute"
            bg={bgLabelFloating}
          >
            {labelFloating}
          </Text>
        </Flex>
        <Flex h="full" justifyContent="center" alignItems="center">
          <Text fontSize={fontSize}>
            {isNumeric ? (
              <Flex alignItems="baseline">
                <Text fontSize="sm" mr="5px">
                  R$
                </Text>
                {moneyMask(valueCamboFloating || 0, false)}
              </Flex>
            ) : (
              valueCamboFloating || 0
            )}
          </Text>
        </Flex>
      </Box>
      {!isReadOnly && <InputDefault name={name} display="none" />}
    </Box>
  );
};
