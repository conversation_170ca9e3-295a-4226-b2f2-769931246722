import { ChakraProvider } from '@chakra-ui/react';
import FullScreenProvider from 'store/FullScreen';

import { theme } from 'theme';

import { InputDateRange } from './exampleInput';

describe('Testing date input', () => {
  beforeEach(() => {
    cy.mount(
      <ChakraProvider theme={theme}>
        <FullScreenProvider>
          <InputDateRange />
        </FullScreenProvider>
      </ChakraProvider>
    );
  });

  it('Date modal opens on button click', () => {
    cy.get('#buttonData').click();
    cy.contains('Selecione o período').should('be.visible');
  });

  it('modal calls a function when it commits', () => {
    cy.get('#buttonData').click();
    cy.get('#buttonConfirmDate').click();
    cy.get('#dateConfirm').should('be.visible');
  });

  it('Date input being cleared', () => {
    cy.get('#buttonData').click();
    cy.get('.minDateInput').click();

    cy.get('.calendar-minDateInput').should('be.visible').click();
    cy.get('.minDateInput').click();

    cy.get('.maxDateInput').click();
    cy.get('.calendar-maxDateInput').should('be.visible').click();
    cy.get('.maxDateInput').click();

    cy.get('#buttonClearDate').click();
  });

  it('Entering value in the start and end date field and completing the action', () => {
    cy.get('#buttonData').click();
    const inputMinDate = cy.get('input[name=minDateInput]');
    const inputMaxDate = cy.get('input[name=maxDateInput]');

    inputMinDate
      .clear()
      .type('20092022')
      .type('{enter}')
      .should((value) => {
        expect(value).to.equal(value);
      });

    inputMaxDate
      .clear()
      .type('20122022')
      .type('{enter}')
      .should((value) => {
        expect(value).to.equal(value);
      });

    cy.get('#buttonConfirmDate').click();
    cy.get('#dateConfirm').should('be.visible');
  });
});
