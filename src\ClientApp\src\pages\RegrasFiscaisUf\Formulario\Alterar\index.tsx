import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import { EnumEstadosSigla } from 'constants/Enum/enumEstados';
import { EnumModelosDocumento } from 'constants/Enum/enumModeloDocRegraFiscal';

import { LayoutFormPage } from 'components/Layout/FormPage';
import { SelectOptions } from 'components/Select/SelectDefault';

import { FormRegraFiscalUf } from '..';
import { formDefaultValues, yupResolver, FormData } from '../validationForms';

type RegraFiscalResponse = {
  codigoRegra: string;
  codigoRejeicao: string;
  notaTecnica: string;
  estados: string[] | null;
  modelosDocumento: string;
  dataInicio: string | null;
  dataFim: string | null;
  descricao: string;
};

export const AlterarRegraFiscalUf = () => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });

  const { handleSubmit, reset } = formMethods;

  const navigate = useNavigate();
  const idRouter = useParams();
  const { id: idRota } = idRouter;

  const getRegraFiscal = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<RegraFiscalResponse>>(
      ConstantEnderecoWebservice.OBTER_REGRA_FISCAL_UF,
      {
        params: { id: idRota },
      }
    );

    if (response.sucesso) {
      const { dados } = response;

      let modeloDocSelecionado: SelectOptions[] = [];

      const valoresModeloDocValidados = dados.modelosDocumento.split(',');

      valoresModeloDocValidados.forEach((modeloDoc) => {
        if (EnumModelosDocumento.some((item) => item.value === modeloDoc)) {
          const listaModeloDoc = EnumModelosDocumento.find(
            (listDoc) => listDoc.value === modeloDoc
          );
          if (listaModeloDoc) {
            modeloDocSelecionado.push(listaModeloDoc);
          }
        }
      });

      const valuesReset = {
        ...response.dados,
        estados: EnumEstadosSigla.filter((estado) =>
          dados.estados?.includes(estado.value)
        ),
        modelosDocumento: modeloDocSelecionado,
        dataInicio: response.dados.dataInicio?.split('T')[0],
        dataFim: response.dados.dataFim?.split('T')[0],
      };

      reset(valuesReset);
    } else {
      navigate(ConstanteRotas.REGRAS_FISCAL_UF);
    }
    setIsLoading(false);
  }, [reset, idRota]);

  const handleAlterarRegraFiscalUf = handleSubmit(async (data) => {
    setIsLoading(true);

    let modeloDocSelecionada = '';
    const modeloDoc = data.modelosDocumento?.map((item) => item.value);

    modeloDoc?.forEach((value) => {
      modeloDocSelecionada = modeloDocSelecionada + `${value},`;
    });

    const response = await api.put<void, ResponseApi>(
      ConstantEnderecoWebservice.ALTERAR_REGRA_FISCAL_UF,
      {
        ...data,
        modelosDocumento: modeloDocSelecionada,
        estados: data?.estados?.map((estado) => estado.value).join(','),
        id: idRouter.id,
      }
    );

    if (response.sucesso) {
      toast.success('Cadastro alterado com sucesso.');
      navigate(ConstanteRotas.REGRAS_FISCAL_UF);
    }

    setIsLoading(false);
  });

  useEffect(() => {
    getRegraFiscal();
  }, []);

  return (
    <LayoutFormPage
      onSubmit={() => handleAlterarRegraFiscalUf()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormRegraFiscalUf />
      </FormProvider>
    </LayoutFormPage>
  );
};
