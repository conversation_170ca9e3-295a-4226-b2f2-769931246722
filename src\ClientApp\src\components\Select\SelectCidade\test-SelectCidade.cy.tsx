import { <PERSON><PERSON><PERSON>rovider } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';

import { theme } from 'theme';
import { hexToRgbA } from 'store/getHexDecimalColor';
import { optionsCidades } from 'services/dataSelectCidade';

import { Select as SelectCidade } from './exampleSelect';

const valueLabel = 'Cidades';
const valueColorLabel = '#6502b2';

function Select({ ...rest }) {
  const formMethods = useForm();
  return (
    <ChakraProvider theme={theme}>
      <FormProvider {...formMethods}>
        <SelectCidade
          label={valueLabel}
          colorLabel={valueColorLabel}
          id="testSelectCidade"
          name="testSelectCidade"
          options={optionsCidades}
          isSearchable
          {...rest}
        />
      </FormProvider>
    </ChakraProvider>
  );
}

describe('Testing default select', () => {
  beforeEach(() => {
    cy.mount(<Select />);
  });

  it('Testing value select', () => {
    cy.get('#testSelectCidade')
      .type('3{enter}{enter}')
      .should((value) => {
        expect(value).to.equal(value);
      });
  });

  it('Testing non-existent value', () => {
    cy.get('#testSelectCidade')
      .type('5123{enter}{enter}')
      .invoke('val')
      .should((value) => {
        expect(value).to.equal(value);
      });
  });

  it('Select label is showing', () => {
    cy.testLabelInput(valueLabel);
  });

  it('Validating the color of the label', () => {
    cy.testColorLabel(valueLabel, hexToRgbA(valueColorLabel));
  });

  optionsCidades.forEach((item) => {
    it('Test by selecting all select options', () => {
      cy.get('#testSelectCidade').click();
      cy.get(`.react-select__option${item.label}`)
        .scrollIntoView()
        .click()
        .should(() => {
          expect(item.label).to.equal(item.label);
        });
    });
  });
});
