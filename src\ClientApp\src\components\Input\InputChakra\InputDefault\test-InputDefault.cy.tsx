import { hexToRgbA } from 'store/getHexDecimalColor';

import { InputDefault as Input } from './exampleInput';

const valueLabel = 'Cpf';
const valueColorLabel = '#6502b2';

describe('Testing default input', () => {
  beforeEach(() => {
    cy.mount(
      <Input
        label={valueLabel}
        colorLabel={valueColorLabel}
        onEnterKeyPress={() => alert('Enter functionality working')}
      />
    );
  });

  it('Testing value input', () => {
    cy.get('input[name=testInput]')
      .type('Testing input')
      .should((value) => {
        expect(value).to.equal(value);
      });
  });

  it('Input label is showing', () => {
    cy.testLabelInput(valueLabel);
  });

  it('Validating the color of the label', () => {
    cy.testColorLabel(valueLabel, hexToRgbA(valueColorLabel));
  });

  it('testing onEnterKeyPress functionality', () => {
    cy.get('input[name=testInput]').type('Testing input{enter}{enter}');
  });
});
