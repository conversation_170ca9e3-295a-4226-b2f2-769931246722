import React from 'react';
import {
  Flex,
  Box,
  InputProps as ChakraInputProps,
  useMultiStyleConfig,
  Text,
  useToken,
} from '@chakra-ui/react';
import { Controller } from 'react-hook-form';

import {
  CampoPrototipo,
  CampoPrototipoProps,
} from 'components/Layout/CampoPrototipo';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';

export type CheckboxAtivoInativoProps = Omit<
  ChakraInputProps,
  'ref' | 'variant' | 'colorScheme'
> &
  CampoPrototipoProps & {
    name: string;
  };

export const CheckboxAtivoInativo = ({
  name,
  label,
  isRequired,
  isDisabled,
  size,
  id,
  iconLeftElement,
  fontLabel,
  colorLabel,
}: CheckboxAtivoInativoProps) => {
  const styles = useMultiStyleConfig('Input', { size });
  const [size1, size7] = useToken('sizes', ['1', '7']);

  return (
    <Controller
      name={name}
      render={({ field: { onChange, value, name }, fieldState: { error } }) => {
        return (
          <CampoPrototipo
            iconLeftElement={iconLeftElement}
            error={error}
            fontLabel={fontLabel}
            isRequired={isRequired}
            colorLabel={colorLabel}
            label={label}
          >
            <Flex
              onClick={isDisabled ? undefined : () => onChange(!value)}
              __css={styles.field}
              cursor={isDisabled ? 'not-allowed' : 'pointer'}
              border="none"
              bg={value ? 'secondary.500' : 'red.400'}
              w="120px"
              id={name}
              p="1"
              display="flex"
              position="relative"
              opacity={isDisabled ? 0.5 : 1}
            >
              <Box
                w="7"
                h="7"
                position="absolute"
                transition="all .2s"
                top="1"
                transform={
                  value
                    ? `translateX(calc(120px - ${size7} - (${size1} * 2)))`
                    : `none`
                }
                borderRadius="md"
                bg="white"
              />

              <Flex
                alignItems="center"
                justifyContent={value ? 'right' : 'left'}
                pl={value ? '0' : `calc(${size7} + (${size1} * 5.5))`}
                w={value ? `calc(120px - ${size7} - (${size1} * 2))` : 'full'}
                pr="1"
                transition="padding-left .2s"
              >
                <Text
                  color="white"
                  userSelect="none"
                  fontSize="xs"
                  id="textCheckboxAtivoInativo"
                  textTransform="uppercase"
                >
                  {value ? 'ATIVO' : 'INATIVO'}
                </Text>
              </Flex>
              <InputDefault
                type="checkbox"
                checked={value}
                name={name}
                display="none"
              />
            </Flex>
          </CampoPrototipo>
        );
      }}
    />
  );
};
