import {
  Flex,
  FormLabel,
  InputGroup,
  InputLeftAddon,
  InputProps,
} from '@chakra-ui/react';
import { Controller } from 'react-hook-form';
import { IconType } from 'react-icons';

import { InputChakra } from '..';

export interface InputUrlProps extends InputProps {
  name: string;
  label?: string;
  colorLabel?: string;
  fontLabel?: string;
  iconLeftElement?: IconType;
  onEnterKeyPress?: () => void;
  blockSpecialCharacters?: boolean;
  placeholderUrl: string;
}

export const InputUrl = ({
  name,
  label,
  isDisabled,
  blockSpecialCharacters = false,
  colorLabel = 'black',
  isRequired,
  onEnterKeyPress,
  onKeyPress,
  fontLabel = 'xs',
  placeholder,
  fontSize = 'sm',
  iconLeftElement,
  variant,
  placeholderUrl,
  ...rest
}: InputUrlProps) => {
  return (
    <Controller
      name={name}
      render={({
        field: { onChange, onBlur, value, name },
        fieldState: { error },
      }) => {
        return (
          <InputGroup display="flex" flexDirection="column">
            {label && (
              <FormLabel fontSize={fontLabel} color={colorLabel} htmlFor={name}>
                {label}
              </FormLabel>
            )}
            <Flex>
              <InputLeftAddon
                children={placeholderUrl}
                h="35px"
                fontSize={fontSize}
              />
              <InputChakra
                onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === 'Enter' && onEnterKeyPress) {
                    e.currentTarget.value = e.currentTarget.value.trim();

                    onEnterKeyPress();
                  }

                  if (onKeyPress) onKeyPress(e);
                }}
                error={error}
                iconLeftElement={iconLeftElement}
                onChange={onChange}
                name={name}
                isRequired={isRequired}
                value={value}
                placeholder={placeholder}
                isDisabled={isDisabled}
                onBlur={onBlur}
                variant={variant}
                fontSize={fontSize}
                borderRadius="0 6px 6px 0"
                {...rest}
              />
            </Flex>
          </InputGroup>
        );
      }}
    />
  );
};
