import {
  Box,
  Flex,
  GridItem,
  Icon,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Text,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useFormContext } from 'react-hook-form';

import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';
import { useEffectDefault } from 'hook/useEffectDefault';

import { LixeiraIcon } from 'icons';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { AccordionDefault } from 'components/Accordion';
import { ButtonDefault } from 'components/Button';
import { LoadingDefault } from 'components/Loading';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SelectDefault, SelectOptions } from 'components/Select/SelectDefault';
import { InputNumber } from 'components/Input/InputChakra/InputNumber';
import { CheckboxAtivoInativo } from 'components/Checkbox/CheckboxAtivoInativo';

import { FormGradesProps } from './validationForm';

type ProdutosListarSelectProps = {
  id: string;
  nome: string;
};

type ServicosListarSelectProps = {
  id: string;
  nome: string;
};

export const FormGradeServicos = ({
  gradeItensCadastrados,
  listaServicos,
  setListaServicos,
  isAlterar,
}: FormGradesProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [optionsProdutos, setOptionsProdutos] = useState<SelectOptions[]>([]);

  const formMethods = useFormContext();
  const { watch, setValue, reset, getValues } = formMethods;
  const [valorRepasseWatch, servicoSelecionadoWatch, servicosGradeWatch] =
    watch(['valorRepasse', 'servicoSelecionado', 'servicosGrade']);

  const possuiPermissaoExcluirItemGrade = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.CadastroGradeServicoAcao.EXCLUIR_GRADE_SERVICOS
  );

  const listaServicosAdicionados = listaServicos?.filter(
    (servico) => servico.servicoEstaAdicionado === true
  );

  const listaServicosNaoAdicionados = useCallback(() => {
    const newListaServicos = listaServicos?.filter(
      (servico) => servico.servicoEstaAdicionado === false
    );

    return newListaServicos || [];
  }, [listaServicos])();

  const handleProdutoChange = (produto: SelectOptions) => {
    setValue('nome', produto.label);
  };

  const handleExcluirItemGrade = (idServico: string) => {
    setListaServicos((servicos) => {
      const newListaServicos = servicos.map((servico) => {
        return {
          ...servico,
          servicoEstaAdicionado:
            servico.value === idServico ? false : servico.servicoEstaAdicionado,
        };
      });
      return newListaServicos;
    });
    if (servicosGradeWatch) {
      delete servicosGradeWatch[idServico];
    }
  };

  const handleAdicionarItemGrade = useCallback(() => {
    if (servicoSelecionadoWatch) {
      setListaServicos((servicos) => {
        const servicosEscolhidos = servicos.map((servico) => {
          return {
            ...servico,
            servicoEstaAdicionado:
              servico.value === servicoSelecionadoWatch?.value
                ? true
                : servico.servicoEstaAdicionado,
          };
        });
        return servicosEscolhidos;
      });
      setValue(
        `servicosGrade.${servicoSelecionadoWatch.value}`,
        valorRepasseWatch
      );
      reset((formValues) => ({
        ...formValues,
        valorRepasse: 0,
        servicoSelecionado: null,
      }));
    }
  }, [servicoSelecionadoWatch, valorRepasseWatch]);

  const getServicos = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<
      void,
      ResponseApi<ServicosListarSelectProps[]>
    >(ConstantEnderecoWebservice.SERVICOS_LISTAR_SELECT);

    if (response.sucesso) {
      const { dados } = response;

      const listaServicos = dados.map((servico) => {
        return {
          id: servico.id,
          label: servico.nome,
          value: servico.id,
          servicoId: servico.id,
          servicoEstaAdicionado: false,
          valorRepasse: 0,
        };
      });

      setListaServicos(listaServicos);
    } else {
      setListaServicos([]);
    }

    setIsLoading(false);
  }, []);

  const getProdutos = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<
      void,
      ResponseApi<ProdutosListarSelectProps[]>
    >(ConstantEnderecoWebservice.PRODUTOS_LISTAR_SELECT);

    if (response.sucesso) {
      const { dados } = response;

      const optionsProdutos = dados.map((produto) => {
        return {
          label: produto.nome,
          value: produto.id,
        };
      });

      setOptionsProdutos(optionsProdutos);
    } else {
      setOptionsProdutos([]);
    }

    setIsLoading(false);
  }, []);

  useEffectDefault(() => {
    getProdutos();
  }, [getProdutos]);

  useEffectDefault(() => {
    getServicos();
  }, [getServicos]);

  useEffect(() => {
    if (
      gradeItensCadastrados &&
      gradeItensCadastrados?.length > 0 &&
      isAlterar
    ) {
      setListaServicos((servicos) => {
        const newListaServicos = servicos.map((servico) => ({
          ...servico,
          servicoEstaAdicionado: gradeItensCadastrados.some(
            ({ value }) => value === servico.id
          ),
          valorRepasse: gradeItensCadastrados.find(
            ({ value }) => value === servico.id
          )?.valorRepasse,
        }));
        return newListaServicos;
      });
    }
  }, [gradeItensCadastrados]);

  return (
    <>
      {isLoading && <LoadingDefault />}
      <SimpleGridForm>
        <GridItem colSpan={[12, 4, 5]}>
          <SelectDefault
            name="produtoId"
            label="Produto"
            isRequired
            options={optionsProdutos}
            onSelect={(produto) => handleProdutoChange(produto)}
          />
        </GridItem>
        <GridItem colSpan={[12, 8, 5]}>
          <InputDefault name="nome" label="Nome" placeholder="Informe o nome" />
        </GridItem>
        <GridItem colSpan={[12, 4, 2]} mb="1">
          <CheckboxAtivoInativo
            name="ativo"
            label="Status"
            fontLabel="xs"
            colorLabel="black"
          />
        </GridItem>
      </SimpleGridForm>

      <AccordionDefault
        mt="10"
        defaultIndex={[0, 1, 2, 3, 4]}
        contentAccordion={[
          {
            title: 'Lista de serviços',
            children: (
              <SimpleGridForm>
                <GridItem colSpan={[12, 12, 8, 5, 3]}>
                  <SelectDefault
                    name="servicoSelecionado"
                    label="Serviço"
                    options={listaServicosNaoAdicionados}
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 4, 3, 2]}>
                  <InputNumber
                    name="valorRepasse"
                    label="Valor de repasse"
                    defaultValue={0}
                    leftElement="R$"
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 12, 4, 2]}>
                  <ButtonDefault
                    width={['full', 'full', 'full', '220px']}
                    color="white"
                    colorScheme="green"
                    mt={['0px', '6']}
                    leftIcon={<IoIosAddCircleOutline />}
                    possuiFuncionalidade={true}
                    isDisabled={!servicoSelecionadoWatch}
                    onClick={handleAdicionarItemGrade}
                  >
                    Adicionar
                  </ButtonDefault>
                </GridItem>
                <GridItem colSpan={[12, 12, 12]}>
                  {listaServicosAdicionados.length > 0 ? (
                    <Box w="full" overflow="auto">
                      <Table
                        sx={{
                          '& tr > th': { borderBottom: 'none' },
                          '& td:only-child': {
                            bg: 'white',
                            h: '60px',
                            border: 'none',
                          },
                          '& tr': { boxShadow: 'none' },
                          '& th': {
                            fontSize: '2xs',
                            color: 'gray.300',
                            paddingBottom: '5px',
                          },
                        }}
                        variant="simple-card"
                        mt="20px"
                      >
                        <Thead>
                          <Tr>
                            <Th fontSize="xs" w="30%">
                              Nome
                            </Th>
                            <Th minW="180px" w="10%" textAlign="end">
                              Valor de repasse
                            </Th>
                            {possuiPermissaoExcluirItemGrade && (
                              <Th w="60%" isNumeric>
                                Ações
                              </Th>
                            )}
                          </Tr>
                        </Thead>
                        <Tbody>
                          {listaServicosAdicionados.map((servico, index) => {
                            return (
                              <>
                                <Box h={index === 0 ? '0' : '2'} />
                                <Tr key={servico.servicoId}>
                                  <Td>{servico.label}</Td>
                                  <Td isNumeric>
                                    <Flex>
                                      <InputNumber
                                        name={`servicosGrade.${servico.id}`}
                                        leftElement="R$"
                                        defaultValue={getValues(
                                          `servicosGrade.${servico.id}`
                                        )}
                                      />
                                    </Flex>
                                  </Td>
                                  {possuiPermissaoExcluirItemGrade && (
                                    <Td isNumeric>
                                      <Icon
                                        mr="5px"
                                        cursor="pointer"
                                        as={LixeiraIcon}
                                        onClick={() =>
                                          handleExcluirItemGrade(servico.id)
                                        }
                                      />
                                    </Td>
                                  )}
                                </Tr>
                              </>
                            );
                          })}
                        </Tbody>
                      </Table>
                    </Box>
                  ) : (
                    <Text mt="20px" fontSize="xs">
                      Nenhum serviço foi adicionado
                    </Text>
                  )}
                </GridItem>
              </SimpleGridForm>
            ),
          },
        ]}
      />
    </>
  );
};
