import { getThemingArgTypes } from '@chakra-ui/storybook-addon';
import { Meta, StoryFn } from '@storybook/react';

import { optionsCidades } from 'services/dataSelectCidade';
import { theme } from 'theme';

import { SelectCidadeProps } from '.';
import { Select } from './exampleSelect';

export default {
  title: 'Components/Select/SelectCidade',
  argTypes: getThemingArgTypes(theme as any, 'Select'),
  args: {
    name: 'cidade',
    id: 'cidade',
    label: 'Cidade',
    isRequired: true,
    size: 'md',
    isDisabled: false,
    isSearchable: true,
  },
} as Meta<SelectCidadeProps>;

export const SelectCidade: StoryFn<SelectCidadeProps> = (props) => {
  return (
    <Select
      {...props}
      colorLabel={props.colorLabel}
      label={props.label}
      isRequired={props.isRequired}
      isDisabled={props.isDisabled}
      id={props.id}
      size={props.size}
      placeholder={props.placeholder}
      name={props.name}
      options={optionsCidades}
      isSearchable={props.isSearchable}
    />
  );
};
