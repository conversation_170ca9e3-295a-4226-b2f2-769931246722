#!/usr/bin/env node

/**
 * Script principal para atualizar todos os mapeamentos de permissões
 * 
 * Este script:
 * 1. Analisa automaticamente todos os arquivos TypeScript da pasta Funcionalidades
 * 2. Gera arquivo Excel atualizado
 * 3. Gera arquivo CSV atualizado  
 * 4. Gera página HTML interativa atualizada
 * 
 * Uso: node atualizar_permissoes.cjs
 */

const fs = require('fs');
const path = require('path');

// Importar módulos
const { analisarTodosArquivos, gerarEstatisticas, imprimirEstatisticas } = require('./analisar_funcionalidades.cjs');

async function executarScript(nomeScript, descricao) {
    console.log(`\n🔄 ${descricao}...`);
    console.log('-'.repeat(50));
    
    try {
        // Importar e executar o script
        const modulo = require(`./${nomeScript}`);
        
        // Verificar se o arquivo foi gerado
        let nomeArquivo;
        if (nomeScript === 'gerar_excel.cjs') {
            nomeArquivo = 'Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx';
        } else if (nomeScript === 'gerar_csv_com_constantes.cjs') {
            nomeArquivo = 'Mapeamento_Acoes_Funcionalidades_com_Constantes.csv';
        } else if (nomeScript === 'gerar_html.cjs') {
            nomeArquivo = 'Mapeamento_Acoes_Funcionalidades_com_Constantes.html';
        }
        
        if (nomeArquivo && fs.existsSync(nomeArquivo)) {
            const stats = fs.statSync(nomeArquivo);
            const tamanho = (stats.size / 1024).toFixed(2);
            console.log(`✅ Arquivo gerado: ${nomeArquivo} (${tamanho} KB)`);
            return true;
        } else {
            console.log(`❌ Arquivo não foi gerado: ${nomeArquivo}`);
            return false;
        }
    } catch (error) {
        console.error(`❌ Erro ao executar ${nomeScript}:`, error.message);
        return false;
    }
}

async function main() {
    console.log('🚀 AGENTE DE ATUALIZAÇÃO DE PERMISSÕES');
    console.log('='.repeat(60));
    console.log('📅 Data/Hora:', new Date().toLocaleString('pt-BR'));
    console.log('📁 Diretório:', process.cwd());
    console.log('='.repeat(60));
    
    // 1. Análise inicial dos arquivos
    console.log('\n📊 FASE 1: Análise dos arquivos de Funcionalidades');
    console.log('-'.repeat(50));
    
    const acoesData = analisarTodosArquivos();
    
    if (Object.keys(acoesData).length === 0) {
        console.error('❌ ERRO: Nenhuma ação foi encontrada nos arquivos!');
        console.error('   Verifique se a pasta src/constants/Funcionalidades existe');
        console.error('   e contém arquivos TypeScript válidos.');
        process.exit(1);
    }
    
    // Gerar e exibir estatísticas
    const stats = gerarEstatisticas(acoesData);
    imprimirEstatisticas(stats);
    
    // 2. Geração dos arquivos
    console.log('\n📁 FASE 2: Geração dos arquivos de saída');
    console.log('-'.repeat(50));
    
    const resultados = {
        excel: false,
        csv: false,
        html: false
    };
    
    // Gerar Excel
    resultados.excel = await executarScript('gerar_excel.cjs', 'Gerando planilha Excel');
    
    // Gerar CSV
    resultados.csv = await executarScript('gerar_csv_com_constantes.cjs', 'Gerando arquivo CSV');
    
    // Gerar HTML
    resultados.html = await executarScript('gerar_html.cjs', 'Gerando página HTML');
    
    // 3. Relatório final
    console.log('\n📋 RELATÓRIO FINAL');
    console.log('='.repeat(60));
    
    const totalAcoes = Object.keys(acoesData).length;
    const totalPermissoes = Object.values(stats).reduce((sum, count) => sum + count, 0);
    const arquivosGerados = Object.values(resultados).filter(Boolean).length;
    
    console.log(`📊 Total de ações analisadas: ${totalAcoes}`);
    console.log(`🔢 Total de permissões: ${totalPermissoes}`);
    console.log(`📁 Arquivos gerados com sucesso: ${arquivosGerados}/3`);
    
    console.log('\n📁 Status dos arquivos:');
    console.log(`   📊 Excel: ${resultados.excel ? '✅ Gerado' : '❌ Falhou'}`);
    console.log(`   📄 CSV:   ${resultados.csv ? '✅ Gerado' : '❌ Falhou'}`);
    console.log(`   🌐 HTML:  ${resultados.html ? '✅ Gerado' : '❌ Falhou'}`);
    
    // Verificar se todos os arquivos foram gerados
    const todosGerados = Object.values(resultados).every(Boolean);
    
    if (todosGerados) {
        console.log('\n🎉 SUCESSO! Todos os arquivos foram atualizados com sucesso!');
        console.log('\n📋 Próximos passos:');
        console.log('   1. ✅ Verificar os arquivos gerados');
        console.log('   2. 🌐 Abrir a página HTML para visualizar');
        console.log('   3. 📊 Validar as permissões no Excel');
        console.log('   4. 🔄 Commit das alterações (se necessário)');
        
        // Listar arquivos gerados com tamanhos
        console.log('\n📁 Arquivos atualizados:');
        const arquivos = [
            'Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx',
            'Mapeamento_Acoes_Funcionalidades_com_Constantes.csv',
            'Mapeamento_Acoes_Funcionalidades_com_Constantes.html'
        ];
        
        arquivos.forEach(arquivo => {
            if (fs.existsSync(arquivo)) {
                const stats = fs.statSync(arquivo);
                const tamanho = (stats.size / 1024).toFixed(2);
                const dataModificacao = stats.mtime.toLocaleString('pt-BR');
                console.log(`   📄 ${arquivo}`);
                console.log(`      Tamanho: ${tamanho} KB | Modificado: ${dataModificacao}`);
            }
        });
        
        process.exit(0);
    } else {
        console.log('\n⚠️  ATENÇÃO! Alguns arquivos não foram gerados corretamente.');
        console.log('   Verifique os erros acima e tente novamente.');
        process.exit(1);
    }
}

// Tratamento de erros não capturados
process.on('uncaughtException', (error) => {
    console.error('\n❌ ERRO CRÍTICO:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('\n❌ PROMISE REJEITADA:', reason);
    process.exit(1);
});

// Executar função principal
if (require.main === module) {
    main().catch(error => {
        console.error('\n❌ ERRO NA EXECUÇÃO:', error.message);
        process.exit(1);
    });
}

module.exports = { main };
