import {
  FormLabel,
  FormControl,
  FormErrorMessage,
  Flex,
  useToken,
  Box,
  Icon,
} from '@chakra-ui/react';
import {
  StylesConfig,
  AsyncSelect,
  components,
  ControlProps,
  AsyncProps,
} from 'chakra-react-select';
import { Controller } from 'react-hook-form';
import { useRef } from 'react';
import { AiOutlineCheck } from 'react-icons/ai';

export interface AsyncSelectDefaultProps extends AsyncProps<any, boolean, any> {
  name: string;
  id?: string;
  label?: string;
  colorLabel?: string;
  filtrosAtivos?: boolean;
  isMulti?: boolean;
  isSearchable?: boolean;
  closeMenuOnSelect?: boolean;
  onSelect?: (newValue: any) => void;
}

export const AsyncSelectDefault = ({
  name,
  label,
  isDisabled,
  colorLabel = 'black',
  isRequired,
  onSelect,
  options,
  id,
  isLoading = false,
  isMulti = false,
  isSearchable = false,
  closeMenuOnSelect = true,
  placeholder = 'Selecione',
  selectedOptionStyle = 'check',
  noOptionsMessage = ({ inputValue }) =>
    !inputValue ? 'Não possui mais opções' : 'Resultado não encontrado',
  loadOptions,
  ...rest
}: AsyncSelectDefaultProps) => {
  const currentInputValue = useRef('');

  const styles: StylesConfig = {
    menuPortal: (base: any) => ({
      ...base,
      zIndex: 9999,
    }),
  };

  return (
    <Controller
      name={name}
      render={({ field: { onChange, name, value }, fieldState: { error } }) => {
        return (
          <FormControl isInvalid={!!error} isRequired={isRequired}>
            {label && (
              <FormLabel mb="8px" fontSize="xs" color={colorLabel}>
                {label}
              </FormLabel>
            )}
            <AsyncSelect
              {...rest}
              id={id}
              value={value}
              name={name}
              menuPortalTarget={document.body}
              noOptionsMessage={noOptionsMessage}
              selectedOptionStyle={selectedOptionStyle}
              closeMenuOnSelect={closeMenuOnSelect}
              onChange={(valueOption) => {
                if (valueOption) {
                  onChange(valueOption);

                  if (onSelect) {
                    onSelect(valueOption);
                  }
                }
              }}
              isSearchable={isSearchable}
              isMulti={isMulti}
              selectedOptionColor="gray"
              placeholder={placeholder}
              isLoading={isLoading}
              isDisabled={isDisabled}
              isRequired={isRequired}
              styles={styles}
              onInputChange={(newValue: string) => {
                currentInputValue.current = newValue;
              }}
              loadOptions={loadOptions}
              useBasicStyles
            />

            {!!error && <FormErrorMessage>{error.message}</FormErrorMessage>}
          </FormControl>
        );
      }}
    />
  );
};
