import { Box, ChakraProvider } from '@chakra-ui/react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { valuesMenu } from 'services/dataActionsMenu';
import FullScreenProvider from 'store/FullScreen';
import { theme } from 'theme';

import { ActionsMenu } from './ActionsMenu.stories';

describe('Testing Action menu component', () => {
  beforeEach(() => {
    cy.mount(
      <ChakraProvider theme={theme}>
        <FullScreenProvider>
          <Box mt="20px">
            <ActionsMenu />
          </Box>
        </FullScreenProvider>
        <ToastContainer />
      </ChakraProvider>
    );
  });

  valuesMenu.forEach((values) => {
    it('Clicking on the options actions menu', () => {
      cy.get('.menuButton').click();
      cy.get(`.menuItem${values.content}`)
        .click()
        .should(() => {
          expect(values.content).to.equal(values.content);
        });

      cy.get(`#${values.content}Sucesso`).should('be.visible');
    });
  });
});
