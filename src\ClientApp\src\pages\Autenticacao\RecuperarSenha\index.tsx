import { VStack } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

import { hideEmail } from 'helpers/hideEmail';
import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';
import { submitInputOnEnter } from 'hook/submitInputOnEnter';

import { ButtonDefault } from 'components/Button';
import { InputAutenticacao } from 'components/Input/InputAutenticacao';
import { Paragraph } from 'components/Layout/Paragraph';
import { Title } from 'components/Layout/Title';
import { Header } from 'components/Layout/Header';

import { yupResolver } from './validationForm';

export const RecuperarSenha = () => {
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  const formMethods = useForm({
    resolver: yupResolver,
    mode: 'onChange',
    defaultValues: {
      email: '',
    },
  });

  const {
    formState: { isValid, errors },
    handleSubmit,
  } = formMethods;

  const handleLembreiSenha = () => {
    navigate(ConstanteRotas.LOGIN);
  };

  const handleRecuperarSenha = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await api.post<void, ResponseApi<any>>(
      `${ConstantEnderecoWebservice.AUTENTICACAO_RECUPERAR_SENHA}/${data.email}`
    );

    if (response.sucesso) {
      const valueHideEmail = hideEmail(data.email);

      navigate(
        SubstituirParametroRota(
          ConstanteRotas.SENHA_ENVIADA_COM_SUCESSO,
          'email',
          valueHideEmail
        )
      );
    }

    setIsLoading(false);
  });

  useEffect(() => {
    submitInputOnEnter(handleRecuperarSenha);
  }, []);

  return (
    <VStack spacing={12}>
      <Header />
      <FormProvider {...formMethods}>
        <VStack spacing={2} alignItems="flex-start" w="full">
          <Title>Esqueceu a senha?</Title>
          <Paragraph color="white" fontSize="sm">
            Não tem problema! Informe o endereço de <br /> e-mail que você
            utilizou no cadastro para <br /> receber um link com instruções
            sobre como <br /> redefinir sua senha.
          </Paragraph>
        </VStack>
        <VStack as="form" w="full" spacing={8}>
          <InputAutenticacao
            name="email"
            type="email"
            variant="floating"
            colorLabel="white"
            label="E-mail"
            bg="none"
            autoFocus
            isDisabled={isLoading}
            isInvalid={!!errors?.email?.message}
            maxLength={256}
          />

          <ButtonDefault
            onClick={() => handleRecuperarSenha()}
            color={!isValid ? 'primary' : 'white'}
            colorScheme="secondary"
            w="full"
            id="button"
            isDisabled={isLoading || !isValid}
            isLoading={isLoading}
            possuiFuncionalidade={true}
          >
            Redefinir senha
          </ButtonDefault>
          <ButtonDefault
            onClick={() => handleLembreiSenha()}
            variant="link"
            color="white"
            width="220px"
            fontSize="xs"
            _hover={{
              color: 'aquamarine.400',
            }}
            possuiFuncionalidade={true}
          >
            Lembrei a senha
          </ButtonDefault>
        </VStack>
      </FormProvider>
    </VStack>
  );
};
