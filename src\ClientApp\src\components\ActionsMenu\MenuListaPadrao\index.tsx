import { MenuList, MenuItem, Box, PositionProps } from '@chakra-ui/react';

import { ActionMenuItem } from '..';

interface MenuItemActionsProps {
  items: ActionMenuItem[];
  menuZIndex?: PositionProps['zIndex'];
}

export const MenuItemActions = ({
  items,
  menuZIndex,
}: MenuItemActionsProps) => {
  return (
    <MenuList id="menuList" zIndex={menuZIndex}>
      {items.map(
        (
          { content, possuiFuncionalidade, isDisabled, icon, ...itemRest },
          index
        ) => (
          <Box key={index}>
            {possuiFuncionalidade && (
              <MenuItem
                {...itemRest}
                isDisabled={isDisabled}
                icon={icon}
                className={`menuItem${content}`}
                _hover={{
                  background: 'gray.100',
                }}
                id={`${content}`}
              >
                {content}
              </MenuItem>
            )}
          </Box>
        )
      )}
    </MenuList>
  );
};
