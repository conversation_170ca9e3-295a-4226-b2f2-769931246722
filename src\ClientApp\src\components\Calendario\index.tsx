import { Flex, Text, useToken } from '@chakra-ui/react';
import { Calendar, CalendarProps } from 'react-date-range';
import { Controller, useFormContext } from 'react-hook-form';
import langBR from 'date-fns/locale/pt-BR';

import { formatAno, formatDateCompleto } from 'helpers/format/formatStringDate';
import { capitalize } from 'helpers/format/stringFormats';

import { ButtonDefault } from 'components/Button';

export interface CalendarioProps
  extends Omit<CalendarProps, 'value' | 'onChange'> {
  name: string;
  minDate?: Date;
  maxDate?: Date;
  dataInicial?: Date;
  secondaryStyle?: boolean;
  onCancelButton: () => void;
  onConfirmButton: () => void;
  onCancelButtonText?: string;
  onConfirmButtonText?: string;
}

export const Calendario = ({
  minDate,
  maxDate,
  name,
  secondaryStyle = false,
  dataInicial = new Date(),
  onCancelButton,
  onConfirmButton,
  onCancelButtonText = 'Cancelar',
  onConfirmButtonText = 'Confirmar',
}: CalendarioProps) => {
  const { watch } = useFormContext();

  const [primary500, secondary500] = useToken('colors', [
    'primary.500',
    'secondary.500',
  ]);

  const dataSelecionada = watch(name);

  const dataFormatadaCompleta = dataSelecionada
    ? capitalize(formatDateCompleto(dataSelecionada))
    : capitalize(formatDateCompleto(dataInicial));

  const anoAtual = dataSelecionada
    ? formatAno(dataSelecionada)
    : formatAno(dataInicial);

  return (
    <Flex
      alignItems="flex-start"
      flexDir="column"
      backgroundColor={secondaryStyle ? secondary500 : primary500}
      color="white"
      maxW="332px"
      boxShadow="md"
      borderRadius="md"
    >
      <Text fontSize="sm" px="12px" fontWeight="normal" pt="12px">
        {anoAtual}
      </Text>
      <Text fontSize="2xl" px="12px" fontWeight="semibold" pb="12px">
        {dataFormatadaCompleta}
      </Text>

      <Controller
        name={name}
        defaultValue={dataInicial}
        render={({ field: { onChange, value } }) => {
          return (
            <Calendar
              minDate={minDate}
              maxDate={maxDate}
              showMonthAndYearPickers={false}
              weekdayDisplayFormat="EEEEE"
              date={value}
              locale={langBR}
              shownDate={value}
              onChange={onChange}
              color={secondaryStyle ? secondary500 : primary500}
              fixedHeight
            />
          );
        }}
      />

      <Flex
        backgroundColor="white"
        w="full"
        padding="12px"
        justifyContent="space-between"
      >
        <ButtonDefault
          width="140px"
          borderRadius="md"
          possuiFuncionalidade={true}
          variant="outlinePill"
          backgroundColor="white"
          border="1px solid"
          borderColor={primary500}
          onClick={() => {
            onCancelButton && onCancelButton();
          }}
        >
          {onCancelButtonText}
        </ButtonDefault>
        <ButtonDefault
          width="140px"
          backgroundColor={secondaryStyle ? secondary500 : primary500}
          possuiFuncionalidade={true}
          color="white"
          borderRadius="md"
          onClick={() => {
            onConfirmButton && onConfirmButton();
          }}
        >
          {onConfirmButtonText}
        </ButtonDefault>
      </Flex>
    </Flex>
  );
};
