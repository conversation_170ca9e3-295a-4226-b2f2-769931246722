import {
  Flex,
  ModalBody,
  Text,
  ModalProps as ModalChakraProps,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  <PERSON><PERSON>,
  Modal<PERSON>eader,
} from '@chakra-ui/react';
import { create, InstanceProps } from 'react-modal-promise';
import { FormProvider, useForm } from 'react-hook-form';

import { ModalDefaultChakra } from 'components/Modal/ModalDefaultChakra';
import { AsyncSelectDefault } from 'components/Select/AsyncSelectDefault';

import { FormData } from './validationForm';
import { useModalClienteAlterarRevenda } from './hooks';

type ModalProps = Omit<ModalChakraProps, 'children' | 'isOpen' | 'onClose'> &
  InstanceProps<ModalChakraProps> & {
    titulo?: string;
    aoConfirmar: ({
      onClose,
      data,
    }: {
      onClose: () => void;
      data: FormData;
    }) => Promise<void>;
    aoCancelar: ({ onClose }: { onClose: () => void }) => void;
  };

export const ModalClienteAlterarRevenda = create<ModalProps>(
  ({
    onReject,
    onResolve,
    aoConfirmar,
    aoCancelar,
    titulo = 'Escolha a nova revenda',
    closeOnOverlayClick = true,
    ...rest
  }) => {
    const {
      isOpen,
      onClose,
      formMethods,
      handleConfirmar,
      handleCancelar,
      obterOpcoesRevenda,
    } = useModalClienteAlterarRevenda({ aoConfirmar, aoCancelar });

    return (
      <ModalDefaultChakra
        closeOnOverlayClick={closeOnOverlayClick}
        size={['full', '2xl']}
        {...rest}
        isCentered
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <ModalContent bg="gray.50">
          <ModalHeader py="40px" px={8}>
            <Text fontSize="20px" textColor="black">
              {titulo}
            </Text>
          </ModalHeader>
          <ModalBody py={0} px={8}>
            <FormProvider {...formMethods}>
              <AsyncSelectDefault
                name="revenda"
                placeholder="Digite o nome e selecione a revenda"
                loadOptions={obterOpcoesRevenda}
                isSearchable
                defaultOptions
              />
            </FormProvider>
          </ModalBody>
          <ModalFooter py="40px" px={8}>
            <Flex
              w="full"
              justifyContent="center"
              alignItems="center"
              mt="10px"
              pb="0"
              px="0"
            >
              <Button
                colorScheme="gray.300"
                mr="20px"
                w="120px"
                variant="outlinePill"
                onClick={() => handleCancelar()}
              >
                Cancelar
              </Button>
              <Button
                w="240px"
                variant="solid"
                colorScheme="secondary"
                color="white"
                onClick={() => handleConfirmar()}
              >
                Alterar revenda
              </Button>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </ModalDefaultChakra>
    );
  }
);
