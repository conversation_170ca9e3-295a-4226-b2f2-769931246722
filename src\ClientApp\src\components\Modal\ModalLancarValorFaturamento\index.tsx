import { useState } from 'react';
import {
  <PERSON>lex,
  ModalBody,
  ModalContent,
  ModalCloseButton,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalProps,
  Text,
  useDisclosure,
  useMediaQuery,
  GridItem,
} from '@chakra-ui/react';
import { useForm, FormProvider } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { LoadingDefault } from 'components/Loading';
import { ButtonDefault } from 'components/Button';
import { ModalDefaultChakra } from 'components/Modal/ModalDefaultChakra';
import { InputNumber } from 'components/Input/InputChakra/InputNumber';
import { TextAreaDefault } from 'components/TextArea';

import { formDefaultValues, yupResolver } from './validationForms';

type ModalLancarValorFaturamentoProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> & {
  faturamentoId: string;
  atualizarListagem: () => void;
} & InstanceProps<ModalProps>;

export const ModalLancarValorFaturamento = create<
  ModalLancarValorFaturamentoProps,
  ModalProps
>(({ onResolve, onReject, faturamentoId, atualizarListagem, ...rest }) => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm({
    defaultValues: formDefaultValues,
    resolver: yupResolver
  });
  const { handleSubmit } = formMethods;

  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  const handleLancarValor = handleSubmit(async (data) => {
    const { valor, motivo } = data;
    
    setIsLoading(true);
    const response = await api.post<void, ResponseApi>(
      ConstantEnderecoWebservice.LANCAR_ACRESCIMO_DESCONTO,
      {
        faturamentoId,
        valor: parseFloat(valor),
        motivo
      }
    );
    
    if (response.sucesso) {
      toast.success('Valor lançado com sucesso!');
      atualizarListagem();
      onClose();
      onResolve();
    }
    setIsLoading(false);
  });

  const handleClose = () => {
    onClose();
    onResolve();
  };

  return (
    <ModalDefaultChakra
      onClose={handleClose}
      size={isLargerThan900 ? 'md' : 'full'}
      isCentered
      {...rest}
    >
      <FormProvider {...formMethods}>
        <ModalContent>
          {isLoading && <LoadingDefault />}
          <ModalHeader
            borderTopRadius="5px"
            bg="primary.500"
            pb="15px"
            borderBottom="1px solid"
            borderColor="gray.50"
          >
            <Flex
              color="white"
              pt="5px"
              justifyContent="space-between"
              alignItems="center"
            >
              <Text pl="2px" color="white">
                Acrésc/Desc
              </Text>
              <ModalCloseButton
                id="closeButton"
                mt="13px"
                mr="10px"
                color="gray.50"
              />
            </Flex>
          </ModalHeader>
          <ModalBody pt="20px" backgroundColor="white">
            <SimpleGridForm>
              <GridItem colSpan={12}>
                <InputNumber
                  name="valor"
                  label="Valor (use sinal negativo para desconto)"
                  id="valor"
                  isRequired
                  placeholder="Informe o valor"
                  leftElement="R$"
                  leftElementColor="black"
                  scale={2}
                  isNegativo
                />
              </GridItem>
              <GridItem colSpan={12}>
                <TextAreaDefault
                  id="motivo"
                  name="motivo"
                  label="Motivo"
                  isRequired
                  placeholder="Informe o motivo do acréscimo ou desconto"
                />
              </GridItem>
            </SimpleGridForm>
          </ModalBody>
          <ModalFooter backgroundColor="white">
            <Flex
              w="full"
              justifyContent="center"
              flexDirection={['column', 'row', 'row']}
            >
              <ButtonDefault
                width={['full', '120px', '120px', '120px']}
                mr="20px"
                mb={['20px', 'undefined', 'undefined']}
                variant="outlinePill"
                onClick={handleClose}
                possuiFuncionalidade={true}
                backgroundColor="white"
                border="1px solid"
                borderColor="gray.100"
              >
                Fechar
              </ButtonDefault>
              <ButtonDefault
                width={['full', '120px', '120px', '120px']}
                colorScheme="secondary"
                onClick={handleLancarValor}
                possuiFuncionalidade={true}
              >
                Salvar
              </ButtonDefault>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </FormProvider>
    </ModalDefaultChakra>
  );
});




