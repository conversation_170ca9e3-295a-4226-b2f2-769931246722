import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';

import { ContainerAutenticacao } from 'components/Layout/Autenticacao';
import { Login } from 'pages/Autenticacao/Login';
import { RecuperarSenha } from 'pages/Autenticacao/RecuperarSenha';
import { SenhaEnviadaSucesso } from 'pages/Autenticacao/SenhaEnviadaSucesso';
import { RedefinirSenha } from 'pages/Autenticacao/RedefinirSenha';
import { SenhaRedefinidaSucesso } from 'pages/Autenticacao/SenhaRedefinidaSucesso';

export const AutenticacaoRoutes = [
  <Route
    key={ConstanteRotas.LOGIN}
    path={ConstanteRotas.LOGIN}
    element={
      <ContainerAutenticacao>
        <Login />
      </ContainerAutenticacao>
    }
  />,
  <Route
    key={ConstanteRotas.LOGIN}
    path={ConstanteRotas.RECUPERAR_SENHA}
    element={
      <ContainerAutenticacao>
        <RecuperarSenha />
      </ContainerAutenticacao>
    }
  />,
  <Route
    key={ConstanteRotas.LOGIN}
    path={ConstanteRotas.SENHA_ENVIADA_COM_SUCESSO}
    element={
      <ContainerAutenticacao>
        <SenhaEnviadaSucesso />
      </ContainerAutenticacao>
    }
  />,
  <Route
    key={ConstanteRotas.LOGIN}
    path={ConstanteRotas.REDEFINIR_SENHA}
    element={
      <ContainerAutenticacao>
        <RedefinirSenha />
      </ContainerAutenticacao>
    }
  />,
  <Route
    key={ConstanteRotas.LOGIN}
    path={ConstanteRotas.SENHA_REDEFINIDA_COM_SUCESSO}
    element={
      <ContainerAutenticacao>
        <SenhaRedefinidaSucesso />
      </ContainerAutenticacao>
    }
  />,
];
