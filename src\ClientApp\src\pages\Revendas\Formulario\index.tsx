import {
  Flex,
  GridItem,
  Td,
  Tr,
  Text,
  Icon,
  Box,
  Table,
  Tbody,
  Th,
  Thead,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { FormProvider, useFormContext } from 'react-hook-form';

import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { formatDateHourMinuteSecond } from 'helpers/format/formatStringDate';
import api, { ResponseApi } from 'services/api';
import { CepResponse } from 'services/viacep';
import auth from 'modules/auth';
import { useEffectDefault } from 'hook/useEffectDefault';
import { formatValuePais } from 'helpers/format/formatValuePais';
import { daysOfWorkingEnum } from 'constants/Enum/enumWorkingDays';
// Update the path below to the correct relative path where EnumTipoUsuario is defined.
// For example, if the file is at src/enums/EnumTipoUsuario.ts, use:
import { EnumTipoUsuario } from 'constants/Enum/enumTipoUsuario';

import { LixeiraIcon } from 'icons';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { AccordionDefault } from 'components/Accordion';
import { ButtonDefault } from 'components/Button';
import { SwitchDefault } from 'components/Switch';
import { LoadingDefault } from 'components/Loading';
import {
  CnpjResponse,
  InputCpfCnpj,
} from 'components/Input/InputChakra/InputCpfCnpj';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { InputCep } from 'components/Input/InputChakra/InputCep';
import { InputPhone } from 'components/Input/InputChakra/InputPhone';
import { SelectCidade } from 'components/Select/SelectCidade';
import { SelectDefault } from 'components/Select/SelectDefault';
import { paisDefault, SelectPais } from 'components/Select/SelectPais';

import { FormRevendaProps, GradeServicos } from './validationForm';
import { WorkingDays } from './components/WorkingDays';

export const FormRevenda = ({
  servicosCadastrados,
  listaServicos,
  setListaServicos,
}: FormRevendaProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [motivoCancelamentoDesabilitado, setMotivoCancelamentoDesabilitado] = useState(false);
  const [isSistemaAdmin, setIsSistemaAdmin] = useState(false);

  // Adicionar useEffect para verificar se o usuário é Sistema Admin
  useEffect(() => {
    // Verifica se o usuário logado é Sistema Admin
    const userType = auth.getTipoUsuarioValue();
    setIsSistemaAdmin(userType === EnumTipoUsuario.SISTEMA_ADMIN);
  }, []);

  const rotaAtual = useLocation();
  const isAlterar = rotaAtual.pathname.includes('alterar');

  const formMethods = useFormContext();
  const { watch, setValue, getValues } = formMethods;

  const [
    revendaEstaAtivaWatch,
    servicoSelecionadoWatch,
    cidadeWatch,
    dataCancelamentoWatch,
    motivoCancelamentoWatch,
    paisNomeWatch,
  ] = watch([
    'ativo',
    'servicoSelecionado',
    'cidadeId',
    'dataHoraCancelamento',
    'grades',
    'motivoCancelamento',
    'paisId',
  ]);

  const possuiPermissaoExcluirServicoGrade = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.CadastroRevendaAcao.EXCLUIR_REVENDAS
  );

  const valuePais = formatValuePais(paisNomeWatch);

  const listaServicosRevendaNaoAdicionadas = useCallback(() => {
    const newListaServicos = listaServicos?.filter(
      (servico) => servico.servicoEstaAdicionado === false
    );

    return newListaServicos || [];
  }, [listaServicos])();

  const listaServicoRevendasAdicionadas = useCallback(() => {
    const newListaServicos = listaServicos?.filter(
      (servico) => servico.servicoEstaAdicionado === true
    );

    return newListaServicos || [];
  }, [listaServicos])();

  const handleExcluirServico = (idServico: string) => {
    setListaServicos((servicos) => {
      const newListaServicos = servicos.map((servico) => {
        return {
          ...servico,
          servicoEstaAdicionado:
            servico.id === idServico ? false : servico.servicoEstaAdicionado,
        };
      });

      return newListaServicos;
    });
  };

  const handleAdicionarServico = useCallback(() => {
    if (servicoSelecionadoWatch) {
      setListaServicos((servicos) => {
        const servicosEscolhidos = servicos.map((servico) => ({
          ...servico,
          servicoEstaAdicionado:
            servico.id === servicoSelecionadoWatch?.id
              ? true
              : servico.servicoEstaAdicionado,
        }));
        return servicosEscolhidos;
      });
    }
    setValue('servicoSelecionado', null);
  }, [servicoSelecionadoWatch]);

  const getGrades = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<GradeServicos[]>>(
      ConstantEnderecoWebservice.GRADE_LISTAR_SELECT
    );

    if (response.sucesso) {
      const { dados } = response;
      const servicosDisponiveis = dados.map((opcao) => {
        return {
          id: opcao.id,
          label: opcao.nome,
          value: opcao.id,
          nome: opcao.produto,
          servicoEstaAdicionado: false,
        };
      });
      setListaServicos(servicosDisponiveis);
    } else {
      setListaServicos([]);
    }

    setIsLoading(false);
  }, []);

  const getInfoCnpj = useCallback(
    (valuesCnpj: CnpjResponse) => {
      const {
        nome,
        fantasia,
        cep,
        logradouro,
        numero,
        complemento,
        bairro,
        telefone,
        municipio,
        uf,
      } = valuesCnpj;

      setValue('razaoSocial', nome);
      setValue('nomeFantasia', fantasia);
      setValue('cep', cep);
      setValue('logradouro', logradouro);
      setValue('numero', numero);
      setValue('complemento', complemento);
      setValue('bairro', bairro);
      setValue('telefone', telefone);
      setValue('cidadeId', municipio);
      setValue('estado', uf);
      setValue('paisId', paisDefault);
    },
    [setValue]
  );

  const getInfoCep = useCallback(
    (data: CepResponse) => {
      setValue('bairro', data?.bairro);
      setValue('complemento', data?.complemento);
      setValue('logradouro', data?.logradouro);
      setValue('cidadeId', data?.localidade);
      setValue('estado', data?.uf);
      setValue('paisId', paisDefault);
    },
    [setValue]
  );

  const copyEmailFinanceiro = async (copyMe: string) => {
    setValue('emailFinanceiro', copyMe);
  };

  useEffectDefault(() => {
    getGrades();
  }, [getGrades]);

  useEffect(() => {
    if (cidadeWatch) {
      setValue('estado', cidadeWatch?.estadoSigla as string);
    } else {
      setValue('estado', '');
    }
  }, [cidadeWatch, setValue]);

  useEffect(() => {
    if (servicosCadastrados && isAlterar) {
      setListaServicos((servicos) => {
        const opcoesNaoSelecionadas = servicos.map((servico) => ({
          ...servico,
          servicoEstaAdicionado: servicosCadastrados.some(
            (servicoCadastrados) => servicoCadastrados.id === servico.id
          ),
        }));
        return opcoesNaoSelecionadas;
      });
    }
  }, [servicosCadastrados]);

  useEffect(() => {
    if (revendaEstaAtivaWatch) {
      setMotivoCancelamentoDesabilitado(true);
      setValue('motivoCancelamento', '');
    } else {
      setMotivoCancelamentoDesabilitado(false);
    }
  }, [revendaEstaAtivaWatch]);

  return (
    <>
      {isLoading && <LoadingDefault />}
      <SimpleGridForm>
        <GridItem colSpan={[12, 12, 3]}>
          <InputCpfCnpj
            name="cnpj"
            asCnpj
            placeholder="Informe o CNPJ"
            label="Cnpj"
            isRequired
            getCnpjData={(valuesCnpj) => getInfoCnpj(valuesCnpj)}
          />
        </GridItem>
        <GridItem colSpan={[12, 12, 9]}>
          <InputDefault
            name="razaoSocial"
            placeholder="Informe a razão social"
            label="Razão social"
            isRequired
          />
        </GridItem>
        <GridItem colSpan={[12, 12, 6]}>
          <InputDefault
            name="nomeFantasia"
            placeholder="Informe o nome fantasia"
            label="Nome fantasia"
            isRequired
          />
        </GridItem>
        <GridItem colSpan={[12, 12, 3]}>
          <InputDefault
            name="inscricaoEstadual"
            placeholder="Informe a inscrição estadual"
            label="Inscrição estadual"
            type="number"
          />
        </GridItem>
        <GridItem colSpan={[12, 12, 3]}>
          <InputDefault
            name="inscricaoMunicipal"
            placeholder="Informe a inscrição municipal"
            label="Inscrição municipal"
          />
        </GridItem>
      </SimpleGridForm>

      <AccordionDefault
        mt="10"
        defaultIndex={[0, 1, 2, 3, 4]}
        contentAccordion={[
          {
            title: 'Dados da empresa',
            children: (
              <SimpleGridForm>
                <GridItem colSpan={[12, 12, 3, 2]}>
                  <InputCep
                    name="cep"
                    placeholder="Informe o cep"
                    label="Cep"
                    isRequired
                    getInfoCep={(valuesCep) => getInfoCep(valuesCep)}
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 7, 8]}>
                  <InputDefault
                    name="logradouro"
                    placeholder="Informe o nome da rua"
                    label="Logradouro"
                    isRequired
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 2]}>
                  <InputDefault
                    name="numero"
                    placeholder="Informe o número"
                    label="Número"
                    type="number"
                    isRequired
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 4, 2, 2]}>
                  <InputDefault
                    name="complemento"
                    placeholder="Informe um complemento"
                    label="Complemento"
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 8, 4, 3]}>
                  <InputDefault
                    name="bairro"
                    placeholder="Informe o nome do bairro"
                    label="Bairro"
                    isRequired
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 5, 4, 3]}>
                  <SelectCidade
                    name="cidadeId"
                    placeholder="Informe o nome da cidade"
                    label="Cidade"
                    isRequired
                    isSearchable
                    paisId={valuePais}
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 3, 2, 1]}>
                  <InputDefault name="estado" label="Estado" isDisabled />
                </GridItem>
                <GridItem colSpan={[12, 12, 5, 4, 3]}>
                  <SelectPais
                    name="paisId"
                    onSelect={(data) => {
                      if (data.value !== 1) {
                        setValue('cep', '');
                        setValue('bairro', '');
                        setValue('logradouro', '');
                      }
                    }}
                    label="País"
                    isRequired
                    isSearchable
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 6, 4, 2]}>
                  <InputPhone
                    name="celular"
                    placeholder="Informe o celular"
                    label="Celular"
                    isRequired
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 6, 4, 2]}>
                  <InputPhone
                    name="telefone"
                    placeholder="Informe o telefone"
                    label="Telefone"
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 6, 6, 4]}>
                  <InputDefault
                    name="emailContato"
                    type="email"
                    placeholder="Informe o e-mail para contato"
                    label="E-mail contato"
                    isRequired
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 6, 6, 4]} position="relative">
                  <Text
                    fontSize="xs"
                    color="blue.500"
                    cursor="pointer"
                    position="absolute"
                    right="0"
                    zIndex="docked"
                    onClick={() =>
                      copyEmailFinanceiro(getValues('emailContato'))
                    }
                  >
                    Copiar e-mail contato
                  </Text>
                  <InputDefault
                    name="emailFinanceiro"
                    type="email"
                    placeholder="Informe o e-mail financeiro"
                    label="E-mail financeiro"
                    isRequired
                  />
                </GridItem>
              </SimpleGridForm>
            ),
          },
          {
            title: 'Dados do responsável',
            children: (
              <SimpleGridForm>
                <GridItem colSpan={[12, 12, 3]}>
                  <InputCpfCnpj
                    asCpf
                    name="cpfResponsavel"
                    placeholder="Informe o cpf do responsável"
                    label="Cpf"
                    isRequired
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 9]}>
                  <InputDefault
                    name="nomeResponsavel"
                    placeholder="Informe o nome do responsável"
                    label="Nome"
                    isRequired
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 4]}>
                  <InputDefault
                    name="login"
                    placeholder="Informe seu login de acesso ao sistema"
                    blockSpecialCharacters
                    label="Login para acesso ao sistema"
                    isRequired
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 8]}>
                  <InputDefault
                    name="emailResponsavel"
                    type="email"
                    placeholder="Informe o e-mail do responsável"
                    label="E-mail"
                    isRequired
                  />
                </GridItem>
              </SimpleGridForm>
            ),
          },
          {
            title: 'Configuração para uso no sistema',
            children: (
              <SimpleGridForm>
                <GridItem colSpan={[12, 12, 7, 6]}>
                  <InputDefault
                    name="linkSuporte"
                    placeholder="Informe o link para contato de suporte"
                    label="Link para contato de suporte (Ex: WhatsApp)"
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 5, 6, 4]}>
                  <InputPhone
                    name="telefoneSuporte"
                    placeholder="Informe o celular ou telefone para contato"
                    label="Telefone para contato de suporte"
                  />
                </GridItem>
                <GridItem colSpan={[12, 12, 12, 12, 12]}>
                  <Text fontWeight="semibold" fontSize="xs" mb="16px">
                    Horários de funcionamento do suporte
                  </Text>
                  <WorkingDays
                    daysOfWorkingEnum={daysOfWorkingEnum}
                    name="horariosFuncionamento"
                  />
                </GridItem>

              </SimpleGridForm>
            ),
          },
          isAlterar && (auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroRevendaAcao.EXCLUIR_REVENDAS) || !isAlterar)
            ? {
                title: 'Informações de cancelamento',
                children: (
                  <FormProvider {...formMethods}>
                    <SimpleGridForm>
                      <GridItem colSpan={[12, 12, 12, 2, 1]} mt="26px">
                        <SwitchDefault
                          id="ativo"
                          name="ativo"
                          textoAuxiliar={
                             revendaEstaAtivaWatch ? 'Ativo' : 'Inativo'
                          }
                          isChecked={revendaEstaAtivaWatch}
                          colorScheme="blue"
                        />
                      </GridItem>
                      <GridItem colSpan={[12, 12, 12, 4, 5]} ml="25px">
                        <InputDefault
                          name="motivoCancelamento"
                          placeholder="Informe o motivo do cancelamento"
                          label="Motivo"
                          isDisabled={motivoCancelamentoDesabilitado}
                        />
                      </GridItem>
                      <GridItem colSpan={[12, 12, 12, 4, 5]}>
                        {dataCancelamentoWatch && !revendaEstaAtivaWatch && (
                          <InputDefault
                            label="Data/Hora"
                            name="dataHoraCancelamento"
                            isDisabled={true}
                            w="full"
                            value={formatDateHourMinuteSecond(
                              dataCancelamentoWatch
                            )}
                          />
                        )}
                      </GridItem>
                    </SimpleGridForm>
                  </FormProvider>
                ),
              }
            : {
                title: '',
                children: null,
              },

            auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroRevendaAcao.PARAMETROS_STI3_REVENDAS)
            ? 
            {
                title: 'Dados STi3',
                children: (
                  <FormProvider {...formMethods}>
                    <SimpleGridForm>
                  <GridItem colSpan={[12, 12, 12, 12, 10]}>
                    <InputDefault
                      name="movideskToken"
                      label="Token do Movidesk para integrar ao Chat da Revenda"
                      placeholder="Informe o token do movidesk"
                    />
                  </GridItem>                      
                  <GridItem colSpan={[12, 12, 12, 12, 10]}>
                        <InputDefault
                          name="consultaPendenciaWS"
                          placeholder="Informe SIM para STi3"
                          label="Caminho WS para consulta de pendências"
                        />
                      </GridItem>
                    </SimpleGridForm>
                  </FormProvider>
                ),
              }
            : {
                title: '',
                children: null,
              },
          auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroRevendaAcao.EXCLUIR_REVENDAS)
            ? {
                title: 'Grades de serviços',
                children: (
                  <SimpleGridForm>
                    <GridItem colSpan={[12, 12, 6]}>
                      <SelectDefault
                        name="servicoSelecionado"
                        label="Grade de serviço"
                        options={listaServicosRevendaNaoAdicionadas}
                        isDisabled={!revendaEstaAtivaWatch}
                      />
                    </GridItem>
                    <GridItem colSpan={[12, 12, 6]}>
                      <ButtonDefault
                        width={['full', 'full', 'full', '220px']}
                        color="white"
                        colorScheme="green"
                        mt={['0px', '6']}
                        leftIcon={<IoIosAddCircleOutline />}
                        possuiFuncionalidade={true}
                        isDisabled={!servicoSelecionadoWatch}
                        onClick={handleAdicionarServico}
                      >
                        Adicionar
                      </ButtonDefault>
                    </GridItem>
                    <GridItem colSpan={[12, 12, 12]}>
                      {listaServicoRevendasAdicionadas.length > 0 ? (
                        <Box w="full" overflow="auto">
                          <Table
                            sx={{
                              '& tr > th': { borderBottom: 'none' },
                              '& td:only-child': {
                                bg: 'white',
                                h: '60px',
                                border: 'none',
                              },
                              '& tr': { boxShadow: 'none' },
                              '& th': {
                                fontSize: '2xs',
                                color: 'gray.300',
                                paddingBottom: '5px',
                              },
                            }}
                            variant="simple-card"
                            mt="20px"
                          >
                            <Thead>
                              <Tr>
                                <Th fontSize="xs" w="30%">
                                  Grade
                                </Th>
                                <Th minW="180px" w="20%">
                                  Nome
                                </Th>
                                {possuiPermissaoExcluirServicoGrade && (
                                  <Th w="50%" isNumeric textAlign="end">
                                    Ações
                                  </Th>
                                )}
                              </Tr>
                            </Thead>
                            <Tbody>
                              {listaServicoRevendasAdicionadas.map(
                                (servico, index) => {
                                  return (
                                    <>
                                      <Box h={index === 0 ? '0' : '2'} />
                                      <Tr key={servico?.id} color="black">
                                        <Td>{servico?.label}</Td>
                                        <Td>{servico?.nome}</Td>
                                        {possuiPermissaoExcluirServicoGrade && (
                                          <Td>
                                            <Flex
                                              justifyContent="flex-end"
                                              mr="5px"
                                            >
                                              <Icon
                                                cursor="pointer"
                                                as={LixeiraIcon}
                                                onClick={() =>
                                                  handleExcluirServico(servico.id)
                                                }
                                              />
                                            </Flex>
                                          </Td>
                                        )}
                                      </Tr>
                                    </>
                                  );
                                }
                              )}
                            </Tbody>
                          </Table>
                        </Box>
                      ) : (
                        <Text mt="20px" fontSize="xs">
                          Nenhum serviço foi adicionado
                        </Text>
                      )}
                    </GridItem>
                  </SimpleGridForm>
                ),
              }
            : {
                title: '',
                children: null,
              },
        ]}
      />
    </>
  );
};
