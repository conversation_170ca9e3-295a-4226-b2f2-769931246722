import { Flex, Text, useMediaQuery } from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';

import { daysOfWorkingEnum } from 'constants/Enum/enumWorkingDays';

import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SwitchDefault } from 'components/Switch';

type WorkingDaysProps = {
  daysOfWorkingEnum: typeof daysOfWorkingEnum;
  name: string;
};

export const WorkingDays = ({ daysOfWorkingEnum, name }: WorkingDaysProps) => {
  const { watch } = useFormContext();
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  return (
    <>
      {daysOfWorkingEnum.map((day, index) => {
        const isDayOpen = watch(`${name}[${index}].aberto`) || false;

        return (
          <Flex
            key={index}
            gap={['12px', '36px']}
            alignItems="center"
            minHeight="36px"
            my="32px"
            wrap="wrap"
          >
            <Text
              fontSize="sm"
              mr="24px"
              fontWeight="semibold"
              w={['90px', '120px']}
            >
              {day}
            </Text>
            <SwitchDefault
              name={`${name}[${index}].aberto`}
              id={`${name}[${index}].aberto`}
              textoAuxiliar={isDayOpen ? 'Aberto' : 'Fechado'}
              width={['100px', '120px']}
              mb="4px"
              isChecked={isDayOpen}
            />
            {isDayOpen && (
              <Flex align="center" gap="24px">
                <InputDefault
                  w={['100px', '120px']}
                  name={`${name}[${index}].abertura`}
                  type="time"
                  isRequired
                />
                {isLargerThan900 && <Text>até</Text>}
                <InputDefault
                  w={['100px', '120px']}
                  name={`${name}[${index}].fechamento`}
                  type="time"
                  isRequired
                />
              </Flex>
            )}
          </Flex>
        );
      })}
    </>
  );
};
