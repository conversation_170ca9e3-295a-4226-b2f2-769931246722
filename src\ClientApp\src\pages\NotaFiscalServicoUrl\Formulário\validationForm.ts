import { useForm as useFormInstance } from 'react-hook-form';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

import { SelectOptions } from 'components/Select/SelectDefault';

import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

export type FormData = {
  id: string;
  servico: null | SelectOptions;
  modeloNf: null | SelectOptions;
  notaFiscalAutorizador: null | SelectOptions;
  urlProducao: string;
  urlHomologacao: string;
  versaoWebService: string;
  versaoServico: string;
};

export const formDefaultValues = {
  servico: null,
  notaFiscalAutorizador: null,
  modeloNf: null,
  urlProducao: '',
  urlHomologacao: '',
  versaoWebService: '',
  versaoServico: '',
};

const schema = yup.object().shape({
  notaFiscalAutorizador: yup
    .object()
    .nullable()
    .default(null)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  servico: yup
    .object()
    .nullable()
    .default(null)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  modeloNf: yup
    .object()
    .nullable()
    .default(null)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),

  versaoWebService: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  versaoServico: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  urlProducao: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  urlHomologacao: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

export const yupResolver = yupResolverInstance(schema);

export const useForm = useFormInstance;
