import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { NotaFiscalServicoURL } from 'pages/NotaFiscalServicoUrl/Listar';
import { AlterarNotaFiscalServicoURL } from 'pages/NotaFiscalServicoUrl/Formulário/Alterar';
import { CadastrarNotaFiscalServicoURL } from 'pages/NotaFiscalServicoUrl/Formulário/Cadastrar';

import LayoutGuard from './LayoutGuard';

export const NotaFiscalServicoURLRoutes = [
  <Route
    key={ConstanteRotas.NOTAFISCAL_SERVICO_URL_LISTAGEM}
    path={ConstanteRotas.NOTAFISCAL_SERVICO_URL_LISTAGEM}
    element={
      <LayoutGuard
        key={ConstanteRotas.NOTAFISCAL_SERVICO_URL_LISTAGEM}
        breadcrumb={[
          { title: 'Fiscal' },
          {
            title: 'NF Serviços URL',
            path: ConstanteRotas.NOTAFISCAL_SERVICO_URL_LISTAGEM,
          },
        ]}
        component={<NotaFiscalServicoURL />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_FISCAL_NOTA_FISCAL_URL)}
      />
    }
  />,

  <Route
    key={ConstanteRotas.NOTAFISCAL_SERVICO_URL_CADASTRAR}
    path={ConstanteRotas.NOTAFISCAL_SERVICO_URL_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.NOTAFISCAL_SERVICO_URL_CADASTRAR}
        breadcrumb={[
          { title: 'Fiscal' },
          {
            title: 'NF Serviços URL',
            path: ConstanteRotas.NOTAFISCAL_SERVICO_URL_LISTAGEM,
          },
          {
            title: 'Cadastrar',
          },
        ]}
        component={<CadastrarNotaFiscalServicoURL />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.NotaFiscalUrlAcao.CADASTRAR_NF_SERVICO)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.NOTAFISCAL_SERVICO_URL_ALTERAR}
    path={ConstanteRotas.NOTAFISCAL_SERVICO_URL_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.NOTAFISCAL_SERVICO_URL_ALTERAR}
        breadcrumb={[
          { title: 'Fiscal' },
          {
            title: 'NF Serviços URL',
            path: ConstanteRotas.NOTAFISCAL_SERVICO_URL_LISTAGEM,
          },
          {
            title: 'Alterar',
          },
        ]}
        component={<AlterarNotaFiscalServicoURL />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.NotaFiscalUrlAcao.ALTERAR_NF_SERVICO)}
      />
    }
  />,
];
