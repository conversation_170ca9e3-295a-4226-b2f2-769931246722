import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';

export type EstadoProps = {
  id: number;
  nome: string;
  sigla: string;
  codigo: string | null;
  paisId: number | null;
};

export const listarSelectEstado = (params: {
  nome: string;
  paisId?: number | null;
}) => {
  const { nome, paisId } = params;

  return api.get<void, ResponseApi<EstadoProps[]>>(
    ConstantEnderecoWebservice.ESTADO_LISTAR_SELECT,
    { params: { nome, paisId } }
  );
};
