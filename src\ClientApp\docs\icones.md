# Como criar ícones no projeto a partir de um SVG

> Os ícones que serão importados para o projeto deverão ter o formato SVG para que possamos utilizá-lo desta forma.

## Contexto sobre SVG e sobre essa utilização

O SVG utiliza XML para descrever as linhas e formas de um vetor (imagem), logo, a sintaxe interna desse formato é compatível com HTML. Nessa abordagem, nós utilizaremos essa compatibilidade para que possamos exportar o SVG de dentro do projeto ao invés de ter que acessá-lo como arquivo (coisa tal que além de facilitar o acesso, torna a renderização dele mais rápida).

Existem algumas tags que são utilizadas para descrever essas linhas e formas do SVG, dentre elas, `<rect />`, `<polygon />`, `<path />`, `<line />`, `<circle />`, entre outras... E existem também tags (principalmente a tag `<g />`) que servem somente para agrupar essas tags de formação do vetor, podendo assim setar estilos que determinado grupo de tags de formação utilizarão.

## Exemplo de importação de um SVG para o projeto

Os designers nos passarão os ícones em formato .svg, então abriremos estes utilizando um editor de texto, encontrado um conteúdo parecido com a seguinte sintaxe:

```
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 23 17">
  <defs>
    <style>
      .cls-1 {
        fill: none;
        stroke: #404040;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 0.75px;
      }
    </style>
  </defs>
  <g id="submenu_-_textos" data-name="submenu - textos">
    <g>
      <path class="cls-1" d="M11.5.5C5.5.5.6,3.8.6,7.9a6.5,6.5,0,0,0,3.7,5.6,6.6,6.6,0,0,1-.6,3.1c0,.1,0,.2.1.2A8,8,0,0,0,8.2,15a12.4,12.4,0,0,0,3.3.4c6.1,0,11-3.3,11-7.5S17.6.5,11.5.5Z"/>
      <line class="cls-1" x1="15.9" y1="6.2" x2="7.2" y2="6.2"/>
      <line class="cls-1" x1="15.9" y1="9.7" x2="7.2" y2="9.7"/>
    </g>
  </g>
</svg>
```

Para começar, o primeiro ponto que devemos capturar é a viewBox do SVG, que identifica o tamanho da "tela de pintura" que este foi criado, coisa tal que é essêncial para que as coordanadas das linhas e formas se encaixem corretamente.

```
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 23 17">
```

Neste exemplo, nossa viewBox é "0 0 23 17".

Após termos a viewBox, precisamos entender a estrutura de elementos e estilo que temos dentro do SVG. É fácil de entendermos estes pontos ao pensar que o SVG nada mais é que um "HTML" para vetores, sendo assim, os estilos podem ser passados inline ou por class/id (como é o nosso exemplo), e os elementos são tags que recebem props como qualquer outra.

Ao copiar os elementos do SVG, delete as classes e ids deles, e passe os estilos para um wrapper (note que no caso eu passo ao style um objeto de estilos, que serve para facilitar a estilização, visto que a maioria dos SVGs do projeto seguem os mesmo parâmetros)

```
<g style={Stroke075Rounded}>
  <path d="M11.5.5C5.5.5.6,3.8.6,7.9a6.5,6.5,0,0,0,3.7,5.6,6.6,6.6,0,0,1-.6,3.1c0,.1,0,.2.1.2A8,8,0,0,0,8.2,15a12.4,12.4,0,0,0,3.3.4c6.1,0,11-3.3,11-7.5S17.6.5,11.5.5Z" />
  <line x1="15.9" y1="6.2" x2="7.2" y2="6.2" />
  <line x1="15.9" y1="9.7" x2="7.2" y2="9.7" />
</g>
```

Depois de termos essas informações, só falta criarmos o objeto em `src/icons/index.tsx`, seguindo os padrões de props já estabelecidos em outros ícones, que podem ser utilizados como exemplo. Utilizando o SVG do nosso exemplo, o objeto que resultaria é:

```
export const NomeDoIcone = createIcon ({
  displayName: 'fiscal icon',
  viewBox: '0 0 23 23',
  path:
   <g style={Stroke075Rounded}>
      <path d="M11.5.5C5.5.5.6,3.8.6,7.9a6.5,6.5,0,0,0,3.7,5.6,6.6,6.6,0,0,1-.6,3.1c0,.1,0,.2.1.2A8,8,0,0,0,8.2,15a12.4,12.4,0,0,0,3.3.4c6.1,0,11-3.3,11-7.5S17.6.5,11.5.5Z" />
      <line x1="15.9" y1="6.2" x2="7.2" y2="6.2" />
      <line x1="15.9" y1="9.7" x2="7.2" y2="9.7" />
    </g>
});

```

> O SVG pode ter mais que uma class ou padrão de estilização, mas a solução é a mesma, crie um wrapper para este segundo estilo e agrupe os elementos que o usam

> **Importante:** Quando for importar os estilos que contem cores (fill e stroke), utilize "currentColor" ao invés de uma cor fixa, para que possamos utilizar o ícone com qualquer cor. [Explicação sobre o currentColor aqui.](https://tableless.com.br/css-3-o-valor-currentcolor/)

E para utilizá-lo, importaríamos assim, e poderiamos usar como um componente JSX

```
import { NomeDoIcone } from 'icons';
import { Box } from '@chakra-ui/react';

const Pagina = () => {
  return (
    <Box>
      <NomeDoIcone />
    </Box>
  );
}
```

Caso for utilizar um ícone com Chakra, use o wrapper deles para que possamos acessar as props de UI-Declarativas da biblioteca, exemplo:

```
import { Box, Icon } from '@chakra-ui/react';

import { NomeDoIcone } from 'icons';

const Pagina = () => {
  return (
    <Box>
      <Icon as={NomeDoIcone} fontSize="2xl" color="secondary.500" /> // Por exemplo, eu posso acessar o fontSize do Chakra para definir o tamanho do ícone, ou o color para definir a cor
    </Box>
  );
}
```
