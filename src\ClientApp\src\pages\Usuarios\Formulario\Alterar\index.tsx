import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import { EnumTipoUsuario } from 'constants/Enum/enumTipoUsuario';
import auth from 'modules/auth';
import api, { ResponseApi } from 'services/api';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormUsuario } from '..';
import { FormData, yupResolver, formDefaultValues } from '../validationForms';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';

export const AlterarUsuarios = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSistemaAdmin, setIsPermission] = useState(false);

  const navigate = useNavigate();

  const paramsRouter = useParams();
  const { id: usuarioId } = paramsRouter;

  const formMethods = useForm<FormData>({
    defaultValues: formDefaultValues,
    resolver: yupResolver,
  });

  const { handleSubmit: onSubmit, reset } = formMethods;

  useEffect(() => {
    setIsPermission(auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroUsuarioAcao.USUARIO_ALTERAR));
  }, []);


  const getUsuarios = useCallback(async () => {
    const response = await api.get<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.OBTER_USUARIO,
      {
        params: {
          usuarioId,
        },
      }
    );
    if (response.sucesso) {
      reset(response.dados);
    }
  }, [reset, usuarioId]);

  const handleSubmit = onSubmit(async (data) => {
    setIsLoading(true);
    const response = await api.put<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.ALTERAR_USUARIO,
      {
        ...data,
        id: usuarioId,
      }
    );
    if (response.sucesso) {
      navigate(ConstanteRotas.USUARIOS);
      toast.success('Cadastro alterado com sucesso.');
    }
    setIsLoading(false);
  });

  useEffect(() => {
    getUsuarios();
  }, [getUsuarios]);

  return (
    <LayoutFormPage onSubmit={() => handleSubmit()} isLoading={isLoading}>
      <FormProvider {...formMethods}>
        <FormUsuario isSistemaAdmin={isSistemaAdmin} />
      </FormProvider>
    </LayoutFormPage>
  );
};
