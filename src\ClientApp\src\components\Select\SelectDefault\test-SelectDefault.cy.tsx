import { <PERSON>kra<PERSON>rovider } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';

import { theme } from 'theme';
import { hexToRgbA } from 'store/getHexDecimalColor';

import { SelectDefault } from './index';

const valueLabel = 'Cpf';
const valueColorLabel = '#6502b2';
const valueOptions = [
  {
    label: 'one',
    value: 1,
  },
  {
    label: 'two',
    value: 2,
  },
  {
    label: 'three',
    value: 3,
  },
];

function Select({ ...rest }) {
  const formMethods = useForm(); // initialise the hook
  return (
    <ChakraProvider theme={theme}>
      <FormProvider {...formMethods}>
        <SelectDefault
          label={valueLabel}
          filtrosAtivos
          colorLabel={valueColorLabel}
          id="testSelect"
          name="testSelect"
          options={valueOptions}
          {...rest}
        />
      </FormProvider>
    </ChakraProvider>
  );
}

describe('Testing default select', () => {
  beforeEach(() => {
    cy.mount(<Select />);
  });

  it('Testing value select', () => {
    cy.get('#testSelect')
      .type('3{enter}{enter}')
      .should((value) => {
        expect(value).to.equal(value);
      });
  });

  it('Testing non-existent value', () => {
    cy.get('#testSelect')
      .type('5123{enter}{enter}')
      .invoke('val')
      .should((value) => {
        expect(value).to.equal(value);
      });
  });

  it('Select label is showing', () => {
    cy.testLabelInput(valueLabel);
  });

  it('Validating the color of the label', () => {
    cy.testColorLabel(valueLabel, hexToRgbA(valueColorLabel));
  });

  valueOptions.forEach((item) => {
    it('Test by selecting all select options', () => {
      cy.get('#testSelect').click();
      cy.get(`.react-select__option${item.label}`)
        .scrollIntoView()
        .click()
        .should(() => {
          expect(item.label).to.equal(item.label);
        });
    });
  });
});
