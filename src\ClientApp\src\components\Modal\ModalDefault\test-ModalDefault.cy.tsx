import { ChakraProvider } from '@chakra-ui/react';

import FullScreenProvider from 'store/FullScreen';
import { theme } from 'theme';

import { ButtonDefault } from 'components/Button';

import { ModalDefault } from '.';

describe('Testing the scope of modals', () => {
  beforeEach(() => {
    cy.mount(
      <ChakraProvider theme={theme}>
        <FullScreenProvider>
          <ButtonDefault
            colorScheme="green"
            id="buttonDefault"
            onClick={() => ModalDefault({ size: 'md' })}
            possuiFuncionalidade={true}
          >
            Entrar
          </ButtonDefault>
        </FullScreenProvider>
      </ChakraProvider>
    );

    cy.get('#buttonDefault').should('contains.text', 'Entrar').click();
  });

  it('Testing save functionality in modal', () => {
    cy.get('#buttonSalvar').should('contains.text', 'Salvar').click();
  });

  it('Testing calcel functionality in modal', () => {
    cy.get('#buttonCalcelar').should('contains.text', 'Cancelar').click();
  });

  it('Testing close functionality in modal', () => {
    cy.get('#closeButton').click();
  });
});
