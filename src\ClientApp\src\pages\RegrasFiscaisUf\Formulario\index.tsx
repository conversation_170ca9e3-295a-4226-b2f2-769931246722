import { FormLabel, GridItem } from '@chakra-ui/react';

import { EnumEstadosSigla } from 'constants/Enum/enumEstados';
import { EnumModelosDocumento } from 'constants/Enum/enumModeloDocRegraFiscal';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SelectDefault } from 'components/Select/SelectDefault';
import { TextAreaDefault } from 'components/TextArea';

export const FormRegraFiscalUf = () => {
  return (
    <SimpleGridForm>
      <GridItem colSpan={[12, 12, 4]}>
        <InputDefault
          fontLabel="12px"
          name="codigoRegra"
          placeholder="Informe o Código"
          label="Código da Regra"
          isRequired
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <InputDefault
          type="number"
          fontLabel="12px"
          name="codigoRejeicao"
          placeholder="Informe o Código"
          label="Código de Rejeição"
          isRequired
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <SelectDefault
          label="Modelos do Documento"
          name="modelosDocumento"
          placeholder="Selecione um modelo"
          isMulti
          isRequired
          options={EnumModelosDocumento.map((status) => status)}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <InputDefault
          name="notaTecnica"
          placeholder="Informe a nota"
          label="Nota Técnica"
          isRequired
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <InputDefault
          fontLabel="12px"
          type="date"
          name="dataInicio"
          label="Data início"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <InputDefault
          fontLabel="12px"
          name="dataFim"
          type="date"
          label="Data fim"
        />
      </GridItem>
      <GridItem colSpan={12}>
        <SelectDefault
          label="Estados"
          name="estados"
          placeholder="Selecione um estado"
          isRequired
          options={EnumEstadosSigla.map((status) => status)}
          isMulti
        />
      </GridItem>
      <GridItem colSpan={12}>
        <FormLabel fontSize="xs">Descrição *</FormLabel>
        <TextAreaDefault
          id="descricao"
          name="descricao"
          maxH={['90px', '90px', '140px']}
          bgColor="white"
          placeholder="Informe a descrição."
        />
      </GridItem>
    </SimpleGridForm>
  );
};
