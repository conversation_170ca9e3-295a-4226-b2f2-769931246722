import { useCallback, useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Flex, GridItem, Td, Tr, Text } from '@chakra-ui/react';

import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { moneyMask } from 'helpers/format/fieldsMasks';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';

import { ActionsMenu } from 'components/ActionsMenu';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { LoadingDefault } from 'components/Loading';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { Pagination } from 'components/Grid/Pagination';

import { FaturamentoExibicaoProps } from 'pages/FaturamentoExibicao/validationForm';
import { ModalDetalhesFaturamentoExibicao } from 'components/Modal/ModalDetalhesFaturamentoExibicao';

export const FaturamentoExibicaoDetalhado = () => {
  const { mesAno } = useParams();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [listaFaturamentos, setListaFaturamentos] = useState<FaturamentoExibicaoProps[]>([]);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [page, setPage] = useState(1);
  const [revendaFantasia, setRevendaFantasia] = useState('');

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      // Garantir que o mesAno esteja no formato correto (MM/YYYY)
      // Se o mesAno vier como "052025", transformar para "05/2025"
      const formattedMesAno = mesAno?.length === 6 
        ? `${mesAno.substring(0, 2)}/${mesAno.substring(2)}`
        : mesAno;

      try {
        const response = await api.get<
          void,
          ResponseApi<GridPaginadaRetorno<FaturamentoExibicaoProps>>
        >(
          formatQueryPagedTable(
            ConstantEnderecoWebservice.LISTAR_FATURAMENTOS_EXIBICAO_DETALHADO,
            gridPaginadaConsulta
          ),
          {
            params: {
              mesAno: formattedMesAno,
              revendaId: location.state?.revendaId || null, // Adicionar revendaId como parâmetro
            },
          }
        );

        if (response.sucesso) {
          setTotalRegistros(response.dados.total);
          setListaFaturamentos(response.dados.registros);
          setRevendaFantasia(response.dados.registros[0]?.revendaFantasia || '');
        }
      } catch (error) {
        console.error('Erro ao buscar dados:', error);
      } finally {
        setIsLoading(false);
      }
    },
    [mesAno, location.state?.revendaId] // Adicionar revendaId às dependências
  );

  return (
    <>
      {isLoading && <LoadingDefault />}
      <SimpleGridForm borderRadius="md" py="12px">
        <GridItem colSpan={[12, 12, 12, 12]}>
          <Flex direction="column" gap="10px" w="full">
            <Text color="secondary.500" fontSize="sm">
              {revendaFantasia} - {mesAno?.substring(0, 2)}/{mesAno?.substring(2)} | Total {moneyMask(listaFaturamentos.reduce((total, item) => total + item.faturamentoTotalRepasse, 0), true)}
            </Text>
          </Flex>
        </GridItem>
      </SimpleGridForm>
      <Pagination
        loadColumnsData={paginationHandle}
        nPages={totalRegistros}
        currentPage={page}
        setCurrentPage={setPage}
        isLoading={isLoading}
        defaultOrderDirection="asc"
        defaultKeyOrdered="assinaturaFantasia"
        tableHeaders={[
          {
            content: 'Ações',
            key: 'Acoes',
            isOrderable: false,
            isNumeric: true,
            width: '0.5px',
          },
          {
            content: 'Fantasia',
            key: 'assinaturaFantasia',
            isOrderable: true,
            width: '40%',
          },
          {
            content: 'SubTotal',
            key: 'faturamentoSubTotal',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '15%',
          },
          {
            content: 'Acrésc/Desc',
            key: 'faturamentoAcrescimoDesconto',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '15%',
          },
          {
            content: 'Valor',
            key: 'faturamentoTotalRepasse',
            isOrderable: true,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '15%',
          },
          {
            content: '',
            key: 'espaco',
            isOrderable: false,
            isNumeric: true,
            width: '15%',
          },
        ]}
        renderTableRows={listaFaturamentos.map((faturamento) => (
          <Tr key={faturamento.faturamentoId}>
            <Td isNumeric>
              <Flex justifyContent="flex-end">
                <ActionsMenu
                  id={`mostrarMais-${faturamento.faturamentoId}`}
                  menuZIndex="modal"
                  items={[
                    {
                      content: 'Ver detalhes',
                      onClick: () => {
                        ModalDetalhesFaturamentoExibicao({
                          
                          faturamento: faturamento,
                        });
                      },
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_VER_DETALHES
                      ),
                    },
                  ]}
                />
              </Flex>
            </Td>
            <Td>{faturamento.assinaturaFantasia}</Td>
            <Td isNumeric>
              {moneyMask(faturamento.faturamentoSubTotal || 0, true)}
            </Td>
            <Td isNumeric>
              {moneyMask(faturamento.faturamentoValorAcrescimoDesconto || 0, true)}
            </Td>
            <Td isNumeric>
              {moneyMask(faturamento.faturamentoTotalRepasse, true)}
            </Td>
            <Td />
          </Tr>
        ))}
      />
    </>
  );
};



