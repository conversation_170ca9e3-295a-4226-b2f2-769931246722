import {
  FormLabel,
  FormControl,
  FormErrorMessage,
  useToken,
  Flex,
} from '@chakra-ui/react';
import { toast } from 'react-toastify';
import { Controller, useFormContext } from 'react-hook-form';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  AsyncSelect,
  StylesConfig,
  components,
  ControlProps,
} from 'chakra-react-select';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';

import {
  LoadOptionsCallbackProps,
  SelectDefaultProps,
  SelectOptions,
} from '../Types/validationForms';

export interface CidadesResponse {
  id: number;
  nome: string;
  codigoIBGE: string;
  estadoNome: string;
  estadoSigla: string;
  paisId: number;
}

export interface SelectCidadeProps
  extends Omit<SelectDefaultProps, 'filtrosAtivos' | 'options'> {
  options?: SelectOptions[];
  paisId?: number;
  cidadeNome?: string;
  isDisabled?: boolean;
  isRequired?: boolean;
  isLoading?: boolean;
  getInfoCidade?: (data: CidadesResponse) => void;
}

export const SelectCidade = ({
  name,
  label,
  colorLabel = 'black',
  isDisabled,
  isRequired,
  isLoading = false,
  onSelect,
  component,
  id,
  getInfoCidade,
  isMulti = false,
  isSearchable = false,
  closeMenuOnSelect = true,
  paisId = 1,
  cidadeNome = '',
  options,
  ...rest
}: SelectCidadeProps) => {
  const [isCidadesLoading, setCidadeIsLoading] = useState(false);
  const [defaultOptions, setDefaultOptions] = useState<SelectOptions[]>(
    options || []
  );
  const [valueCidade, setValueCidade] = useState<CidadesResponse>();
  const [upadateValue, setUpdateValue] = useState(false);

  const { setValue, getValues } = useFormContext();

  const currentInputValue = useRef('');
  const setValueRef = useRef(setValue);

  const getCidadeValues = useMemo(() => getValues(`${name}`), []);

  const [gray200, secondary600] = useToken('colors', [
    'gray.200',
    'secondary.600',
  ]);

  const Control = ({ children, ...props }: ControlProps) => {
    return (
      <components.Control {...props}>
        <Flex w="full" fontSize="14px">
          {children}
        </Flex>
      </components.Control>
    );
  };
  const styles: StylesConfig = {
    control: (css, state: { isFocused: boolean; menuIsOpen: boolean }) => {
      return {
        ...css,
        borderRadius: '6px',
        background: 'white',
        minHeight: '35px',
        height: '35px !important',
        border: state?.isFocused
          ? state?.menuIsOpen
            ? `2px solid ${secondary600}`
            : `1px solid ${gray200}`
          : `1px solid ${gray200}`,
        boxShadow: 'none',
        '&:hover': {
          border: `2px solid ${secondary600}`,
          boxShadow: 'none',
        },
      };
    },
    menuPortal: (base: any) => ({
      ...base,
      zIndex: 9999,
    }),
  };

  const getValueCidades = useCallback(
    (valoresCidade: CidadesResponse) => {
      if (getInfoCidade) {
        getInfoCidade(valoresCidade);
      }
    },
    [getInfoCidade]
  );

  const getResponseListarCidade = useCallback(
    async (cidade: string, paisId: number) => {
      const response = await api.get<void, ResponseApi<CidadesResponse[]>>(
        ConstantEnderecoWebservice.CIDADE_LISTAR_SELECT,
        {
          params: {
            nome: cidade,
            paisId: paisId,
          },
        }
      );
      return response;
    },
    []
  );

  const getCidades = useCallback(
    async (cidade = '', paisId = 1) => {
      setCidadeIsLoading(true);

      const response = await getResponseListarCidade(cidade, paisId);

      if (response?.avisos) {
        response.avisos.map((item: string) => toast.warning(item));
      }

      if (response.dados) {
        const { dados } = response;

        if (dados.length === 1) {
          const cidadeUnica = dados.find((option) =>
            option.nome.toLocaleLowerCase().includes(cidade.toLocaleLowerCase())
          );

          setValue(name, {
            value: cidadeUnica?.id,
            label: `${cidadeUnica?.nome} - ${cidadeUnica?.estadoSigla}`,
            ...cidadeUnica,
          });
          getValueCidades(cidadeUnica || ({} as CidadesResponse));
        } else {
          const listaCidades = dados.map((cidade) => {
            return {
              label: `${cidade?.nome} - ${cidade?.estadoSigla}`,
              value: cidade?.id,
              ...cidade,
            };
          });

          const cidadeSelecionada = listaCidades.find((option) =>
            option.label.includes(`${cidade} - ${option.estadoSigla}`)
          );

          if (cidadeSelecionada?.nome === cidade) {
            setValueCidade(cidadeSelecionada);
            setDefaultOptions(listaCidades);
            getValueCidades(cidadeSelecionada || ({} as CidadesResponse));
          }
        }
      }
      setUpdateValue(true);
      setCidadeIsLoading(false);

      return;
    },
    [paisId]
  );

  const loadOptions = (
    inputValue: string,
    callback: LoadOptionsCallbackProps,
    canSearchForce?: boolean
  ) => {
    if (isDisabled) {
      callback([]);
    }
    setTimeout(
      async () => {
        if (currentInputValue.current === inputValue || canSearchForce) {
          const response = await getResponseListarCidade(inputValue, 1);

          if (response?.avisos) {
            response.avisos.forEach((item: string) => toast.warning(item));
          }

          if (response?.sucesso) {
            callback(
              response?.dados.map((cidade: CidadesResponse) => {
                return {
                  label: `${cidade.nome} - ${cidade.estadoSigla}`,
                  value: cidade.id,
                  paisId: cidade.paisId,
                  codigoIBGE: cidade.codigoIBGE,
                  estadoNome: cidade.estadoNome,
                  estadoSigla: cidade.estadoSigla,
                };
              })
            );

            setCidadeIsLoading(false);
            return;
          }
        }

        setCidadeIsLoading(false);
      },
      canSearchForce ? 0 : 500
    );
  };

  useEffect(() => {
    if (paisId === 1) {
      if (getCidadeValues) {
        const valueCidade = getCidadeValues?.label
          ? getCidadeValues?.label
          : getCidadeValues;
        getCidades(valueCidade as string, paisId);

        return;
      }

      getCidades('', paisId);
    } else if (paisId !== 1) {
      getCidades('Exterior', paisId);
    }
  }, [paisId, getCidadeValues]);

  useEffect(() => {
    if (valueCidade) {
      setValueRef.current(name, valueCidade);
    }
  }, [valueCidade, paisId, upadateValue]);

  useEffect(() => {
    setValueRef.current === setValue;
  }, [setValue]);

  return (
    <Controller
      name={name}
      render={({ field: { onChange, name, value }, fieldState: { error } }) => {
        return (
          <FormControl isInvalid={!!error} isRequired={isRequired}>
            {label && (
              <FormLabel mb="8px" fontSize="xs" color={colorLabel}>
                {label}
              </FormLabel>
            )}

            <AsyncSelect
              {...rest}
              isDisabled={isDisabled}
              useBasicStyles
              loadOptions={loadOptions}
              menuPortalTarget={document.body}
              value={value}
              id={id}
              defaultOptions={defaultOptions}
              styles={styles}
              noOptionsMessage={({ inputValue }) =>
                !inputValue
                  ? 'Procure a cidade por nome'
                  : 'Resultado não encontrado'
              }
              selectedOptionStyle="check"
              onChange={(valueOption) => {
                if (valueOption) {
                  onChange(valueOption);
                  if (onSelect) {
                    onSelect(valueOption);
                  }
                }
              }}
              onInputChange={(newValue: string) => {
                currentInputValue.current = newValue;
              }}
              components={{ Control }}
              isMulti={isMulti}
              selectedOptionColor="gray"
              isRequired={isRequired}
              placeholder="Selecione"
              name={name}
              options={options}
              isLoading={isLoading || isCidadesLoading}
            />

            {!!error && <FormErrorMessage>{error.message}</FormErrorMessage>}
          </FormControl>
        );
      }}
    />
  );
};
