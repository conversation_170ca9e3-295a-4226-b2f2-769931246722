import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FiSearch } from 'react-icons/fi';
import { Box, Flex, Td, Tr, Icon, Link } from '@chakra-ui/react';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { EnumStatusCadastros } from 'constants/Enum/enumStatusCadastros';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';

import { ActionsMenu } from 'components/ActionsMenu';
import { ButtonDefault } from 'components/Button';
import { ContainerListagem } from 'components/Grid/GridList';
import { Pagination } from 'components/Grid/Pagination';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SelectDefault } from 'components/Select/SelectDefault';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';
import { LoadingDefault } from 'components/Loading';

type ProdutosProps = {
  id: string;
  ativo: boolean;
  nome: string;
  identificadorUrl: string;
};

type ProdutoAtivoProps = {
  label: string;
  value: boolean | null;
};

type FormData = {
  nome: string;
  ativo: ProdutoAtivoProps;
};

export const Produtos = () => {
  const [page, setPage] = useState(1);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [listaProdutos, setListaProdutos] = useState<ProdutosProps[]>([]);
  const [currentFilters, setCurrentFilters] = useState<FormData>(
    {} as FormData
  );

  const formMethods = useForm<FormData>({
    defaultValues: {
      ativo: EnumStatusCadastros.TODOS,
      nome: '',
    },
  });
  const { watch } = formMethods;
  const nomeProdutoWatch = watch('nome');

  const navigate = useNavigate();

  const possuiPermissaoAlterarProduto = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.CadastroProdutoAcao.ALTERAR_PRODUTOS
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<ProdutosProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_PRODUTOS,
          gridPaginadaConsulta
        ),
        {
          params: currentFilters,
        }
      );

      if (response.sucesso) {
        setTotalRegistros(response.dados.total);
        setListaProdutos(response.dados.registros);
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const handleExcluirProduto = useCallback(
    async (id: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'A ação irá excluir este produto!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            ConstantEnderecoWebservice.EXCLUIR_PRODUTO,
            { params: { produtoId: id } }
          );

          if (response.sucesso) {
            setRecarregarListagem(!recarregarListagem);
            toast.success('Cadastro excluído com sucesso.');
            return true;
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );

  const handleAlterarProduto = useCallback(
    (produtoId: string) => {
      if (!possuiPermissaoAlterarProduto) return;
      navigate(
        SubstituirParametroRota(
          ConstanteRotas.PRODUTOS_ALTERAR,
          'id',
          produtoId
        )
      );
    },
    [possuiPermissaoAlterarProduto]
  );

  return (
    <>
      {isLoading && <LoadingDefault />}
      <Box>
        <FormProvider {...formMethods}>
          <ContainerListagem
            inputPesquisa={
              <InputDefault
                maxLength={100}
                placeholder="Buscar por nome"
                iconLeftElement={FiSearch}
                name="nome"
                onEnterKeyPress={() => {
                  setCurrentFilters((filtrosJaAdicionados) => ({
                    ...filtrosJaAdicionados,
                    nome: nomeProdutoWatch,
                  }));
                }}
              />
            }
            filtrosListagem={
              <SelectDefault
                name="ativo"
                placeholder="Selecione um filtro"
                filtrosAtivos
                onSelect={(optionSelecionada) =>
                  setCurrentFilters((filtrosJaAdicionados) => ({
                    ...filtrosJaAdicionados,
                    ativo: optionSelecionada?.value,
                  }))
                }
                options={EnumStatusCadastros.properties.map((status) => status)}
              />
            }
            buttonCadastrar={
              <ButtonDefault
                onClick={() => navigate(ConstanteRotas.PRODUTOS_CADASTRAR)}
                width={['full', 'full', 'full', '220px']}
                color="white"
                colorScheme="secondary"
                leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
                possuiFuncionalidade={auth.usuarioPossuiPermissao(
                  ConstantFuncionalidades.CadastroProdutoAcao.CADASTRAR_PRODUTOS
                )}
              >
                Cadastrar novo
              </ButtonDefault>
            }
          >
            <Pagination
              loadColumnsData={paginationHandle}
              nPages={totalRegistros}
              currentPage={page}
              setCurrentPage={setPage}
              isLoading={isLoading}
              defaultOrderDirection="asc"
              defaultKeyOrdered="ativo"
              tableHeaders={[
                {
                  content: <StatusCircle hasValue={false} />,
                  key: 'ativo',
                  width: '1px',
                  isOrderable: false,
                },
                {
                  content: 'Nome',
                  key: 'nome',
                  width: 'auto',
                  isOrderable: true,
                },
                {
                  content: 'Identificador',
                  key: 'identificadorUrl',
                  width: 'auto',
                  isOrderable: false,
                },
                {
                  content: 'Ações',
                  key: 'Acoes',
                  isOrderable: false,
                  width: '10px',
                },
              ]}
              renderTableRows={listaProdutos.map((produto) => (
                <Tr key={produto.id}>
                  <Td>
                    <StatusCircle isActive={produto.ativo} />
                  </Td>
                  <Td pb="3px" pt="3px">
                    <Flex w="full">
                      <Link
                        lineHeight="12.5px"
                        maxW="full"
                        whiteSpace="pre-line"
                        cursor="pointer"
                        onClick={() => handleAlterarProduto(produto.id)}
                      >
                        {produto.nome}
                      </Link>
                    </Flex>
                  </Td>
                  <Td>{produto.identificadorUrl}</Td>
                  <Td>
                    <Flex justifyContent="right">
                      <ActionsMenu
                        id="produtoAcoes"
                        items={[
                          {
                            content: 'Editar',
                            onClick: () => {
                              handleAlterarProduto(produto.id);
                            },
                            possuiFuncionalidade: auth.usuarioPossuiPermissao(
                              ConstantFuncionalidades.CadastroProdutoAcao
                                .ALTERAR_PRODUTOS
                            ),
                          },
                          {
                            content: 'Excluir',
                            onClick: () => {
                              handleExcluirProduto(produto.id);
                            },
                            possuiFuncionalidade: auth.usuarioPossuiPermissao(
                              ConstantFuncionalidades.CadastroProdutoAcao
                                .EXCLUIR_PRODUTOS
                            ),
                          },
                        ]}
                      />
                    </Flex>
                  </Td>
                </Tr>
              ))}
            />
          </ContainerListagem>
        </FormProvider>
      </Box>
    </>
  );
};
