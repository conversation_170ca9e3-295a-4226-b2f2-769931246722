import {
  Flex,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalProps,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';

import { ButtonDefault } from 'components/Button';
import { LoadingDefault } from 'components/Loading';
import { TextAreaDefault } from 'components/TextArea';
import { ModalDefaultChakra } from 'components/Modal/ModalDefaultChakra';

type ModalAdicionarDescricaoProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> & {
  servicoIdTabelaPreco: string;
  isEditable?: string;
  handleAdicionarDescricao: (idServico: string, descricao: string) => void;
} & InstanceProps<ModalProps>;

type FormData = {
  descricao: string;
};

export const ModalAdicionarDescricao = create<ModalAdicionarDescricaoProps>(
  ({ servicoIdTabelaPreco, isEditable, handleAdicionarDescricao, ...rest }) => {
    const [isLoading, setIsLoading] = useState(false);

    const formDefaultValues = {
      descricao: isEditable ? isEditable : '',
    };

    const formMethods = useForm<FormData>({
      defaultValues: formDefaultValues,
    });

    const { handleSubmit } = formMethods;

    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const handleAdicionar = handleSubmit((data) => {
      setIsLoading(true);

      const { descricao } = data;
      if (!descricao.replace(/\s/g, '').length) {
        handleAdicionarDescricao(servicoIdTabelaPreco, '');
      } else {
        handleAdicionarDescricao(servicoIdTabelaPreco, descricao);
      }
      onClose();
      setIsLoading(false);
    });

    return (
      <ModalDefaultChakra
        {...rest}
        isCentered
        size={['full', 'full', '2xl']}
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <FormProvider {...formMethods}>
          <ModalContent
            bg="gray.50"
            w={['full', 'full', 'full']}
            h={['full', '400px']}
          >
            {isLoading && <LoadingDefault />}
            <ModalHeader borderTopRadius="5px" bg="primary.600" pb="15px">
              <Flex
                color="white"
                pt="5px"
                justifyContent="space-between"
                alignItems="center"
              >
                <Text pl="2px">
                  {isEditable ? 'Editar' : 'Adicionar'} descrição
                </Text>
                <ModalCloseButton mt="13px" mr="10px" />
              </Flex>
            </ModalHeader>
            <ModalBody pt="8">
              <TextAreaDefault
                name="descricao"
                id="descricao"
                label="Descrição"
                minHeight="36"
              />
            </ModalBody>
            <ModalFooter>
              <Flex
                w="full"
                justifyContent={['center', 'center', 'right']}
                flexDirection={['column', 'row', 'row']}
              >
                <ButtonDefault
                  width={['full', '120px', '120px', '120px']}
                  colorScheme="gray"
                  mr="20px"
                  mb={['20px', 'undefined', 'undefined']}
                  variant="outlinePill"
                  onClick={() => onClose()}
                  possuiFuncionalidade={true}
                >
                  Cancelar
                </ButtonDefault>
                <ButtonDefault
                  width={['full', '120px', '120px', '120px']}
                  color="white"
                  colorScheme="secondary"
                  onClick={() => handleAdicionar()}
                  possuiFuncionalidade={true}
                >
                  Salvar
                </ButtonDefault>
              </Flex>
            </ModalFooter>
          </ModalContent>
        </FormProvider>
      </ModalDefaultChakra>
    );
  }
);
