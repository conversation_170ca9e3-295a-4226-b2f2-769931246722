import React, { ChangeEvent } from 'react';
import { InputProps } from '@chakra-ui/react';
import { Controller } from 'react-hook-form';

import { telefoneMask } from 'helpers/format/fieldsMasks';

import { InputChakra } from '..';

export const getFormattedValue = (value: string) => {
  return telefoneMask(value);
};

export interface InputPhoneProps extends InputProps {
  name: string;
  label?: string;
  colorLabel?: string;
}

export const InputPhone = ({
  name,
  label,
  colorLabel,
  ...rest
}: InputPhoneProps) => {
  return (
    <Controller
      name={name}
      render={({
        field: { onChange, onBlur, value, name },
        fieldState: { error },
      }) => {
        return (
          <InputChakra
            {...rest}
            label={label}
            colorLabel={colorLabel}
            error={error}
            value={value}
            name={name}
            onBlur={onBlur}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              e.currentTarget.value = getFormattedValue(e.currentTarget.value);

              if (onChange) onChange(e);
            }}
            onInput={(e: ChangeEvent<HTMLInputElement>) => {
              e.currentTarget.value = getFormattedValue(e.currentTarget.value);
            }}
            maxLength={15}
            {...rest}
          />
        );
      }}
    />
  );
};
