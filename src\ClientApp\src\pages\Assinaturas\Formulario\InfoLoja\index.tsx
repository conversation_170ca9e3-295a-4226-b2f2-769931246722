import { useCallback, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { GridItem } from '@chakra-ui/react';
import { toast } from 'react-toastify';

import { formatValuePais } from 'helpers/format/formatValuePais';
import { CepResponse } from 'services/viacep';
import { useAssinaturasContext } from 'store/Assinaturas/AssinaturaContext';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';

import { listarSelectEstado } from 'api/Estado/ListarSelect';
import { listarSelectCidade } from 'api/Cidade';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { InputCep } from 'components/Input/InputChakra/InputCep';
import {
  CnpjResponse,
  InputCpfCnpj,
} from 'components/Input/InputChakra/InputCpfCnpj';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { InputPhone } from 'components/Input/InputChakra/InputPhone';
import { SelectCidade, CidadesResponse } from 'components/Select/SelectCidade';
import { paisDefault, SelectPais } from 'components/Select/SelectPais';
import {
  SelectDefault,
  SelectNameOptions,
  SelectOptions,
} from 'components/Select/SelectDefault';
import { InputRg } from 'components/Input/InputChakra/InputRg';
import { SwitchDefault } from 'components/Switch';
import { AsyncSelectDefault } from 'components/Select/AsyncSelectDefault';
import { InputNumber } from 'components/Input/InputChakra/InputNumber';

type InfoLojaProps = {
  isModal?: boolean;
};

const BRASIL_PAIS_ID = 1;

export const InfoLoja = ({ isModal }: InfoLojaProps) => {
  const [listAssinatura, setListAssinatura] = useState<SelectOptions[]>([]);

  const { watch, setValue } = useFormContext();
  const { isCriarNovaLoja, contaCliente } = useAssinaturasContext();

  const [paisNomeWatch, cnpjWatch] = watch([
    'paisNome',
    'cnpj',
  ]);

  const isCnpj = cnpjWatch?.length > 14;

  const valuePais = formatValuePais(paisNomeWatch);

  const exibirPrecoLoja = isCriarNovaLoja && listAssinatura.length > 1;

  const obterEstado = async (
    estadoNome: string,
    siglaUf: string,
    paisId: number | null
  ) => {
    const response = await listarSelectEstado({
      nome: estadoNome,
      paisId: paisId,
    });

    if (response?.avisos)
      response.avisos.forEach((aviso) => toast.warning(aviso));

    if (siglaUf) {
      return response.dados.find(({ sigla }) => sigla === siglaUf);
    }

    return response.dados[0];
  };

  const obterCidade = async (
    cidadeNome: string,
    codigoIbge: string,
    paisId: number | null
  ) => {
    const response = await listarSelectCidade({
      nome: cidadeNome,
      paisId: paisId,
    });

    if (response?.avisos) {
      response.avisos.forEach((item: string) => toast.warning(item));
    }

    if (codigoIbge) {
      return response.dados.find(({ codigoIBGE }) => codigoIBGE === codigoIbge);
    }

    return response.dados[0];
  };

  const getInfoCep = useCallback(
    async (data: CepResponse) => {
      const estado = await obterEstado(data.estado, data.uf, BRASIL_PAIS_ID);
      const cidade = await obterCidade(
        data.localidade,
        String(data.ibge),
        BRASIL_PAIS_ID
      );

      setValue('bairro', data?.bairro);
      setValue('complemento', data?.complemento);
      setValue('logradouro', data?.logradouro);
      setValue('cidadeNome', {
        label: `${cidade?.nome} - ${cidade?.estadoSigla}`,
        value: cidade?.id,
        ...cidade,
      });
      setValue('estado', { label: estado?.sigla, value: estado?.sigla });
      setValue('paisNome', paisDefault);
    },
    [setValue, paisDefault]
  );

  const getInfoCidade = useCallback(
    (data: CidadesResponse) => {
      setValue('estado', {
        label: data?.estadoSigla,
        value: data?.estadoSigla,
      });
    },
    [setValue, paisDefault]
  );

  const handleUpdateValuesLoja = useCallback(
    (valuesCnpj: CnpjResponse) => {
      setValue('razaoSocial', valuesCnpj.nome);
      setValue('fantasia', valuesCnpj.fantasia);
      setValue('cep', valuesCnpj.cep);
      setValue('logradouro', valuesCnpj.logradouro);
      setValue('numero', valuesCnpj.numero);
      setValue('complemento', valuesCnpj.complemento);
      setValue('bairro', valuesCnpj.bairro);
      setValue('telefone', valuesCnpj.telefone);
      setValue('cidadeNome', valuesCnpj.municipio);
      setValue('estado', valuesCnpj?.uf);
      setValue('paisNome', paisDefault);
    },
    [setValue]
  );

  const getPrecoLoja = useCallback(async () => {
    const response = await api.get<void, ResponseApi<SelectNameOptions[]>>(
      ConstantEnderecoWebservice.LISTAR_ASSINATURAS,
      {
        params: {
          contaClienteId: contaCliente,
        },
      }
    );

    if (response.sucesso) {
      const { dados } = response;
      const valueDados = dados.map((dataItem) => ({
        ...dataItem,
        value: dataItem.id,
        label: dataItem.nome,
      }));

      if (valueDados.length === 1) {
        setValue('idAssinaturaBasePrecos', valueDados[0].value);
      } else {
        setListAssinatura(valueDados);
      }
    }
  }, [contaCliente]);

  const obterOpcoesEstados = async (valor: string) => {
    const response = await listarSelectEstado({
      nome: valor,
      paisId: valuePais,
    });

    if (response?.avisos)
      response.avisos.forEach((aviso) => toast.warning(aviso));

    if (response?.sucesso) {
      return response.dados.map(({ sigla }) => ({
        value: sigla,
        label: sigla,
      }));
    }

    return [];
  };

  const handleAlterarPais = async (paisSelecionado: { id: number }) => {
    if (paisSelecionado.id !== BRASIL_PAIS_ID) {
      const estado = await obterEstado('Exterior', '', null);
      const cidade = await obterCidade('', '', 2);

      setValue('cidadeNome', {
        label: `${cidade?.nome} - ${cidade?.estadoSigla}`,
        value: cidade?.id,
        ...cidade,
      });
      setValue('estado', {
        label: estado?.sigla,
        value: estado?.sigla,
      });
    }
  };

  useEffect(() => {
    if (isCriarNovaLoja) {
      getPrecoLoja();
    }
  }, [getPrecoLoja, isCriarNovaLoja]);

  return (
    <SimpleGridForm>
      <GridItem colSpan={[12, 12, 3]}>
        <InputCpfCnpj
          label="CPF/CNPJ"
          isRequired
          maxLength={18}
          colorLabel="gray.600"
          placeholder="Informe o CNPJ"
          name="cnpj"
          getCnpjData={(valuesCnpj) => handleUpdateValuesLoja(valuesCnpj)}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 9]}>
        <InputDefault
          label={isCnpj ? 'Razão social' : 'Nome'}
          maxLength={60}
          colorLabel="gray.600"
          placeholder="Informe a razão social"
          name="razaoSocial"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 6]}>
        <InputDefault
          label={isCnpj ? 'Nome fantasia' : 'Apelido'}
          maxLength={60}
          colorLabel="gray.600"
          placeholder="Informe o nome fantasia"
          name="fantasia"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 3]}>
        <InputRg
          label={isCnpj ? 'Inscrição estadual' : undefined}
          placeholder={isCnpj ? 'Informe a inscrição estadual' : undefined}
          colorLabel="gray.600"
          maxLength={20}
          isCnpj={isCnpj}
          name="inscricaoEstadual"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 3]}>
        <InputDefault
          label="Inscrição municipal"
          colorLabel="gray.600"
          maxLength={20}
          type="number"
          placeholder="Informe a inscrição municipal"
          name="inscricaoMunicipal"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 3, 2]}>
        <InputCep
          name="cep"
          maxLength={9}
          paisId={valuePais}
          placeholder="Informe o cep"
          label="Cep"
          isRequired
          getInfoCep={(valuesCep) => getInfoCep(valuesCep)}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 7, 8]}>
        <InputDefault
          name="logradouro"
          maxLength={80}
          placeholder="Informe o nome da rua"
          label="Logradouro"
          isRequired
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 2]}>
        <InputDefault
          name="numero"
          placeholder="Informe o número"
          label="Número"
          maxLength={60}
          isRequired
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4, 2, 2]}>
        <InputDefault
          name="complemento"
          placeholder="Informe um complemento"
          label="Complemento"
          maxLength={60}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 8, 2]}>
        <InputDefault
          name="bairro"
          maxLength={60}
          placeholder="Informe o nome do bairro"
          label="Bairro"
          isRequired
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 5, 4, 3]}>
        <SelectCidade
          name="cidadeNome"
          placeholder="Informe o nome da cidade"
          label="Cidade"
          isRequired
          getInfoCidade={(dataCidade) => getInfoCidade(dataCidade)}
          isSearchable
          paisId={valuePais}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 3, 3, 2]}>
        <AsyncSelectDefault
          name="estado"
          label="Estado"
          loadOptions={obterOpcoesEstados}
          isDisabled={!paisNomeWatch || valuePais !== BRASIL_PAIS_ID}
          defaultOptions
          isRequired
          isSearchable
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 5, 4, 3]}>
        <SelectPais
          name="paisNome"
          label="País"
          onSelect={(paisSelecionado) => handleAlterarPais(paisSelecionado)}
          isRequired
          isSearchable
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 6, 4, 2]}>
        <InputPhone
          name="celular"
          maxLength={15}
          isRequired
          placeholder="Informe o celular"
          label="Celular"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 6, 4, 2]}>
        <InputPhone
          name="telefone"
          maxLength={15}
          isRequired
          placeholder="Informe o telefone"
          label="Telefone"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 6, 2]}>
        <InputNumber
          name="codigoERP"
          label="Código ERP"
          precision={9}
          scale={0}
          placeholder="Informe o código erp"
          textAlign="left"
          pl="4"
        />
      </GridItem>
      {!isModal && (
        <GridItem colSpan={[12, 12, 6, 3]}>
          <InputDefault
            name="codigoExterno"
            type="number"
            maxLength={60}
            placeholder="Informe o código externo"
            label="Código externo"
          />
        </GridItem>
      )}
      <GridItem colSpan={[12, 12, 6, 6, 3]}>
        <InputDefault
          name="emailContato"
          type="email"
          maxLength={80}
          placeholder="Informe o e-mail para contato"
          label="E-mail contato"
          isRequired
        />
      </GridItem>
      <GridItem colSpan={[12, 12, exibirPrecoLoja ? 3 : 5]}>
        <InputDefault
          name="contato"
          isRequired
          max={80}
          placeholder="Informe o contato"
          label="Contato"
        />
      </GridItem>
      {exibirPrecoLoja && (
        <GridItem colSpan={[12, 12, 3]}>
          <SelectDefault
            name="idAssinaturaBasePrecos"
            isRequired
            label="Usar o mesmo preço da loja:"
            options={listAssinatura}
            asControlledByObject={false}
          />
        </GridItem>
      )}
    </SimpleGridForm>
  );
};
