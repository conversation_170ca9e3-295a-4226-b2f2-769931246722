import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import { LayoutFormPage } from 'components/Layout/FormPage';

import FormCategoriaTreinamento from '..';
import { FormData, formDefaultValues, yupResolver } from '../validationForm';

const AlterarCategoriaTreinamento = () => {
  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, reset } = formMethods;

  const { id: idRota } = useParams();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(false);

  const getCategoria = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<FormData>>(
      `${ConstantEnderecoWebservice.OBTER_CATEGORIA}/${idRota}`
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso && response.dados) {
        reset(response.dados);
      }
    }
    setIsLoading(false);
  }, [idRota]);

  const handleAlterarCategoria = useCallback(
    handleSubmit(async (data) => {
      setIsLoading(true);
      const response = await api.put<void, ResponseApi<FormData>>(
        `${ConstantEnderecoWebservice.ALTERAR_CATEGORIA}`,
        {
          ...data,
          sequenciaOrdenacao: data.sequenciaOrdenacao,
          id: idRota,
        }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        if (response.sucesso) {
          toast.success('Cadastro alterado com sucesso');
          navigate(ConstanteRotas.CATEGORIAS_TREINAMENTO);
        }
      }
      setIsLoading(false);
    }),
    [idRota]
  );

  useEffect(() => {
    getCategoria();
  }, [getCategoria]);

  return (
    <LayoutFormPage
      onSubmit={() => handleAlterarCategoria()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormCategoriaTreinamento />
      </FormProvider>
    </LayoutFormPage>
  );
};

export default AlterarCategoriaTreinamento;
