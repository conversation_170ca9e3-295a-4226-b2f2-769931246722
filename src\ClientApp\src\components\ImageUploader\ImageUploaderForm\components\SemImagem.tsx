import { Flex, Icon, Box, Text } from '@chakra-ui/react';
import { ControllerRenderProps, FieldValues } from 'react-hook-form';
import { RiImageAddFill } from 'react-icons/ri';

type SemImagemProps = {
  inputFileRef: React.RefObject<HTMLInputElement>;
  field: ControllerRenderProps<FieldValues, string>;
  sizeIcon?: string;
  handleFile: (file: File, onChange: (value: string | null) => void) => void;
  handleDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
};

export const SemImagem = ({
  handleFile,
  sizeIcon,
  inputFileRef,
  field,
  handleDragOver,
}: SemImagemProps) => {
  return (
    <Flex
      onDrop={(e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();

        const file = e.dataTransfer.files[0];

        handleFile(file, field.onChange);
      }}
      onDragOver={handleDragOver}
      borderColor={'gray.200'}
      borderWidth={'1px'}
      borderRadius={'md'}
    >
      <Flex
        justifyContent="center"
        cursor="pointer"
        onClick={() => {
          if (inputFileRef?.current?.click) {
            inputFileRef.current.click();
          }
        }}
      >
        <Box>
          <Flex mb="3px" justifyContent="center" alignItems="center">
            <Icon fontSize={sizeIcon} as={RiImageAddFill} />
          </Flex>

          <Flex justifyContent="center" alignItems="center">
            <Text w="60%" fontSize="14px" textAlign="center" lineHeight="15px">
              Arraste e solte seu arquivo aqui
            </Text>
          </Flex>

          <Flex justifyContent="center" alignItems="center">
            <Text
              w="140px"
              mt="10%"
              textAlign="center"
              color="violet.500"
              fontSize="14px"
              lineHeight="15px"
            >
              Escolher arquivo
            </Text>
          </Flex>
        </Box>
      </Flex>
    </Flex>
  );
};
