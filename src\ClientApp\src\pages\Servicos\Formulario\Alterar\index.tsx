import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import { enumTipoServico } from 'constants/Enum/enumTipoServicos';
import { useEffectDefault } from 'hook/useEffectDefault';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormCadastrarServicos } from '..';
import { formDefaultValues, FormData, yupResolver } from '../validationForms';

type ServicossObterResponse = {
  id: string;
  nome: string;
  diasExpirar: 0;
  tipo: number;
  referencia: number;
  quantitativo: boolean;
  ativo: boolean;
};

export const AlterarServicos = () => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });

  const { handleSubmit, reset } = formMethods;

  const navigate = useNavigate();
  const idRouter = useParams();
  const { id: idRota } = idRouter;

  const getServicos = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<ServicossObterResponse>>(
      ConstantEnderecoWebservice.OBTER_SERVICO,
      {
        params: { id: idRota },
      }
    );

    if (response.sucesso) {
      const { dados } = response;
      const tipoServicoEscolhido = enumTipoServico.options.find(
        (servico: { value: number }) => servico.value === dados.tipo
      );

      const referenciaEscolhida = tipoServicoEscolhido
        ? enumTipoServico.options[tipoServicoEscolhido.value].properties.find(
            (referencia: { value: number }) =>
              referencia.value === dados.referencia
          )
        : null;

      reset({
        ...dados,
        tipo: tipoServicoEscolhido,
        referencia: referenciaEscolhida,
      });
    } else {
      navigate(ConstanteRotas.SERVICOS);
    }

    setIsLoading(false);
  }, [reset, idRota]);

  const handleAlterarServicos = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await api.put<void, ResponseApi<ServicossObterResponse>>(
      ConstantEnderecoWebservice.ALTERAR_SERVICO,
      {
        ...data,
        tipo: data.tipo?.value,
        referencia: data.referencia?.value,
        id: idRota,
      }
    );

    if (response.sucesso) {
      toast.success('Cadastro alterado com sucesso.');
      navigate(ConstanteRotas.SERVICOS);
    }

    setIsLoading(false);
  });

  useEffectDefault(() => {
    getServicos();
  }, [getServicos]);

  return (
    <LayoutFormPage
      onSubmit={() => handleAlterarServicos()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormCadastrarServicos />
      </FormProvider>
    </LayoutFormPage>
  );
};
