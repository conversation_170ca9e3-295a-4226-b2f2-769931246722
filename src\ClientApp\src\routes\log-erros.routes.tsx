import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { LogErros } from 'pages/LogErros';

import LayoutGuard from './LayoutGuard';

export const LogErrosRoutes = [
  <Route
    key={ConstanteRotas.LOG_ERROS}
    path={ConstanteRotas.LOG_ERROS}
    element={
      <LayoutGuard
        key={ConstanteRotas.LOG_ERROS}
        breadcrumb={[{ title: 'Log de erros' }]}
        component={<LogErros />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_LOG)}
      />
    }
  />,
];
