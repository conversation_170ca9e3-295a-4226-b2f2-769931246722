import { useState } from 'react';
import {
  Flex,
  Text,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
} from '@chakra-ui/react';

import { LoadingDefault } from 'components/Loading';
import { ModalDefaultChakra } from 'components/Modal/ModalDefaultChakra';
import { Calendario } from 'components/Calendario';

interface DateSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDateSelect: (date: Date | null) => void;
}

const DateSelectionModal: React.FC<DateSelectionModalProps> = ({ isOpen, onClose, onDateSelect }) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  const handleConfirm = () => {
    onDateSelect(selectedDate);
    onClose();
  };

  return (
    <ModalDefaultChakra
      isCentered
      size='6xl'
      isOpen={isOpen}
      onClose={onClose}
      autoFocus={false}
    >
      <ModalContent
        h={['full', 'fit-content']}
        maxH="full"
        w={['full', '5xl', 'lg']}
        bg="gray.50"
      >
        <LoadingDefault />
        <ModalHeader
          borderTopRadius="5px"
          bg="primary.500"
          pb="15px"
          borderBottom="1px solid"
          borderColor="gray.50"
        >
          <Flex
            color="white"
            pt="5px"
            justifyContent="space-between"
            alignItems="center"
          >
            <Text pl="2px" color="white">
              Selecione a data da baixa
            </Text>
            <ModalCloseButton
              id="closeButton"
              mt="13px"
              mr="10px"
              color="gray.50"
            />
          </Flex>
        </ModalHeader>
        <ModalBody backgroundColor="white" p="0">
          <Flex alignItems="center" justifyContent="center" padding="24px">
            <Calendario
              name="dataBaixa"
              secondaryStyle
              onConfirmButton={() => handleConfirm()}
              onCancelButton={() => onClose()}
              onConfirmButtonText="Baixar"
            />
          </Flex>
        </ModalBody>
      </ModalContent>
    </ModalDefaultChakra>
  );
}
