import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

const schema = yup.object().shape({
  valor: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  motivo: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

export const formDefaultValues = {
  valor: '',
  motivo: ''
};

export const yupResolver = yupResolverInstance(schema);