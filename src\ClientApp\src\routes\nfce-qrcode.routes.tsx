import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { NfceQrCodeListar } from 'pages/NfceQrCode/Listar';
import { CadastrarNfceQrCode } from 'pages/NfceQrCode/Formulario/Cadastrar';
import { AlterarNfceQrCode } from 'pages/NfceQrCode/Formulario/Alterar';

import LayoutGuard from './LayoutGuard';

export const NfceQrCodeRoutes = [
  <Route
    key={ConstanteRotas.NFCE_QRCODE}
    path={ConstanteRotas.NFCE_QRCODE}
    element={
      <LayoutGuard
        key={ConstanteRotas.NFCE_QRCODE}
        breadcrumb={[{ title: 'Fiscal' }, { title: 'NFc-e QrCode' }]}
        component={<NfceQrCodeListar />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_FISCAL_NFCE)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.NFCE_QRCODE_CADASTRAR}
    path={ConstanteRotas.NFCE_QRCODE_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.NFCE_QRCODE_CADASTRAR}
        breadcrumb={[
          { title: 'Fiscal' },
          { title: 'NFc-e QrCode', path: ConstanteRotas.NFCE_QRCODE },
          { title: 'Cadastrar' },
        ]}
        component={<CadastrarNfceQrCode />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.FicalNFCeAcao.CADASTRAR_NFCE)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.NFCE_QRCODE_ALTERAR}
    path={ConstanteRotas.NFCE_QRCODE_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.NFCE_QRCODE_ALTERAR}
        breadcrumb={[
          { title: 'Fiscal' },
          { title: 'NFc-e QrCode', path: ConstanteRotas.NFCE_QRCODE },
          { title: 'Alterar' },
        ]}
        component={<AlterarNfceQrCode />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.FicalNFCeAcao.ALTERAR_NFCE)}
      />
    }
  />,
];
