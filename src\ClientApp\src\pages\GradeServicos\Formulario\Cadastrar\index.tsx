import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import api, { ResponseApi } from 'services/api';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormGradeServicos } from '..';
import {
  FormData,
  formDefaultValues,
  ServicoProps,
  yupResolver,
} from '../validationForm';

export const FormCadastrarGrade = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [listaServicos, setListaServicos] = useState<ServicoProps[]>([]);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });

  const { handleSubmit, reset } = formMethods;

  const navigate = useNavigate();

  const gradesSelecionadas = listaServicos.filter(
    (servicos) => servicos.servicoEstaAdicionado
  );

  const getResponseCadastroGrade = useCallback(
    async (data: FormData) => {
      const response = await api.post<void, ResponseApi<FormData>>(
        ConstantEnderecoWebservice.CADASTRAR_GRADE_SERVICOS,
        {
          ativo: data.ativo,
          nome: data.nome,
          produtoId: data.produtoId?.value,
          gradeItem: gradesSelecionadas.map((grade) => {
            const valorRepasseServico = data?.servicosGrade[grade.servicoId];

            return {
              servicoId: grade.servicoId,
              valorRepasse: valorRepasseServico,
            };
          }),
        }
      );
      return response;
    },
    [gradesSelecionadas]
  );

  const handleCadastrarGrade = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await getResponseCadastroGrade(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      navigate(ConstanteRotas.GRADE_SERVICOS);
    }

    setIsLoading(false);
  });

  const handleResetGrade = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await getResponseCadastroGrade(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      reset(formDefaultValues);
    }

    setListaServicos((listaServicos) => {
      const newListaServicos = listaServicos.map((valor) => {
        return {
          ...valor,
          servicoEstaAdicionado: false,
        };
      });

      return newListaServicos;
    });

    setIsLoading(false);
  });

  return (
    <LayoutFormPage
      onSubmit={() => handleCadastrarGrade()}
      onResetSubmit={() => handleResetGrade()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormGradeServicos
          listaServicos={listaServicos}
          setListaServicos={setListaServicos}
        />
      </FormProvider>
    </LayoutFormPage>
  );
};
