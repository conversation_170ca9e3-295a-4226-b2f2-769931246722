import { ChakraProvider } from '@chakra-ui/provider';
import { Flex } from '@chakra-ui/react';

import { formatDateCompleto } from 'helpers/format/formatStringDate';
import { capitalize } from 'helpers/format/stringFormats';
import { hexToRgbA } from 'store/getHexDecimalColor';
import { theme } from 'theme';

import { Calendario } from './exampleCalendario';

const isSecondaryStyle = false;
const textoBotaoCancelar = 'Cancelar';
const textoBotaoConfirmar = 'Baixar';
const dataInicial = new Date();
const primaryColor = '#636567';
const secondaryColor = '#38B7BF';

describe('Testing component Calendario', () => {
  beforeEach(() => {
    cy.mount(
      <ChakraProvider theme={theme}>
        <Flex alignItems="center" justifyContent="center" pt="12px">
          <Calendario
            name="dataTest"
            dataInicial={dataInicial}
            secondaryStyle={isSecondaryStyle}
            onCancelButtonText={textoBotaoCancelar}
            onConfirmButtonText={textoBotaoConfirmar}
            onConfirmButton={() => {}}
            onCancelButton={() => {}}
          />
        </Flex>
      </ChakraProvider>
    );
  });

  it('renders correct text for both buttons', () => {
    cy.contains(textoBotaoCancelar);
    cy.contains(textoBotaoConfirmar);
  });

  it('renders correct color in primary style for both buttons', () => {
    cy.contains('button', textoBotaoConfirmar)
      .should('have.css', 'background-color')
      .and('eq', hexToRgbA(primaryColor));

    cy.contains('button', textoBotaoCancelar)
      .should('have.css', 'background-color')
      .and('eq', hexToRgbA('#FFF'));
  });

  it('renders correct color in secondary style for both buttons', () => {
    cy.mount(
      <ChakraProvider theme={theme}>
        <Flex alignItems="center" justifyContent="center" pt="12px">
          <Calendario
            name="dataTest"
            dataInicial={dataInicial}
            secondaryStyle={!isSecondaryStyle}
            onCancelButtonText={textoBotaoCancelar}
            onConfirmButtonText={textoBotaoConfirmar}
            onConfirmButton={() => {}}
            onCancelButton={() => {}}
          />
        </Flex>
      </ChakraProvider>
    );
    cy.contains('button', textoBotaoConfirmar)
      .should('have.css', 'background-color')
      .and('eq', hexToRgbA(secondaryColor));

    cy.contains('button', textoBotaoCancelar)
      .should('have.css', 'background-color')
      .and('eq', hexToRgbA('#FFF'));
  });

  it('renders correct actual date', () => {
    const anoAtual = new Date().getFullYear();
    const dataCompleta = formatDateCompleto(dataInicial);

    cy.contains('p', anoAtual);
    cy.contains('p', capitalize(dataCompleta));
  });
});
