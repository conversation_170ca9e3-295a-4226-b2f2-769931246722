import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

import { SelectOptions } from 'components/Select/SelectDefault';

export type FormData = {
  codigoRegra: string;
  codigoRejeicao: string;
  notaTecnica: string;
  estados: SelectOptions[] | null;
  modelosDocumento: SelectOptions[] | null;
  dataInicio: Date | null | string;
  dataFim: Date | null | string;
  descricao: string;
};

export const formDefaultValues = {
  codigoRegra: '',
  codigoRejeicao: '',
  notaTecnica: '',
  estados: null,
  modelosDocumento: [],
  dataInicio: null,
  dataFim: null,
  descricao: '',
};

const schema = yup.object().shape({
  codigoRegra: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  codigoRejeicao: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  modelosDocumento: yup
    .array()
    .min(1, EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  notaTecnica: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  estados: yup
    .array()
    .nullable()
    .test({
      message: EnumValidacoesSistema.CAMPO_OBRIGATORIO,
      test: (value) => {
        return (value?.length ?? 0) > 0;
      },
    })
    .default(null)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

export const yupResolver = yupResolverInstance(schema);
