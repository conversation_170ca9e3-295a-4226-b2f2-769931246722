import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from '@chakra-ui/react';
import { FiChevronRight } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';

import { BreadcrumbInterface, useLayoutContext } from 'store/Layout';

type BreadcrumbsProps = {
  listBreadcrumbs?: BreadcrumbInterface[];
};

export const Breadcrumbs = ({ listBreadcrumbs }: BreadcrumbsProps) => {
  const { breadcrumb: valuesBreadcrumbs } = useLayoutContext();

  const breadcrumb = listBreadcrumbs ? listBreadcrumbs : valuesBreadcrumbs;

  const navigate = useNavigate();

  return (
    <Breadcrumb
      color="secondary.600"
      fontSize="lg"
      separator={<FiChevronRight color="secondary.600" />}
    >
      {(breadcrumb || []).map((breadcrumbItem, index) => (
          <BreadcrumbItem key={index}>
          <BreadcrumbLink onClick={() => navigate(breadcrumbItem.path || '')}>
            {breadcrumbItem.title}
          </BreadcrumbLink>
        </BreadcrumbItem>
      ))}
    </Breadcrumb>
  );
};
