import { useState } from 'react';
import { Box } from '@chakra-ui/react';
import {
  DndContext,
  closestCenter,
  DragEndEvent,
  Active,
  useSensors,
  PointerSensor,
  TouchSensor,
  useSensor,
  DragOverlay,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  arrayMove,
} from '@dnd-kit/sortable';
import {
  restrictToParentElement,
  restrictToVerticalAxis,
} from '@dnd-kit/modifiers';

import { ItemDraggrable } from '../ItemDraggrable';

interface DraggrableListProps {
  listItemDraggrable: string[];
}

export const DraggrableList = ({ listItemDraggrable }: DraggrableListProps) => {
  const [itemIsDragging, setItemIsDragging] = useState<Active | null>(null);
  const [items, setItems] = useState<string[]>(listItemDraggrable);
  const itemInDragging = items?.find((item) => item === itemIsDragging?.id);

  /*/
    A lib usa o hook useSensors onde você pode definir quais sensores você quer utilizar para o drag and drop
    e definir configurações para esses sensores. Neste exemplo o drag só inicia após o item ultrapassar
    a distancia definida.
  /*/

  const sensores = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
        tolerance: 20,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        distance: 5,
        tolerance: 20,
      },
    })
  );

  return (
    <DndContext
      onDragEnd={handleOnDragEnd}
      modifiers={[restrictToParentElement, restrictToVerticalAxis]}
      collisionDetection={closestCenter}
      sensors={sensores}
      onDragStart={({ active }) => {
        setItemIsDragging(active);
      }}
      onDragCancel={() => {
        setItemIsDragging(null);
      }}
    >
      <Box>
        <SortableContext items={items} strategy={verticalListSortingStrategy}>
          {items.map((item) => (
            <ItemDraggrable key={item} nome={item} />
          ))}
          <DragOverlay>
            {itemInDragging && (
              <ItemDraggrable key={itemInDragging} nome={itemInDragging} />
            )}
          </DragOverlay>
        </SortableContext>
      </Box>
    </DndContext>
  );

  function handleOnDragEnd(event: DragEndEvent) {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setItems((items) => {
        const activeIndex = items.indexOf(active?.id as string);
        const overIndex = items.indexOf(over?.id as string);

        return arrayMove(items, activeIndex, overIndex);
      });
    }
  }
};
