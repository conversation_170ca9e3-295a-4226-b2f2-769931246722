import { toast } from 'react-toastify';

export const valuesMenu = [
  {
    content: 'Editar',
    onClick: () => {
      toast.success('Você editou o item', {
        toastId: 'EditarSucesso',
      });
    },
    possuiFuncionalidade: true,
  },

  {
    content: 'Alterar',
    onClick: () => {
      toast.success('Você alterou o item', {
        toastId: 'AlterarSucesso',
      });
    },
    possuiFuncionalidade: true,
  },
  {
    content: 'Excluir',
    onClick: () => {
      toast.success('Você excluiu o item', {
        toastId: 'ExcluirSucesso',
      });
    },
    possuiFuncionalidade: true,
  },
];
