import auth from 'modules/auth';

import {
  DominiosDeliveryEnum,
  optionsFomerZendar,
  optionsPowerStockChef,
} from 'constants/Enum/enumDominios';

export const useAssinaturaFormulario = () => {
  const usuarioNomeDominio = auth.getUserNameDominio();
  const usuarioRevendaPrincipal = usuarioNomeDominio.includes('zendar');

  const opcoesDominiosDelivery = Object.entries(DominiosDeliveryEnum).map(
    ([_, value]) => ({ label: value, value })
  );

  const getDominioDefault = () => {
    const optionsDominios = usuarioRevendaPrincipal
      ? optionsFomerZendar
      : optionsPowerStockChef;

    const defaultDominio =
      optionsDominios.find((item) => item.value === usuarioNomeDominio) ??
      optionsDominios[0];
    return defaultDominio.value;
  };

  const opcaoPadraoDominioDelivery = opcoesDominiosDelivery.find((opcao) => {
    if (usuarioRevendaPrincipal) {
      return opcao.value === DominiosDeliveryEnum.DOMINIO_STI3;
    }

    return opcao.value === DominiosDeliveryEnum.DOMINIO_REVENDA;
  });

  // Calculate default expiration date (10 days after due date in next month) xx
  const getDefaultExpirationDate = (diaVencimento: number = 10) => {
    const today = new Date();
    const currentYear = today.getFullYear();
    const nextMonth = today.getMonth() + 1;

    // Calculate expiration date: diaVencimento + 10 days in next month
    const dataExpiracao = new Date(currentYear, nextMonth, diaVencimento + 10);

    return dataExpiracao;
  };

  const formDefaultValues = {
    nome: '',
    cidadeNome: null,
    faturamento: null,
    telefoneResponsavel: '',
    email: '',
    dominio: '',
    urlDominio: getDominioDefault(),
    dominioDelivery: '',
    urlDominioDelivery: opcaoPadraoDominioDelivery?.value,
    usuario: 'admin',
    cnpj: '',
    razaoSocial: '',
    fantasia: '',
    inscricaoEstadual: '',
    inscricaoMunicipal: '',
    codigoExterno: '',
    observacao: '',
    cep: '',
    logradouro: '',
    numero: '',
    bairro: '',
    celular: '',
    telefone: '',
    emailContato: '',
    tabelaPreco: null,
    paisNome: { value: 1, label: 'Brasil' },
    complemento: '',
    idAssinaturaBasePrecos: null,
    codigoERP: '',
    diaVencimento: '10',
    dataExpiracao: getDefaultExpirationDate(10),
    ultimaValidacao: getDefaultExpirationDate(10),
    validacaoAutomatica: true,
  };

  return {
    formDefaultValues,
    opcoesDominiosDelivery,
    opcaoPadraoDominioDelivery,
    usuarioRevendaPrincipal,
    usuarioNomeDominio,
    getDefaultExpirationDate,
  };
};
