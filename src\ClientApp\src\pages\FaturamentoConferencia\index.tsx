import { useCallback, useEffect, useRef, useState } from 'react';
import { Flex, GridItem, Td, Tr, Text, Tag, Box, Select } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { format, subMonths } from 'date-fns';
import { FormProvider, useForm } from 'react-hook-form';

import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { ConstanteRotas, SubstituirParametroRota } from 'constants/constantRotas';
import { moneyMask } from 'helpers/format/fieldsMasks';
import { formatDateMesAno } from 'helpers/format/formatStringDate';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';

import { ActionsMenu } from 'components/ActionsMenu';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { LoadingDefault } from 'components/Loading';
import { SelectDefault } from 'components/Select/SelectDefault';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { Pagination } from 'components/Grid/Pagination';

import { FaturamentosAgrupadosProps, FaturamentoFiltroProps } from 'pages/FaturamentoExibicao/validationForm';
import { toast } from 'react-toastify';

export const FaturamentoConferencia = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [listaFaturamentos, setListaFaturamentos] = useState<FaturamentosAgrupadosProps[]>([]);
  const [page, setPage] = useState(1);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [agrupar, setAgrupar] = useState(true);
  const [mesAnoSelecionado, setMesAnoSelecionado] = useState(format(new Date(), 'MMyyyy'));
  const [opcoesComboMesAno, setOpcoesComboMesAno] = useState<{label: string, value: string}[]>([]);
  
  const formMethods = useForm();
  const navigate = useNavigate();

  // Função para buscar os dados
  const buscarDados = useCallback(async (pageNumber: number = 1, mesAnoParam?: string, pageSize: number = 100) => {
    console.log('Buscando dados para:', mesAnoParam || mesAnoSelecionado, 'página:', pageNumber, 'tamanho:', pageSize);
    setIsLoading(true);
    try {
      const mesAnoParaBusca = mesAnoParam || mesAnoSelecionado;
      const formattedMesAno = mesAnoParaBusca?.length === 6 
        ? `${mesAnoParaBusca.substring(0, 2)}/${mesAnoParaBusca.substring(2)}`
        : mesAnoParaBusca;
      
      console.log('MesAno formatado:', formattedMesAno);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<FaturamentosAgrupadosProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_FATURAMENTOS_CONFERENCIA,
          { 
            currentPage: pageNumber,
            pageSize: pageSize,
            orderColumn: 'NomeFantasia',
            orderDirection: 'asc'
          }
        ),
        {
          params: {
            agrupar,
            mesAno: formattedMesAno,
          },
        }
      );

      console.log('Resposta da API:', response);

      if (response.sucesso) {
        setTotalRegistros(response.dados.total);
        setListaFaturamentos(response.dados.registros);
      }
    } catch (error) {
      toast.error('Erro ao carregar dados do faturamento');
      console.error('Erro ao carregar dados:', error);
    } finally {
      setIsLoading(false);
    }
  }, [mesAnoSelecionado, agrupar]);

  // Carrega dados iniciais
  useEffect(() => {
    buscarDados(1);
  }, []);

  useEffect(() => {
    const dataAtual = new Date();
    const opcoes = [];
    
    // Adiciona mês atual e 12 meses anteriores
    for (let i = 0; i <= 3; i++) {
      const data = subMonths(dataAtual, i);
      const valor = format(data, 'MMyyyy');
      const label = format(data, 'MM/yyyy');
      opcoes.push({ label, value: valor });
    }
    
    setOpcoesComboMesAno(opcoes);
  }, []);

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      await buscarDados(
        gridPaginadaConsulta.currentPage, 
        undefined, 
        gridPaginadaConsulta.pageSize
      );
      setPage(gridPaginadaConsulta.currentPage);
    },
    [buscarDados]
  );

  return (
    <>
      {isLoading && <LoadingDefault />}
      <FormProvider {...formMethods}>
        <SimpleGridForm borderRadius="md" py="12px">
          <GridItem colSpan={12}>
            <Flex justifyContent="space-between" alignItems="center">
              <Box minW="250px">
                <Select
                  placeholder="Selecione o mês/ano"
                  value={mesAnoSelecionado}
                  onChange={(e) => {
                    const novoMesAno = e.target.value;
                    console.log('Select onChange:', novoMesAno);
                    setMesAnoSelecionado(novoMesAno);
                    setPage(1);
                    buscarDados(1, novoMesAno);
                  }}
                >
                  {opcoesComboMesAno.map(opcao => (
                    <option key={opcao.value} value={opcao.value}>
                      {opcao.label}
                    </option>
                  ))}
                </Select>
              </Box>
              <Flex direction="row" gap="10px">
                <Text color="secondary.500" fontSize="sm">
                  Pag. {page} | Qtde: {listaFaturamentos.length} | Total {moneyMask(listaFaturamentos.reduce((total, item) => total + item.faturamentoTotalRepasse, 0), true)}
                </Text>
              </Flex>
            </Flex>
          </GridItem>
        </SimpleGridForm>
      </FormProvider>
      <Pagination
        loadColumnsData={paginationHandle}
        nPages={totalRegistros}
        currentPage={page}
        setCurrentPage={setPage}
        isLoading={isLoading}
        defaultOrderDirection="asc"
        defaultKeyOrdered="NomeFantasia"
        tableHeaders={[
          {
            content: 'Ações',
            key: 'Acoes',
            isOrderable: false,
            isNumeric: true,
            width: '0.5px',
          },
          {
            content: 'Revenda',
            key: 'revendaFantasia',
            isOrderable: true,
            width: '30%',
          },
          {
            content: 'Competência',
            key: 'faturamentoDataEmissao',
            isOrderable: true,
            width: '10%',
          },
          {
            content: 'Pagamento',
            key: 'faturamentoDataVencimento',
            isOrderable: true,
            width: '10%',
          },
          {
            content: 'Assinaturas',
            key: 'assinaturaQtde',
            isOrderable: true,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '10%',
          },
          {
            content: 'SubTotal',
            key: 'faturamentoSubTotal',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '10%',
          },
          {
            content: 'Acrésc/Desc',
            key: 'faturamentoAcrescimoDesconto',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '10%',
          },
          {
            content: 'Valor',
            key: 'valor',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '10%',
          },
          {
            content: '',
            key: 'espaco',
            isOrderable: false,
            isNumeric: true,
            width: '100%',
          },
        ]}
        renderTableRows={listaFaturamentos.map((faturamento) => (
          <Tr key={faturamento.revendaId}>
            <Td isNumeric>
              <Flex justifyContent="flex-end">
                <ActionsMenu
                  id={`mostrarMais-${faturamento.faturamentoMesAno}`}
                  menuZIndex="modal"
                  items={[
                    {
                      content: 'Ver detalhes',
                      onClick: () => {
                        // Keep the slash format for consistency
                        const formattedMesAno = faturamento.faturamentoMesAno;
                        
                        navigate(
                          SubstituirParametroRota(
                            ConstanteRotas.FATURAMENTO_EXIBICAO_DETALHADO,
                            'mesAno',
                            formattedMesAno.replace('/', '')  // Remove slash only for URL
                          ),
                          {
                            state: {
                              revendaId: faturamento.revendaId
                            }
                          }
                        );
                      },
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_VER_DETALHES
                      ),
                    }
                  ]}
                />
              </Flex>
            </Td>
            <Td>{faturamento.revendaFantasia}</Td>
            <Td>{formatDateMesAno(faturamento.faturamentoDataEmissao)}</Td>
            <Td>
              {faturamento.faturamentoDataVencimento ? formatDateMesAno(faturamento.faturamentoDataVencimento) : '-'}
            </Td>
            <Td isNumeric>
              {faturamento.assinaturaQtde}
            </Td>
            <Td isNumeric>
              {moneyMask(faturamento.faturamentoSubTotal || 0, true)}
            </Td>
            <Td isNumeric>
              {moneyMask(faturamento.faturamentoValorAcrescimoDesconto || 0, true)}
            </Td>
            <Td w="15%" isNumeric>
              {moneyMask(faturamento.faturamentoTotalRepasse, true)}
            </Td>
            <Td w="100%" />
          </Tr>
        ))}
      />
    </>
  );
};
