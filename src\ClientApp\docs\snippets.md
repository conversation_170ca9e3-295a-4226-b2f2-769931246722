# Snippets disponíveis no projeto

#### O arquivo que contém os snippets fica dentro da pasta `.vscode` e contém a extensão `.code-snippets`. Mais de um arquivo pode ser gerado para separar os snippets.

#### Vale ressaltar que alguns snippets habilitam inserção de variáveis ao usar o `TAB` após iniciá-los, essa pode ser uma função primordial para a utlização do snippet.

Abaixo segue a lista com todos os Snippets disponíveis e os gatilhos para cada um. O **⇥** significa a tecla `TAB`.

| Gatilho | Conteúdo |
| ------- | -------- |
| `st-rfc →` | Cria um Functional Component básico |
| `st-mrfc →` | Cria um Functional Component utilizando **memo()** |
| `st-state →` | Cria a estrutura básica de um **useState** |
| `st-callback →` | Cria a estrutura básica de um **useCallback** |
| `st-memo →` | Cria a estrutura básica de um **useMemo** |
| `st-effect →` | Cria a estrutura básica de um **useEffect** |
| `st-console-log →` | Cria a estrutura básica de um **console.log()** |

#### Importante lembrar que todos os snippets do Multiempresa funcionam da mesma forma no Zendar.