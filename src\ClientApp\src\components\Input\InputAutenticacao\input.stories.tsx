import { Meta, StoryFn } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';
import { Box } from '@chakra-ui/react';

import { theme } from 'theme';

import { InputAutenticacao as Input } from './exampleInput';
import { InputDefaultProps } from '.';

export default {
  title: 'Components/Input',
  argTypes: {
    ...getThemingArgTypes(theme as any, 'Input'),
    colorLabel: {
      control: { type: 'color' },
    },
    colorScheme: {
      control: { type: 'color' },
    },
  },
  args: {
    name: 'inputAutenticacao',
    placeholder: 'Insira o valor aqui',
    label: 'Label',
    colorLabel: 'pink.500',
    colorScheme: 'aquamarine.400',
  },
} as Meta;

export const InputAutenticacao: StoryFn<InputDefaultProps> = (props) => {
  return (
    <Box mt="20px">
      <Input
        colorLabel={props.colorLabel}
        label={props.label}
        colorScheme={props.colorScheme}
        variant="floating"
        w={props.size}
        placeholder={props.placeholder}
      />
    </Box>
  );
};
