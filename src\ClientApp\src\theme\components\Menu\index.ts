type AccessibleColor = {
  color?: string;
  background?: string;
  borderColor?: string;
  focusBackground?: string;
  activeBackground?: string;
};

/** Accessible color overrides for less accessible colors. */
const accessibleColorMap: { [key: string]: AccessibleColor } = {
  white: {
    color: 'black',
    background: 'white',
    borderColor: 'gray.100',
    focusBackground: 'gray.100',
    activeBackground: 'gray.200',
  },
  primary: {
    background: 'primary.800',
    borderColor: 'primary.900',
    focusBackground: 'primary.700',
    activeBackground: 'primary.800',
  },
  secondary: {
    color: 'black',
  },
};

type Dict = Record<string, any>;

const Menu = {
  parts: ['item', 'command', 'list', 'button', 'groupTitle', 'divider'],

  baseStyle: ({ colorScheme }: Dict) => {
    const {
      color = 'white',
      background = `${colorScheme}.500`,
      borderColor = `${colorScheme}.600`,
      focusBackground = `${colorScheme}.600`,
      activeBackground = `${colorScheme}.600`,
    } = accessibleColorMap[colorScheme] || {};

    return {
      button: {
        color,
      },
      item: {
        color,
        px: '2.35rem',
        transition: 'background 50ms ease-in 0s, filter 50ms ease-in 0s',
        _focus: {
          bg: focusBackground,
          _disabled: { filter: 'none' },
        },
        _active: {
          bg: activeBackground,
          _disabled: { filter: 'none' },
        },
        _expanded: {
          bg: focusBackground,
          _disabled: { filter: 'none' },
        },
      },
      list: {
        color,
        bg: background,
        borderColor,
        boxShadow: '0px 0px 4px 0px #00000029',
      },
    };
  },

  defaultProps: {
    colorScheme: 'white',
  },
};

export default Menu;
