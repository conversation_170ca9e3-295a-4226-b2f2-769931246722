export const EnumValidacoesSistema = {
  CAMPO_OBRIGATORIO: 'O campo é obrigatório',

  EMAIL_INVALIDO: 'O e-mail informado é inválido',

  SENHA_MINIMO_SEIS_CARACTERES: 'A senha deve possuir pelo menos 6 caracteres',

  SENHA_UM_CARACTER_NUMERICO:
    'A senha deve possuir pelo menos um caracter numérico',

  SENHA_UMA_LETRA_MAIUSCULA:
    'A senha deve possuir pelo menos uma letra maiúscula',

  SENHA_UMA_LETRA_MINUSCULA:
    'A senha deve possuir pelo menos uma letra minúscula',

  SENHA_UM_CARACTER_ESPECIAL:
    'A senha deve possuir pelo menos um caracter especial',

  SENHA_NAO_COINCIDEM: 'As senhas não coincidem',

  DATA_INVALIDA: 'A data informada é inválida',

  CAMPO_COM_CARACTERE_ESPECIAL:
    'Este campo não pode conter caracteres especiais ou números',

  CEP_INVALIDO: 'O CEP informado é inválido',

  CNPJ_INVALIDO: 'O CNPJ esta inválido',

  CNPJ_ERRO_CONSULTA:
    'Não foi possível consultar o CNPJ, tente novamente em alguns instantes',

  CEP_ERRO_CONSULTA:
    'Não foi possível consultar o CEP, tente novamente em alguns instantes',

  DIAS_MAIOR_QUE_ZERO: 'A quantidade de dias deve ser maior que 0',

  MAX_LENGTH_100: 'Este campo não aceita mais que 100 caracteres',
  MAX_LENGTH_5000: 'Este campo não aceita mais que 5000 caracteres',
};
