import {
  Flex,
  GridItem,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalProps,
  Text,
  useDisclosure,
  Textarea,
} from '@chakra-ui/react';
import { useCallback, useState } from 'react';
import { create, InstanceProps } from 'react-modal-promise';
import { toast } from 'react-toastify';

import { useEffectDefault } from 'hook/useEffectDefault';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';

import { ButtonDefault } from 'components/Button';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { LoadingDefault } from 'components/Loading';

import { ModalDefaultChakra } from '../ModalDefaultChakra';

type ModalVisualizarErroAtualizacaoProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> & {
  id: string;
  atualizarListagem: () => void;
} & InstanceProps<ModalProps>;

export const ModalVisualizarErroAtualizacao =
  create<ModalVisualizarErroAtualizacaoProps>(
    ({ id, atualizarListagem, ...rest }) => {
      const [isLoading, setIsLoading] = useState(false);
      const [erroAtualizacao, setErroAtualizacao] = useState('');

      const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

      const getErroAtualizacao = useCallback(async () => {
        setIsLoading(true);
        const response = await api.get<void, ResponseApi<string>>(
          ConstantEnderecoWebservice.OBTER_ERRO_ATUALIZACAO_CLIENTE,
          {
            params: { id: id },
          }
        );

        if (response.sucesso) {
          const { dados } = response;
          setErroAtualizacao(dados);
        }
        setIsLoading(false);
      }, []);

      useEffectDefault(() => {
        getErroAtualizacao();
      }, [id]);

      const handleAtualizarIndividual = async (id: string) => {
        const response = await api.post<void, ResponseApi>(
          `${ConstantEnderecoWebservice.ATUALIZAR_INDIVIDUAL_ATUALIZACAO}${id}`
        );

        if (response.sucesso) {
          toast.success('Atualização iniciada com sucesso.');
          atualizarListagem();
        }
      };

      return (
        <ModalDefaultChakra
          {...rest}
          isCentered
          size={['full', 'full', '5xl']}
          isOpen={isOpen}
          onClose={onClose}
          autoFocus={false}
        >
          <ModalContent bg="gray.50" w={['full', 'full', 'full']}>
            {isLoading && <LoadingDefault />}
            <ModalHeader borderTopRadius="5px" bg="primary.600" pb="15px">
              <Flex
                color="white"
                pt="5px"
                justifyContent="space-between"
                alignItems="center"
              >
                <Text pl="2px">Visualizar erro</Text>
                <ModalCloseButton mt="13px" mr="10px" />
              </Flex>
            </ModalHeader>
            <ModalBody pt="0">
              <SimpleGridForm mt="25px">
                <GridItem colSpan={[12, 12, 12]}>
                  <Textarea readOnly>{erroAtualizacao}</Textarea>
                </GridItem>
              </SimpleGridForm>
            </ModalBody>
            <ModalFooter>
              <Flex
                w="full"
                justifyContent={['center', 'center', 'right']}
                flexDirection={['column', 'row', 'row']}
              >
                <ButtonDefault
                  width={['full', '120px', '120px', '120px']}
                  colorScheme="gray"
                  mr="20px"
                  mb={['20px', 'undefined', 'undefined']}
                  variant="outlinePill"
                  onClick={() => onClose()}
                  possuiFuncionalidade={true}
                >
                  Cancelar
                </ButtonDefault>
                <ButtonDefault
                  width={['full', '160px', '160px', '160px']}
                  color="white"
                  colorScheme="secondary"
                  onClick={() => handleAtualizarIndividual(id)}
                  possuiFuncionalidade={auth.usuarioPossuiPermissao(
                    ConstantFuncionalidades.AtualizacaoAcao
                      .ATUALIZAR_GERENCIAR_ATUALIZACAO
                  )}
                >
                  Tentar novamente
                </ButtonDefault>
              </Flex>
            </ModalFooter>
          </ModalContent>
        </ModalDefaultChakra>
      );
    }
  );
