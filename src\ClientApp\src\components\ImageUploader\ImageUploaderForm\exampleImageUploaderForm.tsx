import { FormProvider, useForm } from 'react-hook-form';

import yupResolver from './validationForm';
import { ImageUploaderForm as ImageUploader, ImageUploaderProps } from '.';

export const ImageUploaderForm = ({ ...props }: ImageUploaderProps) => {
  const formMethods = useForm({
    resolver: yupResolver,
  });

  return (
    <FormProvider {...formMethods}>
      <ImageUploader {...props} />
    </FormProvider>
  );
};
