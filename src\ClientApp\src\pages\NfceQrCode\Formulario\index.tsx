import { GridItem } from '@chakra-ui/react';

import { EnumEstados } from 'constants/Enum/enumEstados';
import EnumServicosNfceQrCode from 'constants/Enum/enumServicoNfceQrCode';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SelectDefault } from 'components/Select/SelectDefault';

export const FormNfceQrCode = () => {
  return (
    <SimpleGridForm>
      <GridItem colSpan={[12, 12, 3]}>
        <SelectDefault
          label="Serviço"
          name="servico"
          isRequired
          options={EnumServicosNfceQrCode.properties}
        />
      </GridItem>

      <GridItem colSpan={[12, 12, 3]}>
        <SelectDefault
          label="Estado"
          name="estadoCodigo"
          isRequired
          options={EnumEstados.properties}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 2]}>
        <InputDefault
          name="versao"
          maxLength={5}
          label="Versão"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 6]}>
        <InputDefault
          placeholder="Informe a url"
          name="urlProducao"
          isRequired
          label="Url de Produção"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 6]}>
        <InputDefault
          name="urlHomologacao"
          placeholder="Informe a url"
          isRequired
          label="Url de Homologação"
        />
      </GridItem>
    </SimpleGridForm>
  );
};
