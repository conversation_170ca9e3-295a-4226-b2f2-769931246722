import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';
import { enumTipoServico } from 'constants/Enum/enumTipoServicos';

export type SelectOptions = {
  label: string;
  value: number;
  properties?: {
    label: string;
    value: number;
  }[];
};

export type FormData = {
  ativo: boolean;
  nome: string;
  diasExpirar: number;
  tipo: SelectOptions | null;
  referencia: SelectOptions | null;
  quantitativo: boolean;
};

export const formDefaultValues = {
  ativo: true,
  nome: '',
  diasExpirar: 1,
  tipo: null,
  referencia: null,
  quantitativo: false,
};

const schema = yup.object().shape({
  nome: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  diasExpirar: yup.lazy((_, params) => {
    const { tipo, referencia } = params.parent;

    if (tipo.value === enumTipoServico.PLANO && referencia.value === 0) {
      return yup
        .number()
        .typeError(EnumValidacoesSistema.DIAS_MAIOR_QUE_ZERO)
        .min(1, EnumValidacoesSistema.DIAS_MAIOR_QUE_ZERO)
        .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO);
    } else {
      return yup.number();
    }
  }),
  tipo: yup
    .object()
    .typeError(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .nullable()
    .default(null)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  referencia: yup
    .object()
    .typeError(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .nullable()
    .default(null)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});
export const yupResolver = yupResolverInstance(schema);
