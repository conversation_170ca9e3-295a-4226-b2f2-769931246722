import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FiSearch } from 'react-icons/fi';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Box, Flex, Td, Tr, Text, Icon } from '@chakra-ui/react';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import api, { ResponseApi } from 'services/api';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { ActionsMenu } from 'components/ActionsMenu';
import { ButtonDefault } from 'components/Button';
import { ContainerListagem } from 'components/Grid/GridList';
import { Pagination } from 'components/Grid/Pagination';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { EnumEstados } from 'constants/Enum/enumEstados';
import EnumServicosNfceQrCode from 'constants/Enum/enumServicoNfceQrCode';

import {
  NfceQrCode,
  formDefaultValuesListagem,
  FormData,
} from './validationForms';

export const NfceQrCodeListar = () => {
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [listaNfceQrCode, setListaNfceQrCode] = useState<NfceQrCode[]>([]);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<FormData>(
    formDefaultValuesListagem
  );

  const formMethods = useForm<FormData>({
    defaultValues: formDefaultValuesListagem,
  });

  const { watch } = formMethods;
  const urlProducaoWatch = watch('urlProducao');

  const navigate = useNavigate();

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<NfceQrCode>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_NFCE_QRCODE,
          gridPaginadaConsulta
        ),
        {
          params: currentFilters,
        }
      );

      if (response.sucesso) {
        setTotalRegistros(response.dados.total);
        setListaNfceQrCode(response.dados.registros);
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const handleExcluirNfce = useCallback((id: string) => {
    ModalWarning({
      title: 'Você tem certeza?',
      description: 'Essa ação vai excluir a Nfc-e QrCode!',
      confirmButtonText: 'Sim, continuar!',
      cancelButtonText: 'Cancelar',
      onConfirm: async () => {
        const response = await api.delete<void, ResponseApi>(
          ConstantEnderecoWebservice.EXCLUIR_NFCE_QRCODE,
          { params: { id } }
        );

        if (response.sucesso) {
          toast.success(`A Nfc-e QrCode foi excluída com sucesso.`);

          setRecarregarListagem(!recarregarListagem);
          return true;
        }
        return false;
      },
    });
  }, []);

  return (
    <Box>
      <FormProvider {...formMethods}>
        <ContainerListagem
          inputPesquisa={
            <InputDefault
              maxLength={100}
              placeholder="Buscar Nfc-e QrCode"
              iconLeftElement={FiSearch}
              name="urlProducao"
              onEnterKeyPress={() => {
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  urlProducao: urlProducaoWatch,
                }));
              }}
            />
          }
          buttonCadastrar={
            <ButtonDefault
              onClick={() => navigate(ConstanteRotas.NFCE_QRCODE_CADASTRAR)}
              width={['full', 'full', 'full', '220px']}
              color="white"
              colorScheme="secondary"
              leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
              possuiFuncionalidade={auth.usuarioPossuiPermissao(
                ConstantFuncionalidades.FicalNFCeAcao.CADASTRAR_NFCE
              )}
            >
              Cadastrar novo
            </ButtonDefault>
          }
        >
          <Pagination
            loadColumnsData={paginationHandle}
            nPages={totalRegistros}
            currentPage={page}
            setCurrentPage={setPage}
            isLoading={isLoading}
            defaultOrderDirection="asc"
            defaultKeyOrdered="Servico"
            tableHeaders={[
              {
                content: 'Serviço',
                key: 'servico',
                isOrderable: false,
                width: '160px',
              },
              {
                content: 'Url de Produção',
                key: 'ulr-producao',
                isOrderable: false,
                width: 'auto',
              },
              {
                content: 'Url de Homologação',
                key: 'ulr-homologacao',
                isOrderable: false,
                width: 'auto',
              },
              {
                content: 'Estado',
                key: 'estado',
                isOrderable: false,
                width: 'auto',
              },
              {
                content: 'Versão',
                key: 'versao',
                isOrderable: false,
                width: 'auto',
              },
              {
                content: 'Ações',
                key: 'Acoes',
                isOrderable: false,
                isNumeric: true,
                width: '10px',
              },
            ]}
            renderTableRows={listaNfceQrCode.map((nfceQrcod) => (
              <Tr key={nfceQrcod.id}>
                <Td>
                  {
                    EnumServicosNfceQrCode.properties.find(
                      (servico) => servico.value === nfceQrcod.servico
                    )?.label
                  }
                </Td>
                <Td pb="3px" pt="3px">
                  <Flex w="full">
                    <Text lineHeight="12.5px" maxW="full" whiteSpace="pre-line">
                      {nfceQrcod.urlProducao}
                    </Text>
                  </Flex>
                </Td>
                <Td>{nfceQrcod.urlHomologacao}</Td>
                <Td>
                  {
                    EnumEstados.properties.find(
                      (estado) => estado.value === nfceQrcod.estadoCodigo
                    )?.label
                  }
                </Td>
                <Td>{nfceQrcod.versao}</Td>

                <Td isNumeric>
                  <Flex justifyContent="right">
                    <ActionsMenu
                      id="nfceQrcodacoes"
                      items={[
                        {
                          content: 'Editar',
                          onClick: () => {
                            navigate(
                              SubstituirParametroRota(
                                ConstanteRotas.NFCE_QRCODE_ALTERAR,
                                'id',
                                nfceQrcod.id
                              )
                            );
                          },
                          possuiFuncionalidade: auth.usuarioPossuiPermissao(
                            ConstantFuncionalidades.FicalNFCeAcao.ALTERAR_NFCE
                          ),
                        },
                        {
                          content: 'Excluir',
                          onClick: () => {
                            handleExcluirNfce(nfceQrcod.id);
                          },
                          possuiFuncionalidade: auth.usuarioPossuiPermissao(
                            ConstantFuncionalidades.FicalNFCeAcao.EXCLUIR_NFCE
                          ),
                        },
                      ]}
                    />
                  </Flex>
                </Td>
              </Tr>
            ))}
          />
        </ContainerListagem>
      </FormProvider>
    </Box>
  );
};
