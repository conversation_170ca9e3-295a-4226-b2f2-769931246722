import cypress from 'cypress';

import { hexToRgbA } from 'store/getHexDecimalColor';

import { InputCpfCnpj as Input } from './exampleInput';

const valueLabel = 'Cpf';
const valueColorLabel = '#6502b2';

describe('Testing cpf/cnpj input', () => {
  beforeEach(() => {
    cy.mount(<Input label={valueLabel} colorLabel={valueColorLabel} />);
  });

  const isCnpj = true;
  const valueInput = '12112312312323';

  it('Field formatting is being rendered', () => {
    cy.get('input[name=cnpj]')
      .type(valueInput, {
        delay: 0,
      })
      .invoke('val')
      .should((value) => {
        expect(String(value).includes('/')).to.eq(isCnpj ? true : false); // passes
      });
  });

  it('Input label is showing', () => {
    cy.testLabelInput(valueLabel);
  });

  it('Validating the color of the label', () => {
    cy.testColorLabel(valueLabel, hexToRgbA(valueColorLabel));
  });

  it('Render only numbers', () => {
    cy.get('input[name=cnpj]')
      .type(valueInput, {
        delay: 0,
      })
      .invoke('val')
      .should((value) => {
        expect(value === '').to.eq(false); // passes
      });
  });

  it('Number of correct characters in the input', () => {
    cy.get('input[name=cnpj]')
      .type(valueInput, {
        delay: 0,
      })
      .invoke('val')
      .should((value) => {
        expect(value ? String(value).length : 0).to.eq(isCnpj ? 18 : 14); // passes
      });
  });
});
