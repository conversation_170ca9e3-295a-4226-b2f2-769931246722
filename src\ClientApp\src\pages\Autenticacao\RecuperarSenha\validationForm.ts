import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

const schema = yup.object().shape({
  email: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .email(EnumValidacoesSistema.EMAIL_INVALIDO),
});

export const yupResolver = yupResolverInstance(schema);
