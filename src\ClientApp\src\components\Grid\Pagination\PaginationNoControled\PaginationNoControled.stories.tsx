import { Meta } from '@storybook/react';
import { useCallback, useState } from 'react';
import { Box, Divider, Flex, Td, Tr } from '@chakra-ui/react';

import { dataPagination } from 'services/dataStorybook';

import { PaginationNoControled as Pagination } from '.';

export default {
  title: 'Components/Paginação',
  component: Pagination,
} as Meta;

export const PaginationNoControled = () => {
  const [page, setPage] = useState(1);

  // no lugar do dataPagination, seria um state com o valor que retorna da api

  const currentTableData = useCallback(() => {
    const firstPageIndex = (page - 1) * 5;
    const lastPageIndex = firstPageIndex + 5;

    return dataPagination.slice(firstPageIndex, lastPageIndex);
  }, [dataPagination, page])();

  // a função currentTableData é para validar a quantidade de dados que irão aparecer em casa página
  // nesse caso são 5
  // obs: Sempre que for usar esse componente precisa passar essa função

  return (
    <Box>
      <Pagination
        setCurrentPage={setPage}
        nPages={dataPagination.length}
        currentPage={page}
        itemsPerPage={5}
        tableHeaders={[
          {
            key: 'name',
            content: 'Nome',
            width: '50%',
            justifyContent: 'left',
          },
          {
            key: 'number',
            content: 'Numero',
            justifyContent: 'right',
          },
        ]}
        renderTableRows={currentTableData.map((item, index) => (
          <>
            <Tr
              sx={{
                '& > td': {
                  height: '55px',
                  whiteSpace: 'nowrap',
                },
              }}
            >
              <Td pt="0" pb="0" w="40%" color="black">
                <Flex justifyContent="left">{item.name}</Flex>
              </Td>

              <Td pt="0" pb="2px" isNumeric w="30%" color="black">
                <Flex minW="200px" justifyContent="right">
                  {item.number}
                </Flex>
              </Td>
            </Tr>
            {index !== currentTableData.length && (
              <Tr>
                <Td pt="0" pb="0" colSpan={4}>
                  <Divider bg="gray.200" h="1px" />
                </Td>
              </Tr>
            )}
          </>
        ))}
      />
    </Box>
  );
};
