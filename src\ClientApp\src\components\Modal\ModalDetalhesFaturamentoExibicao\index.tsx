import { useCallback, useEffect, useState } from 'react';
import {
  Box,
  Flex,
  Grid,
  GridItem,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalProps,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useDisclosure,
  useMediaQuery,
  Modal<PERSON>eader,
  ModalCloseButton,
  Divider,
} from '@chakra-ui/react';
import { useForm, FormProvider } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';

import { moneyMask } from 'helpers/format/fieldsMasks';
import { capitalize } from 'helpers/format/stringFormats';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { LoadingDefault } from 'components/Loading';
import { ButtonDefault } from 'components/Button';
import { ModalDefaultChakra } from 'components/Modal/ModalDefaultChakra';

import { FaturamentoExibicaoProps } from 'pages/FaturamentoExibicao/validationForm';

type ModalDetalhesFaturamentoExibicaoProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> & {
  faturamento: FaturamentoExibicaoProps;
} & InstanceProps<ModalProps>;

export const ModalDetalhesFaturamentoExibicao = create<
  ModalDetalhesFaturamentoExibicaoProps,
  ModalProps
>(({ onResolve, onReject, faturamento, ...rest }) => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm();
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  const handleClose = () => {
    onClose();
    onResolve();
  };

  return (
    <ModalDefaultChakra
      onClose={handleClose}
      size={isLargerThan900 ? '6xl' : 'full'}
      isCentered
      {...rest}
      autoFocus={false}
    >
      <FormProvider {...formMethods}>
        <ModalContent
          h={['full', 'fit-content']}
          maxH="full"
          w={['full', '5xl', '8xl']}
          bg="gray.50"
        >
          {isLoading && <LoadingDefault />}
          <ModalHeader
            borderTopRadius="5px"
            bg="primary.500"
            pb="15px"
            borderBottom="1px solid"
            borderColor="gray.50"
          >
            <Flex
              color="white"
              pt="5px"
              justifyContent="space-between"
              alignItems="center"
            >
              <Text pl="2px" color="white">
                Detalhes faturamento
              </Text>
              <ModalCloseButton
                id="closeButton"
                mt="13px"
                mr="10px"
                color="gray.50"
              />
            </Flex>
          </ModalHeader>
          <ModalBody pt="20px" backgroundColor="white">
            <SimpleGridForm
              backgroundColor="primary.500"
              boxShadow="xs"
              rounded="md"
            >
              <GridItem colSpan={[12, 12, 8, 8]}>
                <Grid px={['24px', '24px', '24px', '48px']} py="18px">
                  <Text fontSize="md" fontWeight="normal" color="white">
                    Assinatura
                  </Text>
                  <Text
                    fontSize={['xl', '2xl']}
                    color="secondary.400"
                    fontWeight="semibold"
                  >
                    {faturamento?.assinaturaFantasia && capitalize(faturamento?.assinaturaFantasia)}
                  </Text>
                </Grid>
              </GridItem>  
              <GridItem colSpan={[12, 12, 4, 4]}>
                <Grid
                  gap="5px"
                  px={['24px', '24px', '24px', '48px']}
                  py="18px"
                  placeItems={['flex-start', 'flex-start', 'flex-end']}
                >
                  <Text fontSize="md" fontWeight="normal" color="white">
                    Total de Repasse
                  </Text>
                  <Text
                    fontSize={['xl', 'xl', 'xl', '2xl']}
                    color="secondary.400"
                    fontWeight="semibold"
                  >
                    {moneyMask(faturamento?.faturamentoTotalRepasse, true)}
                  </Text>
                </Grid>
              </GridItem>
            </SimpleGridForm>

            {/* Segunda linha com SubTotal e Acréscimo/Desconto */}
            {(faturamento?.faturamentoValorAcrescimoDesconto != 0) && (
              <SimpleGridForm
                backgroundColor="primary.500"
                boxShadow="xs"
                rounded="md"
                mt="10px"
              >
                <GridItem colSpan={[12, 6, 2, 2]}>
                  <Grid
                    gap="5px"
                    px={['24px', '24px', '24px', '24px']}
                    py="18px"
                    placeItems={['flex-start', 'flex-start', 'flex-start']}
                  >
                    <Text fontSize="md" fontWeight="normal" color="white">
                      SubTotal
                    </Text>
                    <Text
                      fontSize={['xl', 'xl', 'xl', '2xl']}
                      color="secondary.400"
                      fontWeight="semibold"
                    >
                      {moneyMask(faturamento?.faturamentoSubTotal || 0, true)}
                    </Text>
                  </Grid>
                </GridItem>
                <GridItem colSpan={[12, 6, 2, 2]}>
                  <Grid
                    gap="5px"
                    px={['24px', '24px', '24px', '24px']}
                    py="18px"
                    placeItems={['flex-start', 'flex-start', 'flex-start']}
                  >
                    <Text fontSize="md" fontWeight="normal" color="white">
                      Acrésc/Desc
                    </Text>
                    <Text
                      fontSize={['xl', 'xl', 'xl', '2xl']}
                      color="secondary.400"
                      fontWeight="semibold"
                    >
                      {moneyMask(faturamento?.faturamentoValorAcrescimoDesconto || 0, true)}
                    </Text>
                  </Grid>
                </GridItem>
                <GridItem colSpan={[12, 12, 8, 8]}>
                  <Grid
                    gap="5px"
                    px={['24px', '24px', '24px', '24px']}
                    py="18px"
                    placeItems={['flex-start', 'flex-start', 'flex-start']}
                  >
                    <Text fontSize="md" fontWeight="normal" color="white">
                      Motivo
                    </Text>
                    <Text
                      fontSize={['md', 'md', 'md', 'lg']}
                      color="secondary.400"
                      fontWeight="normal"
                      noOfLines={1}
                      title={faturamento?.faturamentoMotivoAcrescimoDesconto}
                    >
                      {faturamento?.faturamentoMotivoAcrescimoDesconto}
                    </Text>
                  </Grid>
                </GridItem>
              </SimpleGridForm>
            )}

            <SimpleGridForm mt="24px">              
              <GridItem colSpan={12}>
                {faturamento?.itens?.length > 0 ? (
                  <Box
                    w="full"
                    overflow="auto"
                    maxH="400px"
                    boxShadow="0px 0px 6px #00000034"
                    rounded="md"
                  >
                    <Table
                      sx={{
                        '& th': {
                          fontSize: '2xs',
                          color: 'primary.500',
                          border: 'none',
                          backgroundColor: 'gray.50',
                        },
                        '& tr': {
                          border: 'none',
                        },
                        '& td': {
                          borderColor: 'gray.50',
                        },
                      }}
                    >
                      <Thead>
                        <Tr>
                          <Th fontSize="xs" w="20%">
                            Serviço
                          </Th>
                          <Th w="15%">Qtde</Th>
                          <Th w="15%" isNumeric>
                            Vlr. Unitário Repasse
                          </Th>
                          <Th w="15%" isNumeric>
                            Vlr. Total Repasse
                          </Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {faturamento.itens.map((item, index) => (
                          <Tr
                            key={item.faturamentoItemId}
                            sx={{ marginTop: index === 0 ? '0' : '2px' }}
                          >
                            <Td minW="180px">{item?.servicoNome}</Td>
                            <Td>{item.quantidade}</Td>
                            <Td isNumeric>
                              {moneyMask(item.valorUnitarioRepasse, true)}
                            </Td>
                            <Td isNumeric>
                              {moneyMask(item.valorRepasse, true)}
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </Box>
                ) : (
                  <Text mt="20px" fontSize="xs">
                    Nenhum item de faturamento encontrado
                  </Text>
                )}
              </GridItem>
            </SimpleGridForm>
          </ModalBody>
          <ModalFooter backgroundColor="white">
            <Flex
              w="full"
              justifyContent="center"
              flexDirection={['column', 'row', 'row']}
            >
              <ButtonDefault
                width={['full', '240px', '120px', '120px']}
                mr="20px"
                mb={['20px', 'undefined', 'undefined']}
                variant="outlinePill"
                onClick={handleClose}
                possuiFuncionalidade={true}
                backgroundColor="white"
                border="1px solid"
                borderColor="gray.100"
              >
                Fechar
              </ButtonDefault>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </FormProvider>
    </ModalDefaultChakra>
  );
});


