import { Meta, StoryFn } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';

import { theme } from 'theme';

import { InputCep as Input } from './exampleInput';
import { InputCepProps } from '.';

export default {
  title: 'Components/Input',
  argTypes: getThemingArgTypes(theme as any, 'Input'),
  args: {
    name: 'inputCep',
    placeholder: 'Insira o valor aqui',
    label: 'Label',
    background: 'white.500',
    isRequired: true,
  },
} as Meta;

export const InputCep: StoryFn<InputCepProps> = (props) => {
  return (
    <Input
      label={props.label}
      w={props.size}
      variant={props.variant}
      {...props}
    />
  );
};
