import { useCallback, useState } from 'react';
import { Flex, Icon, Link, Td, Tr } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { FiSearch } from 'react-icons/fi';
import { toast } from 'react-toastify';

import auth from 'modules/auth';
import api, { ResponseApi } from 'services/api';

import { EnumStatusCadastros } from 'constants/Enum/enumStatusCadastros';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { formatQueryPagedTableZenflix } from 'helpers/queryParamsFormat';

import { ButtonDefault } from 'components/Button';
import { ActionsMenu } from 'components/ActionsMenu';
import { Pagination } from 'components/Grid/Pagination';
import { ModalCancelar } from 'components/Modal/ModalCancelar';
import { ContainerListagem } from 'components/Grid/GridList';
import { SelectDefault } from 'components/Select/SelectDefault';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';

type FormData = {
  titulo: string;
  ativo: boolean | null;
};

type TemaProps = {
  id: string;
  ativo: boolean;
  titulo: string;
  sequenciaOrdenacao: number;
};

const ListarTemas = () => {
  const formMethods = useForm<FormData>({
    defaultValues: {
      ativo: EnumStatusCadastros.TODOS.value,
      titulo: '',
    },
  });
  const { watch } = formMethods;
  const tituloWatch = watch('titulo');

  const navigate = useNavigate();

  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [listaTemas, setListaTemas] = useState<TemaProps[]>([]);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [currentFilters, setCurrentFilters] = useState({} as FormData);

  const possuiPermissaoAlterarTema = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.Zenflix.ALTERAR_TEMAS
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<TemaProps>>
      >(
        formatQueryPagedTableZenflix(
          ConstantEnderecoWebservice.LISTAR_TEMAS,
          gridPaginadaConsulta
        ),
        { params: currentFilters }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        if (response.sucesso && response.dados) {
          setListaTemas(response.dados.registros);
          setTotalRegistros(response.dados.total);
        }
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const handleExcluirTema = useCallback(
    async (temaId: string) => {
      ModalCancelar({
        title: 'Você tem certeza?',
        description: 'A ação irá excluir este tema!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        colorScheme: 'red',
        isVisibleMotivo: false,
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            `${ConstantEnderecoWebservice.EXCLUIR_TEMA}/${temaId}`
          );

          if (response) {
            if (response.avisos) {
              response.avisos.forEach((aviso) => toast.warning(aviso));
            }
            if (response.sucesso) {
              setRecarregarListagem(!recarregarListagem);
              toast.success('Cadastro excluído com sucesso.');
              return true;
            }
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );

  const handleAlterarTema = useCallback((temaId: string) => {
    if (!possuiPermissaoAlterarTema) return;
    navigate(
      SubstituirParametroRota(ConstanteRotas.TEMA_ALTERAR, 'id', temaId)
    );
  }, []);

  return (
    <FormProvider {...formMethods}>
      <ContainerListagem
        inputPesquisa={
          <InputDefault
            maxLength={100}
            placeholder="Buscar por titulo"
            iconLeftElement={FiSearch}
            name="titulo"
            onEnterKeyPress={() => {
              setCurrentFilters((filtrosJaAdicionados) => ({
                ...filtrosJaAdicionados,
                titulo: tituloWatch,
              }));
            }}
          />
        }
        filtrosListagem={
          <SelectDefault
            name="ativo"
            placeholder="Selecione um filtro"
            filtrosAtivos
            asControlledByObject={false}
            onSelect={(optionSelecionada) =>
              setCurrentFilters((filtrosJaAdicionados) => ({
                ...filtrosJaAdicionados,
                ativo: optionSelecionada?.value,
              }))
            }
            options={EnumStatusCadastros.properties.map((status) => status)}
          />
        }
        buttonCadastrar={
          <ButtonDefault
            onClick={() => navigate(ConstanteRotas.TEMA_CADASTRAR)}
            width={['full', 'full', 'full', '220px']}
            color="white"
            colorScheme="secondary"
            leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
            possuiFuncionalidade={auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.Zenflix.CADASTRAR_TEMAS
            )}
          >
            Cadastrar novo
          </ButtonDefault>
        }
      >
        <Pagination
          currentPage={page}
          isLoading={isLoading}
          nPages={totalRegistros}
          setCurrentPage={setPage}
          loadColumnsData={paginationHandle}
          defaultKeyOrdered="titulo"
          defaultOrderDirection="asc"
          tableHeaders={[
            {
              content: <StatusCircle hasValue={false} />,
              key: 'ativo',
              width: '1%',
              isOrderable: false,
            },
            {
              content: 'Título',
              key: 'titulo',
              width: '45%',
              isOrderable: false,
            },
            {
              content: 'Ordem',
              key: 'ordem',
              width: '50%',
              isOrderable: false,
            },
            {
              content: 'Ações',
              key: 'acoes',
              isOrderable: false,
              width: '10px',
            },
          ]}
          renderTableRows={listaTemas.map((tema) => (
            <Tr key={tema.id}>
              <Td>
                <StatusCircle isActive={tema.ativo} />
              </Td>
              <Td pb="3px" pt="3px">
                <Flex w="full">
                  <Link
                    lineHeight="12.5px"
                    maxW="full"
                    whiteSpace="pre-line"
                    cursor="pointer"
                    onClick={() => handleAlterarTema(tema.id)}
                  >
                    {tema.titulo}
                  </Link>
                </Flex>
              </Td>
              <Td pb="3px" pt="3px">
                {tema.sequenciaOrdenacao}
              </Td>
              <Td>
                <Flex justifyContent="right">
                  <ActionsMenu
                    id="videoAcoes"
                    items={[
                      {
                        content: 'Editar',
                        onClick: () => handleAlterarTema(tema.id),
                        possuiFuncionalidade: possuiPermissaoAlterarTema,
                      },
                      {
                        content: 'Excluir',
                        onClick: () => handleExcluirTema(tema.id),
                        possuiFuncionalidade: auth.usuarioPossuiPermissao(
                          ConstantFuncionalidades.Zenflix.EXCLUIR_TEMAS
                        ),
                      },
                    ]}
                  />
                </Flex>
              </Td>
            </Tr>
          ))}
        />
      </ContainerListagem>
    </FormProvider>
  );
};

export default ListarTemas;
