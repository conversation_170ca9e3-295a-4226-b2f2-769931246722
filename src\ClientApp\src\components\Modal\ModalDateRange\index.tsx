import { useState, useEffect, useMemo, useRef } from 'react';
import {
  useMediaQuery,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  Flex,
  useToken,
  ModalFooter,
  HStack,
  Button,
  Stack,
  ModalProps,
  useDisclosure,
} from '@chakra-ui/react';
import {
  addDays,
  endOfDay,
  startOfDay,
  isSameDay,
  differenceInCalendarDays,
} from 'date-fns';
import { defaultStaticRanges, DateRangePicker } from 'react-date-range';
import { pt } from 'date-fns/esm/locale';
import { create, InstanceProps } from 'react-modal-promise';

import { setDateMaxHours, setDateMinHours } from 'helpers/data/setHoursDate';

import InputDate from 'components/Input/InputDate';

import { ModalDefaultChakra } from '../ModalDefaultChakra';

export type Selection = {
  startDate: Date;
  endDate: Date;
};

type ModalDateRangeProps = Omit<ModalProps, 'children' | 'isOpen' | 'onClose'> &
  InstanceProps<ModalProps> & {
    onSubmit: (newSelection?: Selection | undefined) => boolean;
    getCurrentSelection?: () => Selection;
    minDate?: Date;
    maxDate?: Date;
    cleanFiltersButtonText?: string;
  };

export const ModalDateRange = create<ModalDateRangeProps>(
  ({
    onResolve,
    onSubmit,
    getCurrentSelection,
    cleanFiltersButtonText,
    minDate = addDays(new Date(), -300),
    maxDate = addDays(new Date(), 900),
    ...rest
  }) => {
    const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
    const [isLessThan700] = useMediaQuery('(max-width: 700px)');
    const [primary500, gray50, gray100] = useToken('colors', [
      'primary.500',
      'gray.50',
      'gray.100',
    ]);

    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const defineds = {
      startOfToday: startOfDay(new Date()),
      endOfToday: endOfDay(new Date()),
    };

    const buttonConfirm = useRef<HTMLButtonElement>(null);

    const [selection, setSelection] = useState({} as Selection);

    const selectionRange = useMemo(
      () => ({ ...selection, key: 'selection' }),
      [selection]
    );

    function handleOnSubmit() {
      const dadosForamValidados = onSubmit(selection);
      if (dadosForamValidados) {
        onClose();
      }
    }

    function handleSetSelection({
      startDate: newStartDate,
      endDate: newEndDate,
    }: Selection) {
      const startDate = setDateMinHours(newStartDate);

      const endDate = setDateMaxHours(newEndDate);

      setSelection({ startDate, endDate });
    }

    useEffect(() => {
      if (isOpen && getCurrentSelection) {
        const newSelection = getCurrentSelection();

        setSelection(newSelection);
      }
    }, [getCurrentSelection, isOpen]);

    const defaultInputRanges = [
      {
        label: 'dias até hoje',
        range(value: number) {
          return {
            startDate: addDays(defineds.startOfToday, value * -1),
            endDate: defineds.endOfToday,
          };
        },
        getCurrentValue(range: any) {
          if (isSameDay(range.startDate, defineds.endOfToday)) return ' ';
          return (
            differenceInCalendarDays(defineds.endOfToday, range.startDate) ?? 0
          );
        },
      },
    ];

    return (
      <ModalDefaultChakra
        {...rest}
        isCentered
        size={['full', 'full', '2xl']}
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <ModalContent
          margin={0}
          maxW={isLargerThan900 ? '650' : undefined}
          borderRadius={isLargerThan900 ? 'md' : 0}
          bg="gray.50"
        >
          <ModalHeader
            color="primary.500"
            mt={isLargerThan900 ? undefined : 12}
            mb={isLargerThan900 ? undefined : 8}
          >
            Selecione o período
          </ModalHeader>
          <ModalCloseButton
            mt={isLargerThan900 ? undefined : 14}
            mr={isLargerThan900 ? undefined : 6}
            _focus={{ border: 'none' }}
          />
          <ModalBody py={0}>
            <Flex justifyContent="center">
              <Stack
                direction={{ base: 'column', sm: 'row' }}
                justifyContent={{ base: 'center', sm: 'flex-end' }}
                alignItems="center"
                spacing={{ base: '4', sm: '6' }}
                mb="4"
                mt="5px"
                w={{ base: 'full', sm: '575px' }}
              >
                <InputDate
                  name="minDateInput"
                  value={selection.startDate}
                  onChange={(newStartDate) => {
                    handleSetSelection({
                      ...selection,
                      startDate: newStartDate,
                    });
                  }}
                  w="full"
                  maxW={{ base: '350px', sm: '164px' }}
                  minDate={minDate}
                  maxDate={selection.endDate}
                  typeOfFormattedDate="onlyDate"
                />
                <InputDate
                  value={selection.endDate}
                  name="maxDateInput"
                  onChange={(newEndDate) => {
                    handleSetSelection({ ...selection, endDate: newEndDate });
                    if (
                      newEndDate.getDate() !== selection.endDate.getDate() ||
                      newEndDate.getMonth() !== selection.endDate.getMonth() ||
                      newEndDate.getFullYear() !==
                        selection.endDate.getFullYear()
                    ) {
                      buttonConfirm.current?.focus();
                    }
                  }}
                  w="full"
                  maxW={{ base: '350px', sm: '164px' }}
                  minDate={selection.startDate}
                  maxDate={maxDate}
                  typeOfFormattedDate="onlyDate"
                />
              </Stack>
            </Flex>

            <Flex
              h="full"
              alignItems={isLessThan700 ? 'center' : ''}
              justifyContent="center"
              sx={{
                '& .rdrMonthName': {
                  color: primary500,
                },
                '& .rdrDateDisplayWrapper, & .rdrStaticRange, & .rdrDefinedRangesWrapper':
                  {
                    bg: gray50,
                    borderColor: gray100,
                  },
                '& .rdrDayToday > .rdrDayNumber > span::after': {
                  bg: primary500,
                },
                '.rdrDefinedRangesWrapper': {
                  display: isLessThan700 && 'none',
                },
              }}
            >
              <DateRangePicker
                onChange={(range: any) => {
                  if (range && range.selection) {
                    handleSetSelection(range.selection as Selection);
                  }
                }}
                months={1}
                minDate={minDate}
                maxDate={maxDate}
                locale={pt}
                direction="vertical"
                ranges={[selectionRange]}
                scroll={{ enabled: false }}
                staticRanges={defaultStaticRanges.map((obj, index) => {
                  const valuesRanges = [
                    'Hoje',
                    'Ontem',
                    'Essa semana',
                    'Semana passada',
                    'Este mês',
                    'Mês passado',
                  ];
                  const newObj = { ...obj, label: valuesRanges[index] };

                  return newObj;
                })}
                inputRanges={defaultInputRanges.map((obj: any) => {
                  return obj;
                })}
                // @ts-ignore
                rangeColors={primary500}
                color={primary500}
                showDateDisplay={false}
              />
            </Flex>
          </ModalBody>
          <ModalFooter
            justifyContent="center"
            borderTop="1px"
            borderColor="gray.100"
            mx={8}
          >
            <HStack spacing={6}>
              <Button
                onBlur={() => {
                  document.getElementById('dateInitial')?.focus();
                }}
                variant="outlinePill"
                size="sm"
                minW="100px"
                id="buttonClearDate"
                colorScheme="gray.400"
                onClick={() => {
                  handleSetSelection({
                    endDate: new Date(),
                    startDate: new Date(),
                  });
                  document.getElementById('dateInitial')?.focus();
                }}
              >
                {cleanFiltersButtonText || 'Limpar'}
              </Button>
              <Button
                ref={buttonConfirm}
                variant="solid"
                id="buttonConfirmDate"
                colorScheme="secondary"
                size="sm"
                minW="100px"
                onClick={handleOnSubmit}
              >
                Confirmar
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </ModalDefaultChakra>
    );
  }
);
