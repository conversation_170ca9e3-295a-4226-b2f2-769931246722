import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

const schema = yup.object().shape({
  usuario: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  senha: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

export const yupResolver = yupResolverInstance(schema);
