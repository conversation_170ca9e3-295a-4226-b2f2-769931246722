const fs = require('fs');
const { analisarTodosArquivos, enumMapping } = require('./analisar_funcionalidades.cjs');

// Tipos de usuário na ordem das colunas
const tiposUsuario = [
    'SISTEMA_ADMIN',
    'SISTEMA_FINANCEIRO', 
    'DESENVOLVEDOR',
    'REVENDA_ADMIN',
    'REVENDA_ASSISTENTE',
    'SUPORTE_STI3',
    'CANAIS_GERENTE',
    'ANALISTA_CONTEUDO'
];

function gerarHTML() {
    console.log('🔄 Gerando página HTML com dados atualizados...');
    
    // Obter dados atualizados das ações
    const acoesData = analisarTodosArquivos();
    
    if (Object.keys(acoesData).length === 0) {
        console.error('❌ Nenhuma ação foi encontrada!');
        return false;
    }
    
    // Ordenar ações alfabeticamente
    const acoesOrdenadas = Object.keys(acoesData).sort();
    
    // Gerar estatísticas
    const stats = {};
    tiposUsuario.forEach(tipo => stats[tipo] = 0);
    
    Object.values(acoesData).forEach(usuarios => {
        usuarios.forEach(usuarioEnum => {
            if (enumMapping[usuarioEnum]) {
                stats[enumMapping[usuarioEnum]]++;
            }
        });
    });
    
    // Gerar linhas da tabela
    let linhasTabela = '';
    acoesOrdenadas.forEach(acao => {
        const usuariosEnum = acoesData[acao];
        let linha = `                    <tr>\n                        <td>${acao}</td>\n`;
        
        tiposUsuario.forEach(tipo => {
            // Encontrar o valor enum correspondente ao tipo
            const enumValue = Object.keys(enumMapping).find(key => enumMapping[key] === tipo);
            const temPermissao = usuariosEnum.includes(parseInt(enumValue));
            const classe = temPermissao ? 'sim' : 'nao';
            const texto = temPermissao ? 'SIM' : 'NÃO';
            linha += `                        <td class="${classe}">${texto}</td>\n`;
        });
        
        linha += '                    </tr>\n';
        linhasTabela += linha;
    });
    
    // Gerar estatísticas para exibição
    let estatisticasHTML = '';
    Object.entries(stats).forEach(([tipo, count]) => {
        estatisticasHTML += `                <div class="stat-item">\n`;
        estatisticasHTML += `                    <span class="stat-label">${tipo}:</span>\n`;
        estatisticasHTML += `                    <span class="stat-value">${count} permissões</span>\n`;
        estatisticasHTML += `                </div>\n`;
    });
    
    const totalPermissoes = Object.values(stats).reduce((sum, count) => sum + count, 0);
    const dataAtual = new Date().toLocaleString('pt-BR');
    
    // Template HTML
    const htmlContent = `<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapeamento de Ações e Funcionalidades</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .info-bar {
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #495057;
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .copy-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table-container {
            overflow-x: auto;
            max-height: 70vh;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        th {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        th:first-child {
            text-align: left;
            min-width: 300px;
        }

        td {
            padding: 12px 10px;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }

        td:first-child {
            text-align: left;
            font-weight: 500;
            background: #f8f9fa;
            position: sticky;
            left: 0;
            z-index: 5;
            border-right: 2px solid #e9ecef;
        }

        .sim {
            background: #d4edda;
            color: #155724;
            font-weight: bold;
        }

        .nao {
            background: #f8d7da;
            color: #721c24;
            font-weight: bold;
        }

        tr:hover td {
            background-color: #e3f2fd;
        }

        tr:hover td:first-child {
            background-color: #bbdefb;
        }

        .stats {
            padding: 30px;
            background: #f8f9fa;
        }

        .stats h3 {
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 1.5em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stat-label {
            font-weight: 600;
            color: #495057;
        }

        .stat-value {
            font-weight: bold;
            color: #667eea;
            font-size: 1.1em;
        }

        .footer {
            padding: 20px 30px;
            background: #2c3e50;
            color: white;
            text-align: center;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Mapeamento de Ações e Funcionalidades</h1>
            <p>Sistema de Controle de Permissões por Tipo de Usuário</p>
        </div>

        <div class="info-bar">
            <div class="info-item">
                <span>📊</span>
                <span>Total de Ações: ${acoesOrdenadas.length}</span>
            </div>
            <div class="info-item">
                <span>👥</span>
                <span>Tipos de Usuário: ${tiposUsuario.length}</span>
            </div>
            <div class="info-item">
                <span>🔢</span>
                <span>Total de Permissões: ${totalPermissoes}</span>
            </div>
            <div class="info-item">
                <span>🕒</span>
                <span>Atualizado em: ${dataAtual}</span>
            </div>
        </div>

        <div class="controls">
            <input type="text" id="searchInput" class="search-box" placeholder="🔍 Buscar ação...">
            <button class="copy-btn" onclick="copyTableToClipboard()">📋 Copiar Tabela para Excel</button>
            <button class="copy-btn" onclick="downloadCSV()">💾 Baixar CSV</button>
        </div>

        <div class="table-container">
            <table id="permissionsTable">
                <thead>
                    <tr>
                        <th>Ação</th>
                        <th>SISTEMA_ADMIN</th>
                        <th>SISTEMA_FINANCEIRO</th>
                        <th>DESENVOLVEDOR</th>
                        <th>REVENDA_ADMIN</th>
                        <th>REVENDA_ASSISTENTE</th>
                        <th>SUPORTE_STI3</th>
                        <th>CANAIS_GERENTE</th>
                        <th>ANALISTA_CONTEUDO</th>
                    </tr>
                </thead>
                <tbody>
${linhasTabela}                </tbody>
            </table>
        </div>

        <div class="stats">
            <h3>📈 Estatísticas por Tipo de Usuário:</h3>
            <div class="stats-grid">
${estatisticasHTML}            </div>
        </div>

        <div class="footer">
            <p>🤖 Gerado automaticamente pelo Agente de Atualização de Permissões | ${dataAtual}</p>
        </div>
    </div>

    <script>
        // Função de busca
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const table = document.getElementById('permissionsTable');
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const firstCell = rows[i].getElementsByTagName('td')[0];
                if (firstCell) {
                    const textValue = firstCell.textContent || firstCell.innerText;
                    if (textValue.toLowerCase().indexOf(searchTerm) > -1) {
                        rows[i].style.display = '';
                    } else {
                        rows[i].style.display = 'none';
                    }
                }
            }
        });

        // Função para copiar tabela
        function copyTableToClipboard() {
            const table = document.getElementById('permissionsTable');
            const range = document.createRange();
            range.selectNode(table);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
            document.execCommand('copy');
            window.getSelection().removeAllRanges();
            alert('✅ Tabela copiada para a área de transferência!');
        }

        // Função para baixar CSV
        function downloadCSV() {
            const table = document.getElementById('permissionsTable');
            let csv = [];
            const rows = table.querySelectorAll('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');
                for (let j = 0; j < cols.length; j++) {
                    row.push(cols[j].innerText);
                }
                csv.push(row.join(','));
            }

            const csvFile = new Blob([csv.join('\\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = 'Mapeamento_Acoes_Funcionalidades.csv';
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    </script>
</body>
</html>`;

    // Salvar arquivo HTML
    const nomeArquivo = 'Mapeamento_Acoes_Funcionalidades_com_Constantes.html';
    
    try {
        fs.writeFileSync(nomeArquivo, htmlContent, 'utf8');
        console.log(`✅ Arquivo HTML criado com sucesso: ${nomeArquivo}`);
        console.log(`📊 Total de ações mapeadas: ${acoesOrdenadas.length}`);
        
        // Exibir estatísticas
        console.log('\n📈 Estatísticas por tipo de usuário:');
        Object.entries(stats).forEach(([tipo, count]) => {
            console.log(`   ${tipo}: ${count} permissões`);
        });
        
        return nomeArquivo;
    } catch (error) {
        console.error('❌ Erro ao criar arquivo HTML:', error);
        return false;
    }
}

// Executar se chamado diretamente
if (require.main === module) {
    gerarHTML();
}

module.exports = { gerarHTML };
