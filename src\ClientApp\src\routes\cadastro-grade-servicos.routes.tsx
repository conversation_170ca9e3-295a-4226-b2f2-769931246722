import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { GradeServicos } from 'pages/GradeServicos/Listar';
import { FormCadastrarGrade } from 'pages/GradeServicos/Formulario/Cadastrar';
import { FormAlterarGrade } from 'pages/GradeServicos/Formulario/Alterar';

import LayoutGuard from './LayoutGuard';

export const GradesServicosRoutes = [
  <Route
    key={ConstanteRotas.GRADE_SERVICOS}
    path={ConstanteRotas.GRADE_SERVICOS}
    element={
      <LayoutGuard
        key={ConstanteRotas.GRADE_SERVICOS}
        breadcrumb={[
          { title: 'Cadastro', path: ConstanteRotas.GRADE_SERVICOS },
          { title: 'Grade de serviços' },
        ]}
        component={<GradeServicos />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_CADASTRO_GRADE_SERVICO)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.GRADE_SERVICOS_CADASTRAR}
    path={ConstanteRotas.GRADE_SERVICOS_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.GRADE_SERVICOS_CADASTRAR}
        breadcrumb={[
          { title: 'Cadastro', path: ConstanteRotas.GRADE_SERVICOS },
          { title: 'Grade de serviços', path: ConstanteRotas.GRADE_SERVICOS },
          { title: 'Cadastrar' },
        ]}
        component={<FormCadastrarGrade />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroGradeServicoAcao.CADASTRAR_GRADE_SERVICOS)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.GRADE_SERVICOS_ALTERAR}
    path={ConstanteRotas.GRADE_SERVICOS_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.GRADE_SERVICOS_ALTERAR}
        breadcrumb={[
          { title: 'Cadastro', path: ConstanteRotas.GRADE_SERVICOS },
          { title: 'Grade de serviços', path: ConstanteRotas.GRADE_SERVICOS },
          { title: 'Alterar' },
        ]}
        component={<FormAlterarGrade />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroGradeServicoAcao.ALTERAR_GRADE_SERVICOS)}
      />
    }
  />,
];
