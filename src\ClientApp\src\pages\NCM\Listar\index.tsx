import React, { useState, useCallback } from 'react';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { Flex, Td, Tr } from '@chakra-ui/react';

import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import { formatUTCToLocateDateTime } from 'helpers/format/formatUTCToLocateDateTime';
import { formattingCodeInLabel } from 'helpers/format/formattingCodeInLabel';
import api, { ResponseApi } from 'services/api';
import { EnumEstados } from 'constants/Enum/enumEstados';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { EnumStatusImportacao } from 'constants/Enum/enumStatusImportacao';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { ButtonDefault } from 'components/Button';
import { Pagination } from 'components/Grid/Pagination';
import { ModalCadastrarImportacaoNCM } from 'components/Modal/ModalCadastrarImportacaoNCM';

interface ImportacoesNcmProps {
  id: string;
  status: number;
  versao: string;
  codigoEstado: number;
  dataHora: string;
}

export const ImportacoesNCM = () => {
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [importacoesNCM, setImportacoesNCM] = useState<ImportacoesNcmProps[]>(
    []
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<ImportacoesNcmProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_IMPORTACOES_NCM,
          gridPaginadaConsulta
        )
      );

      if (response?.sucesso) {
        setTotalRegistros(response.dados.total);
        setImportacoesNCM(response.dados.registros);
      }

      setIsLoading(false);
    },
    [recarregarListagem]
  );

  const atualizarListagem = useCallback(() => {
    setRecarregarListagem(!recarregarListagem);
  }, [recarregarListagem]);

  return (
    <Flex direction="column">
      <ButtonDefault
        onClick={() =>
          ModalCadastrarImportacaoNCM({
            atualizarListagem: atualizarListagem,
          })
        }
        width={['full', 'full', 'full', '56']}
        isLoading={isLoading}
        color="white"
        colorScheme="secondary"
        leftIcon={<IoIosAddCircleOutline />}
        alignSelf="flex-end"
        mb={['0', '17px']}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.ImportacaoNcmAcao.CADASTRAR_IMPORTACAO_NCM
        )}
      >
        Cadastrar novo
      </ButtonDefault>
      <Pagination
        loadColumnsData={paginationHandle}
        nPages={totalRegistros}
        currentPage={page}
        setCurrentPage={setPage}
        isLoading={isLoading}
        defaultOrderDirection="asc"
        defaultKeyOrdered="DataHora"
        tableHeaders={[
          {
            content: 'Data / Hora',
            key: 'DataHora',
            isOrderable: true,
            width: 'auto',
          },
          {
            content: 'Estado',
            key: 'Estado',
            isOrderable: false,
            width: 'auto',
          },
          {
            content: 'Versão',
            key: 'Versao',
            isOrderable: false,
            width: 'auto',
          },
          {
            content: 'Status',
            key: 'Status',
            isOrderable: false,
            width: 'auto',
          },
        ]}
        renderTableRows={importacoesNCM.map((importacaoNcm) => (
          <Tr key={importacaoNcm.id}>
            <Td>{formatUTCToLocateDateTime(importacaoNcm.dataHora)}</Td>
            <Td>
              {formattingCodeInLabel(EnumEstados, importacaoNcm.codigoEstado)}
            </Td>
            <Td>{importacaoNcm.versao}</Td>
            <Td>
              {formattingCodeInLabel(
                EnumStatusImportacao,
                importacaoNcm.status
              )}
            </Td>
          </Tr>
        ))}
      />
    </Flex>
  );
};
