import {
  FormControl,
  FormErrorMessage,
  FormLabel,
  Icon,
  InputGroup,
  InputLeftElement,
  InputProps,
} from '@chakra-ui/react';
import { FieldError } from 'react-hook-form';
import { IconType } from 'react-icons';
import { useCallback } from 'react';

interface FieldErrorProps extends FieldError {
  value?: {
    message?: string;
  };
}

export interface CampoPrototipoProps extends InputProps {
  label?: string;
  colorLabel?: string;
  fontLabel?: string;
  iconLeftElement?: IconType;
  error?: FieldErrorProps | undefined;
  children?: React.ReactNode;
}

export const CampoPrototipo = ({
  label,
  colorLabel = 'black',
  isRequired,
  iconLeftElement,
  error,
  fontLabel = 'xs',
  children,
}: CampoPrototipoProps) => {
  const valueError = useCallback(() => {
    if (error?.message) {
      return error?.message;
    } else if (error?.value?.message) {
      return error?.value?.message;
    }
  }, [error])();

  return (
    <FormControl isInvalid={!!error} isRequired={isRequired}>
      <InputGroup flexDirection="column">
        {iconLeftElement && (
          <InputLeftElement
            children={
              <Icon
                as={iconLeftElement}
                zIndex="0"
                color="gray.200"
                fontSize="xl"
              />
            }
          />
        )}
        {label && (
          <FormLabel id="inputLabel" fontSize={fontLabel} color={colorLabel}>
            {label}
          </FormLabel>
        )}
        {children}
      </InputGroup>
      {!!error && (
        <FormErrorMessage marginTop="2px" whiteSpace="nowrap">
          {valueError}
        </FormErrorMessage>
      )}
    </FormControl>
  );
};
