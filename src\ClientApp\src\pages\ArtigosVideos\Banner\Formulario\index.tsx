import { Box, GridItem, Text } from '@chakra-ui/react';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { InputNumber } from 'components/Input/InputChakra/InputNumber';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { CheckboxAtivoInativo } from 'components/Checkbox/CheckboxAtivoInativo';
import { ImageUploaderForm } from 'components/ImageUploader/ImageUploaderForm';

const FormBanner = () => {
  return (
    <SimpleGridForm>
      <GridItem colSpan={[12, 8, 12, 12, 8]}>
        <InputDefault
          label="Título"
          name="titulo"
          placeholder="Digite o título do banner"
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 6, 6, 2]}>
        <InputNumber
          label="Ordenação para exibição"
          name="sequenciaOrdenacao"
          scale={0}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 6, 6, 2]}>
        <CheckboxAtivoInativo label="Ativo" name="ativo" />
      </GridItem>
      <GridItem colSpan={12}>
        <Box>
          <ImageUploaderForm
            label="Imagem para exibição na tela inicial"
            name="imagem"
            isAlterarImagem
            width="100%"
            height="225px"
            maxH="225px"
            naoValidarTamanhos
          />
          <Text>
            <Text as="strong">Atenção:</Text> A imagem recomendada para o banner
            é de 1800x225px
          </Text>
        </Box>
      </GridItem>
    </SimpleGridForm>
  );
};

export default FormBanner;
