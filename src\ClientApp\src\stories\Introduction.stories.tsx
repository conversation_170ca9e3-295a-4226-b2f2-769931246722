import { Meta, StoryFn } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';
import { Image, Flex, Text } from '@chakra-ui/react';
import IconMultiEmpresa from 'assets/logo.png';

export default {
  title: 'Intro',
} as Meta;

export const Intro = () => (
  <Flex ml="80px" mt="10px" alignItems="center">
    <Image
      mb="6px"
      w="70px"
      h="70px"
      objectFit="cover"
      src={IconMultiEmpresa}
    />
    <Text
      ml="15px"
      fontFamily="Montserrat"
      fontWeight="extrabold"
      fontSize="2xl"
    >
      Stargate
    </Text>
    <style>
      @import
      url('https://fonts.googleapis.com/css2?family=Montserrat:wght@700&family=Signika:wght@700&display=swap');
    </style>
  </Flex>
);
