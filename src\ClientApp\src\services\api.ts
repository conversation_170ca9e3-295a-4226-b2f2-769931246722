import { toast } from 'react-toastify';

import auth from 'modules/auth';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import axios, { AxiosHeaders } from 'axios';

export interface ResponseApi<T = unknown> {
  sucesso: boolean;
  avisos: Array<string>;
  erros: Array<string>;
  dados: T;
}

type FailedRequestList = {
  onResolve(token: string): void;
  onReject(error: any): void;
};

let isMakingTheRequest = false;
let failedRequestList: FailedRequestList[] = [];

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  headers: { 'Access-Control-Allow-Origin': '*' },
});

api.interceptors.response.use(
  (response) => {
    if (response) {
      if (response.data.avisos) {
        response.data.avisos.forEach((item: string) => toast.warning(item));
      }

      return response.data;
    }
  },

  async (error) => {
    try {
      const originalRequest = error.config;
      if (!originalRequest.retry && error.response?.status === 401) {
        originalRequest.retry = true;

        if (!isMakingTheRequest) {
          isMakingTheRequest = true;

          return api
            .put<void, ResponseApi>(
              ConstantEnderecoWebservice.AUTENTICACAO_REFRESH_TOKEN,
              {
                token: auth.getToken(),
                refreshToken: auth.getRefreshToken(),
              }
            )
            .then(async (res) => {
              if (res.sucesso && res.dados) {
                auth.setToken(res.dados);
                const token = await auth.getToken();
                api.defaults.headers.common.Authorization = `Bearer ${token}`;
                failedRequestList.forEach((req) => {
                  req.onResolve(auth.getToken());
                });
                failedRequestList = [];
              } else {
                // refresh token inválido.
                auth.clearTokenAndRedirect();
              }
            })
            .catch((err) => {
              failedRequestList.forEach((req) => {
                req.onReject(err);
                failedRequestList = [];
              });
            })
            .finally(() => {
              isMakingTheRequest = false;
            });
        }

        return new Promise((success, onReject) => {
          failedRequestList.push({
            onResolve: (token) => {
              api.defaults.headers.common.Authorization = `Bearer ${token}`;
              success(api(originalRequest));
            },
            onReject: (err) => {
              onReject(err);
            },
          });
        });
      }

      if (error.response?.status === 423 && !originalRequest.retry) {
        originalRequest.retry = true;

        auth.clearTokenAndRedirect();

        return {
          data: {
            sucesso: false,
          },
        };
      }

      if (error.response?.status === 405 && !originalRequest.retry) {
        originalRequest.retry = true;

        toast.warning(
          'O seu usuário não possui permissão para utilizar essa funcionalidade.'
        );
        return api(originalRequest);
      }

      if (
        !originalRequest.retry ||
        error.response?.status === 400 ||
        (error.response?.status >= 500 && error.response?.status <= 510)
      ) {
        originalRequest.retry = true;
        toast.error('Ocorreu um erro inesperado.');

        return {
          data: {
            sucesso: false,
          },
        };
      }

      return null;
    } catch (err) {
      toast.error('Ocorreu um erro inesperado.');

      return {
        data: {
          sucesso: false,
        },
      };
    }
  }
);

api.interceptors.request.use((config) => {
  const token = auth.getToken();
  const configNew = config;
  if (configNew?.headers)
    if (configNew?.headers) {
      (configNew.headers as AxiosHeaders)?.set(
        'Access-Control-Allow-Origin',
        '*'
      );
    }
  if (configNew?.headers) {
    (configNew.headers as AxiosHeaders)?.set(
      'Authorization',
      `Bearer ${token}`
    );
  }
  return configNew;
});

export default api;
