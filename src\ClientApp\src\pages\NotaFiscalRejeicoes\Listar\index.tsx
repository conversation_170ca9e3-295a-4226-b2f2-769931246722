import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { toast } from 'react-toastify';
import { Flex, Link, Td, Tr } from '@chakra-ui/react';

import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import api, { ResponseApi } from 'services/api';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { ActionsMenu } from 'components/ActionsMenu';
import { ButtonDefault } from 'components/Button';
import { Pagination } from 'components/Grid/Pagination';
import { ModalWarning } from 'components/Modal/ModalWarning';

interface NotaFiscalRejeicoesProps {
  id: string;
  codigo: string;
  rejeicao: string;
  link: string;
}

export const NotaFiscalRejeicoes = () => {
  const navigate = useNavigate();

  const [totalRegistros, setTotalRegistros] = useState(0);
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [notaFiscalRejeicoes, setNotaFiscalRejeicoes] = useState<
    NotaFiscalRejeicoesProps[]
  >([]);

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<NotaFiscalRejeicoesProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_NF_REJEICOES,
          gridPaginadaConsulta
        )
      );

      if (response?.sucesso) {
        setTotalRegistros(response.dados.total);
        setNotaFiscalRejeicoes(response.dados.registros);
      }

      setIsLoading(false);
    },
    [recarregarListagem]
  );

  const handleCadastrarNotaFiscalRejeicao = () => {
    navigate(ConstanteRotas.NOTAFISCAL_REJEICOES_CADASTRAR);
  };

  const handleExcluirNotaFiscalRejeicao = useCallback(
    async (id: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'A ação irá excluir a nota fiscal rejeição!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            ConstantEnderecoWebservice.EXCLUIR_NF_REJEICAO,
            { params: { id } }
          );

          if (response.sucesso) {
            setRecarregarListagem(!recarregarListagem);
            toast.success('Cadastro excluído com sucesso.');
            return true;
          }
          return false;
        },
      });
    },
    [recarregarListagem]
  );

  return (
    <Flex direction="column">
      <ButtonDefault
        onClick={() => handleCadastrarNotaFiscalRejeicao()}
        width={['full', 'full', 'full', '56']}
        isLoading={isLoading}
        color="white"
        colorScheme="secondary"
        leftIcon={<IoIosAddCircleOutline />}
        alignSelf="flex-end"
        mb={['0', '17px']}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.NotaFiscalRejeicaoAcao.CADASTRAR_NF_REJEICAO
        )}
      >
        Cadastrar novo
      </ButtonDefault>
      <Pagination
        loadColumnsData={paginationHandle}
        nPages={totalRegistros}
        currentPage={page}
        setCurrentPage={setPage}
        isLoading={isLoading}
        defaultOrderDirection="asc"
        defaultKeyOrdered="Codigo"
        tableHeaders={[
          {
            content: 'Código',
            key: 'Codigo',
            isOrderable: true,
            w: '50%',
          },
          {
            content: 'Rejeição',
            key: 'Rejeicao',
            isOrderable: false,
            width: '25%',
          },
          {
            content: 'Link',
            key: 'Link',
            isOrderable: false,
            width: '25%',
          },
          {
            content: 'Ações',
            key: 'acoes',
            isOrderable: false,
          },
        ]}
        renderTableRows={notaFiscalRejeicoes.map((notaFiscalRejeicao) => (
          <Tr key={notaFiscalRejeicao.id}>
            <Td>{notaFiscalRejeicao.codigo}</Td>
            <Td>{notaFiscalRejeicao.rejeicao}</Td>
            <Td>
              <Link
                href={notaFiscalRejeicao.link}
                target="_blank"
                rel="noreferrer"
                color="blue.500"
              >
                {notaFiscalRejeicao.link}
              </Link>
            </Td>
            <Td>
              <Flex>
                <ActionsMenu
                  id="mostrarMais"
                  items={[
                    {
                      content: 'Alterar',
                      onClick: () => {
                        navigate(
                          SubstituirParametroRota(
                            ConstanteRotas.NOTAFISCAL_REJEICOES_ALTERAR,
                            'id',
                            notaFiscalRejeicao.id
                          )
                        );
                      },

                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.NotaFiscalRejeicaoAcao
                          .ALTERAR_NF_REJEICAO
                      ),
                    },

                    {
                      content: 'Excluir',
                      onClick: () => {
                        handleExcluirNotaFiscalRejeicao(notaFiscalRejeicao.id);
                      },
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.NotaFiscalRejeicaoAcao
                          .EXCLUIR_NF_REJEICAO
                      ),
                    },
                  ]}
                />
              </Flex>
            </Td>
          </Tr>
        ))}
      />
    </Flex>
  );
};
