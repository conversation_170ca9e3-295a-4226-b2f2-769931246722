import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FiSearch } from 'react-icons/fi';
import { Box, Flex, Td, Tr, Icon, Link } from '@chakra-ui/react';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { EnumStatusCadastros } from 'constants/Enum/enumStatusCadastros';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';

import { ActionsMenu } from 'components/ActionsMenu';
import { ButtonDefault } from 'components/Button';
import { ContainerListagem } from 'components/Grid/GridList';
import { Pagination } from 'components/Grid/Pagination';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SelectDefault } from 'components/Select/SelectDefault';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';

import {
  GradeProps,
  FormData,
  formListagemDefaultValues,
} from './validationForms';

export const GradeServicos = () => {
  const [page, setPage] = useState(1);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [listaGrades, setListaGrades] = useState<GradeProps[]>([]);
  const [currentFilters, setCurrentFilters] = useState<FormData>(
    formListagemDefaultValues
  );

  const formMethods = useForm<FormData>({
    defaultValues: formListagemDefaultValues,
  });
  const { watch, reset } = formMethods;
  const nomeDaGradeWatch = watch('nome');

  const navigate = useNavigate();

  const possuiPermissaoAlterarGrade = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.CadastroGradeServicoAcao.ALTERAR_GRADE_SERVICOS
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<GradeProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_GRADE_SERVICOS,
          gridPaginadaConsulta
        ),
        {
          params: currentFilters,
        }
      );

      if (response.sucesso) {
        setTotalRegistros(response.dados.total);
        setListaGrades(response.dados.registros);
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const handleExcluirgrade = useCallback(
    async (id: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'A ação vai excluir essa grade!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            ConstantEnderecoWebservice.EXCLUIR_GRADE_SERVICOS,
            { params: { id } }
          );

          if (response.sucesso) {
            setCurrentFilters((filtrosJaAdicionados) => ({
              ...filtrosJaAdicionados,
              ativo: null,
            }));
            reset(formListagemDefaultValues);
            setRecarregarListagem(!recarregarListagem);
            toast.success('Cadastro excluído com sucesso.');
            return true;
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );

  const handleAlterarGrade = useCallback(
    (gradeId: string) => {
      if (!possuiPermissaoAlterarGrade) return;
      navigate(
        SubstituirParametroRota(
          ConstanteRotas.GRADE_SERVICOS_ALTERAR,
          'id',
          gradeId
        )
      );
    },
    [possuiPermissaoAlterarGrade]
  );

  return (
    <Box>
      <FormProvider {...formMethods}>
        <ContainerListagem
          inputPesquisa={
            <InputDefault
              maxLength={100}
              placeholder="Buscar por nome"
              iconLeftElement={FiSearch}
              name="nome"
              onEnterKeyPress={() => {
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  nome: nomeDaGradeWatch,
                }));
              }}
            />
          }
          filtrosListagem={
            <SelectDefault
              name="ativo"
              placeholder="Selecione um filtro"
              filtrosAtivos
              onSelect={(optionSelecionada) =>
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  ativo: optionSelecionada?.value,
                }))
              }
              options={EnumStatusCadastros.properties.map((status) => status)}
            />
          }
          buttonCadastrar={
            <ButtonDefault
              onClick={() => navigate(ConstanteRotas.GRADE_SERVICOS_CADASTRAR)}
              width={['full', 'full', 'full', '220px']}
              color="white"
              colorScheme="secondary"
              leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
              possuiFuncionalidade={auth.usuarioPossuiPermissao(
                ConstantFuncionalidades.CadastroGradeServicoAcao
                  .CADASTRAR_GRADE_SERVICOS
              )}
            >
              Cadastrar novo
            </ButtonDefault>
          }
        >
          <Pagination
            loadColumnsData={paginationHandle}
            nPages={totalRegistros}
            currentPage={page}
            setCurrentPage={setPage}
            isLoading={isLoading}
            defaultOrderDirection="asc"
            defaultKeyOrdered="ativo"
            tableHeaders={[
              {
                content: <StatusCircle hasValue={false} />,
                key: 'ativo',
                isOrderable: false,
                width: '1px',
              },
              {
                content: 'Nome',
                key: 'nome',
                width: 'auto',
              },
              {
                content: 'Ações',
                key: 'Acoes',
                isOrderable: false,
                width: '10px',
              },
            ]}
            renderTableRows={listaGrades.map((grade) => (
              <Tr key={grade.id}>
                <Td>
                  <StatusCircle isActive={grade.ativo} />
                </Td>
                <Td pb="3px" pt="3px">
                  <Flex w="full">
                    <Link
                      lineHeight="12.5px"
                      maxW="full"
                      whiteSpace="pre-line"
                      cursor="pointer"
                      onClick={() => handleAlterarGrade(grade.id)}
                    >
                      {grade.nome}
                    </Link>
                  </Flex>
                </Td>
                <Td>
                  <Flex justifyContent="right">
                    <ActionsMenu
                      id="gradeAcoes"
                      items={[
                        {
                          content: 'Editar',
                          onClick: () => {
                            handleAlterarGrade(grade.id);
                          },
                          possuiFuncionalidade: auth.usuarioPossuiPermissao(
                            ConstantFuncionalidades.CadastroGradeServicoAcao
                              .ALTERAR_GRADE_SERVICOS
                          ),
                        },
                        {
                          content: 'Excluir',
                          onClick: () => {
                            handleExcluirgrade(grade.id);
                          },
                          possuiFuncionalidade: auth.usuarioPossuiPermissao(
                            ConstantFuncionalidades.CadastroGradeServicoAcao
                              .EXCLUIR_GRADE_SERVICOS
                          ),
                        },
                      ]}
                    />
                  </Flex>
                </Td>
              </Tr>
            ))}
          />
        </ContainerListagem>
      </FormProvider>
    </Box>
  );
};
