import { Meta, StoryFn } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';

import { theme } from 'theme';

import { InputPhone as Input } from './exampleInput';
import { InputPhoneProps } from '.';

export default {
  title: 'Components/Input',
  argTypes: getThemingArgTypes(theme as any, 'Input'),
  args: {
    name: 'storybookInput',
    placeholder: 'Insira o valor aqui',
    label: 'Label',
    colorLabel: 'pink.500',
  },
} as Meta;

export const InputPhone: StoryFn<InputPhoneProps> = (props) => {
  return (
    <Input
      colorLabel={props.colorLabel}
      label={props.label}
      w={props.size}
      variant={props.variant}
      placeholder={props.placeholder}
    />
  );
};
