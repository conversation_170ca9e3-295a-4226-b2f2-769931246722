import cypress from 'cypress';

describe('testing login', () => {
  it('testing login screen functionality', () => {
    cy.visit('/');

    return cy.fixture('userAutenticacao').then(async ({ usuario, senha }) => {
      cy.get('#usuario').type(usuario);
      cy.get('#senha').type(senha);
      cy.get('#button').dblclick();

      const valuesAutenticacao = {
        usuario,
        senha,
      };

      cy.apiAutenticacao(valuesAutenticacao).then((response) => {
        expect(response.status).to.equal(200);
      });

      it('testing if the login screen went to the home route', () => {
        cy.location().then(({ pathname }) => {
          expect(pathname).equal('/home');
        });
      });
    });
  });
});
