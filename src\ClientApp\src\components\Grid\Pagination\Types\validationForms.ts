export interface GridPaginadaRetorno<T = unknown> {
  total: number;
  registros: Array<T>;
}

export type PagedTableForwardRefData = {
  reload: () => Promise<void>;
};
export interface GridPaginadaConsulta {
  currentPage: number;
  pageSize: number;
  orderColumn: string;
  orderDirection: string;
}

export type SelectResultPageProps = {
  label: string;
  value: number;
};

export type PaginationData = {
  currentPage: number;
  pageSize: number;
  orderColumn: string;
  orderDirection: 'asc' | 'desc';
};
