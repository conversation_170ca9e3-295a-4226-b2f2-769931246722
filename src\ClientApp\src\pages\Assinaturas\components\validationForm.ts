import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';
import * as yup from 'yup';

export type FormData = {
  revenda: {
    value: string;
    label: string;
  } | null;
};

const schema = yup.object().shape({
  revenda: yup
    .object()
    .nullable()
    .shape({
      value: yup.string(),
      label: yup.string(),
    })
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

export const yupResolver = yupResolverInstance(schema);
