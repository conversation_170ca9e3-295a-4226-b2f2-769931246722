import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { Atualizacao } from 'pages/Atualizacao/Listar';
import { GerenciarAtualizacao } from 'pages/Atualizacao/Gerenciar';

import LayoutGuard from './LayoutGuard';

export const AtualizacaoRoutes = [
  <Route
    key={ConstanteRotas.ATUALIZACAO}
    path={ConstanteRotas.ATUALIZACAO}
    element={
      <LayoutGuard
        key={ConstanteRotas.ATUALIZACAO}
        breadcrumb={[
          { title: 'Atualização', path: ConstanteRotas.ATUALIZACAO },
        ]}
        component={<Atualizacao />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_ATUALIZACAO)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.ATUALIZACAO_GERENCIAR}
    path={ConstanteRotas.ATUALIZACAO_GERENCIAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.ATUALIZACAO_GERENCIAR}
        breadcrumb={[
          { title: 'Atualização', path: ConstanteRotas.ATUALIZACAO },
          { title: 'Gerenciar' },
        ]}
        component={<GerenciarAtualizacao />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AtualizacaoAcao.VISUALIZAR_GERENCIAR_ATUALIZACAO)}
      />
    }
  />,
];
