export interface PlanoProps {
  label: string;
  color: string;
  value: number;
}

const planosEnum = {
  START: 0,
  PRO: 1,
  PRIME: 2,
  SMARTPOS: 3,

  properties: [
    { label: 'START', color: 'yellow.600', value: 0 },
    { label: 'PRO', color: 'indigo.300', value: 1 },
    { label: 'PRIME', color: 'aquamarine.500', value: 2 },
    { label: 'SMART POS', color: 'blue.500', value: 3 },
  ],

  options: [
    { value: 0, label: 'START' },
    { value: 1, label: 'PRO' },
    { value: 2, label: 'PRIME' },
    { value: 3, label: 'SMARTPOS' },
  ],

  optionsTodos: [
    { value: null, label: 'Todos os planos' },
    { value: 'START', label: 'START' },
    { value: 'PRO', label: 'PRO' },
    { value: 'PRIME', label: 'PRIME' },
    { value: 'SMARTPOS', label: 'SMART POS' },
  ],
};

export default planosEnum;
