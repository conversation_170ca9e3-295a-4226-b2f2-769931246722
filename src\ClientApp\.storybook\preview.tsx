import { ChakraProvider } from '@chakra-ui/react';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';

import { theme } from '../src/theme/index';
import FullScreenProvider from '../src/store/FullScreen';

export const parameters = {
  actions: { argTypesRegex: '^on[A-Z].*' },
  chakra: {
    theme,
  },
  controls: {
    matchers: {
      color: /(background|color|colorLabel)$/i,
      date: /Date$/,
    },
  },
};

export const decorators = [
  (Story) => (
    <ChakraProvider theme={theme}>
      <FullScreenProvider>
        <BrowserRouter>
          <Story />
        </BrowserRouter>
      </FullScreenProvider>
    </ChakraProvider>
  ),
];
