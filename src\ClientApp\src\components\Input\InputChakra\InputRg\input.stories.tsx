import { Meta, StoryFn } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';

import { theme } from 'theme';

import { InputRg as Input } from './exampleInput';
import { InputRgProps } from '.';

export default {
  title: 'Components/Input',
  argTypes: getThemingArgTypes(theme as any, 'Input'),
  args: {
    name: 'storybookInput',
    isCnpj: true,
    maxLength: 15,
  },
} as Meta;

export const InputRg: StoryFn<InputRgProps> = (props) => {
  return (
    <Input
      label={props.isCnpj ? 'Inscrição estadual' : undefined}
      placeholder={props.isCnpj ? 'Informe a inscrição estadual' : undefined}
      {...props}
    />
  );
};
