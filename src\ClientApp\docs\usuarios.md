# Informações sobre os usuarios do Stargate

O Stargate é composto por 5 tipos de usuários diferentes com cada um possuindo permissões de acesso a rotas ou ações dentro
do sistema.

## Funcionalidades

As funcionalidades vão desde um usuário poder visualizar uma listagem até alterar e cadastrar algo no sistema.

Elas estão separadas por grupos na pasta Funcionalidades para facilitar a organização.

Os grupos são formados por `admRevenda, admSistema, desenvolvedor, gerenteRevendaAdmRevenda, assistenteRevenda e gerenteRevendas`, cada grupo possui funcionalidades disponiveis somente para os tipos de usuários desse grupo. Abaixo um exemplo de dois grupos diferentes.

```js
export const Desenvolvedor = {
  ATUALIZAR_GERENCIAR_ATUALIZACAO: [
    EnumTipoUsuario.ADM_SISTEMA,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
};
```

```js
export const AdmRevenda = {
  CADASTRAR_VENDEDORES: [
    EnumTipoUsuario.ADM_SISTEMA,
    EnumTipoUsuario.ADM_REVENDA,
  ],
};
```

Perceba que o grupo com o Desenvolvedor possui a funcionalidade ATUALIZAR_GERENCIAR_ATUALIZACAO que está disponível somente para o usuário ADM_SISTEMA e DESENVOLVEDOR

Por padrão toda funcionalidade está disponível ao usuário ADM_SISTEMA, uma funcionalidade pode estar somente em um grupo e disponível somente aos usuários daquele grupo.

Para adicionar uma nova funcionalidade ao sistema precisamos adiciona-la ao grupo do qual ela faz parte na pasta de Funcionalidades e informar os tipos de usuários que terão permissão para usá-la.

Exemplo de uma funcionalidade sendo adicionada ao grupo Desenvolvedor:

```js
export const Desenvolvedor = {
  ATUALIZAR_GERENCIAR_ATUALIZACAO: [
    EnumTipoUsuario.ADM_SISTEMA,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
  NOVA_FUNCIONALIDADE: [
    EnumTipoUsuario.ADM_SISTEMA,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
};
```

## Tipos de usuário

Os tipos de usuário são representados pelo [EnumTipoUsuario](../src/constants/Enum/enumTipoUsuario.ts) e tem números correspondentes para cada tipo

0 - AdmSistema: Tem acesso a todas as revendas e pode cadastrar planos

1 - GerenteRevendas: Tem permissão para adicionar e visualizar as revendas no sistema, e pode também cadastrar tabela de preço

2 - Desenvolvedor: Tem permissão para adicionar novas versões, importar ncm, (cadastrar/importar/visualizar => oq pode ser feito com o parâmetros) parâmetros fiscais entre outras ações

3 - AdmRevenda: Tem acesso ao cadastro dos clientes e pode editar as informações da tabela de preço e dos vendedores da revenda

4 - Vendedor: Possui permissão para cadastrar novos clientes e pode também montar os pacotes de contratação

Esse número é o que será retornado pelo token de autenticação.

## Funções auxiliares

Caso precise do número ou nome do tipo de usuário pode usar as seguintes funções

- Essa função retorna o numero equivalente ao tipo de usuário logado

```js
const tipoUsuarioNumero = auth.getTipoUsuarioValue();
```

- Essa função retorna o nome equivalente ao tipo de usuário logado

```js
const tipoUsuarioNome = auth.getTipoUsuarioName();
```

## Criando uma nova tela e validando as permissões que ela possui

Caso esteja criando uma nova tela, se atente a esses passos:

- Passo 1: Validação da URL (ConstantRota)
- Passo 2: Validação de Menu e Submenu
- Passo 3: Validação do menu de Ações da listagem (-- opcional nem toda listagem tem um menu de ações)
- Passo 4: Validação dos botões

Todas validações são feitas usando a mesma função, a função espera uma funcionalidade como parametro
e verifica se aquele usuário está incluso no array de usuários dessa funcionalidade.

```js
usuarioPossuiPermissao(funcionalidade: number[]): boolean {
    const tipoDeUsuario = this.getTipoUsuarioValue();
    return funcionalidade.includes(tipoDeUsuario);
  },
```

Abaixo deixarei exemplos de uso da função para os 4 casos citados acima

### Validando URL

Ao criar uma nova rota você deve passar a propriedade [possuiFuncionalidade], está propriedade aceita um valor
booleano que caso seja [true] o usuário tem permissão para acessar essa rota e caso seja [false] ele é
redirecionado para a tela Home.

```js
 <Route
    key={ConstanteRotas.NOME_DA_ROTA}
    path={ConstanteRotas.NOME_DA_ROTA}
    element={
      <LayoutGuard
        key={ConstanteRotas.NOME_DA_ROTA}
        breadcrumb={[
          { title: 'Rota principal' },
          { title: 'Rota secundária', path: ConstanteRotas.NOME_DA_ROTA },
        ]}
        component={<Component/>}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.GRUPO_QUAL_ELA_PERTENCE.NOME_DA_FUNCIONALIDADE
        )}
      />
    }
  />,
```

Ao passarmos nossa função ela faz a validação da funcionalidade e retorna true ou false de acordo com as permissões desse
usuário.

```js
auth.usuarioPossuiPermissao(
  ConstantFuncionalidades.GRUPO_QUAL_ELA_PERTENCE.NOME_DA_FUNCIONALIDADE
);
```

### Validando Menu e Submenu

Para adicionar um novo item ao menu iremos adiciona-lo ao arquivo a seguir
[ContentItemMenu](src\components\Layout\Menu\SubMenu\NavContantItemMenu\index.tsx)

Neste exemplo temos nosso item do menu e os itens do submenu sendo verificadas pela mesma função usada nas rotas.

```js
<ContentItemMenu
  hasItemContent
  keyMenu="exemplo"
  hasDisplayIcon={hasDisplayIcon}
  iconMenu={GiHandTruck}
  valueItemContent={[
    {
      title: 'Item do submenu 1',
      keyMenu: 'exemplo',
      colorHover: 'gray.100',
      onClick: () => {
        navigate(ConstanteRotas.NOME_DA_ROTA);
      },
      possuiFuncionalidade: auth.usuarioPossuiPermissao(
        ConstantFuncionalidades.GRUPO_QUAL_ELA_PERTENCE.NOME_DA_FUNCIONALIDADE
      ),
    },
    {
      title: 'Item do submenu 2',
      keyMenu: 'exemplo',
      colorHover: 'gray.100',
      onClick: () => {
        navigate(ConstanteRotas.NOME_DA_ROTA);
      },
      possuiFuncionalidade: auth.usuarioPossuiPermissao(
        ConstantFuncionalidades.GRUPO_QUAL_ELA_PERTENCE.NOME_DA_FUNCIONALIDADE
      ),
    },
  ]}
  possuiFuncionalidade={auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.GRUPO_QUAL_ELA_PERTENCE.NOME_DA_FUNCIONALIDADE
  )}
>
  Item do menu
</ContentItemMenu>
```

Dessa forma as opções do menu irão ficar visíveis de acordo com a permissão do usuário.

### Validando menu de ações em listagens

Nas listagens é possível inserir algumas ações como: Cadastrar novos dados, alterar tais dados e excluir essas informações também.
Isso pode variar de acordo com o que a tela necessite, então para validar essa funcionalidade utilizamos as permissões.

Neste exemplo passamos a nossa função para verificar a funcionalidade como uma propriedade de cada item do menu

```js
<ActionsMenu
  id="exemplo"
  items={[
    {
      content: 'Editar',
      onClick: () => {
        navigate(
          SubstituirParametroRota(
            ConstanteRotas.ROTA_PARA_ALTERAR,
            'id',
            exemplo.id
          )
        );
      },
      possuiFuncionalidade: auth.usuarioPossuiPermissao(
        ConstantFuncionalidades.GRUPO_QUAL_ELA_PERTENCE.ALTERAR_EXEMPLO
      ),
    },
    {
      content: 'Excluir',
      onClick: () => {
        handleExcluir(exemplo.id);
      },
      possuiFuncionalidade: auth.usuarioPossuiPermissao(
        ConstantFuncionalidades.GRUPO_QUAL_ELA_PERTENCE.EXCLUIR_EXEMPLO
      ),
    },
  ]}
/>
```

Comparamos se ele tem permissões para excluir e alterar as informações da listagem, caso ele não tenha as opções não aparecem
no menu de ações.

### Validando botões

Além do menu de ações podemos ter botões de cadastro,exclusão ou atualização que só estarão disponiveis para alguns
usuários.

Neste exemplo usamos a função para verificar a funcionalidade de cadastrar e caso ele não tenha permissão o botão
de cadastro não aparece na tela.

```js
<ButtonDefault
  onClick={() => navigate(ConstanteRotas.EXEMPLO_CADASTRAR)}
  width={['full', 'full', 'full', '220px']}
  color="white"
  colorScheme="secondary"
  leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
  possuiFuncionalidade={auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.GRUPO_QUAL_ELA_PERTENCE.CADASTRAR_EXEMPLO
  )}
>
  Cadastrar
</ButtonDefault>
```
