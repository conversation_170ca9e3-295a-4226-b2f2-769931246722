import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import api, { ResponseApi } from 'services/api';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormNotaFiscalValidacoes } from '..';
import { formDefaultValues, FormData, yupResolver } from '../validationForms';

export const CadastrarNotaFiscalValidacoes = () => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });

  const { handleSubmit, reset } = formMethods;

  const navigate = useNavigate();

  const getNotaFiscalValidacoes = useCallback(async (data: FormData) => {
    const response = await api.post<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.CADASTRAR_NF_VALIDACAO,
      {
        descricao: data.descricao,
        ativo: true,
      }
    );
    return response;
  }, []);

  const handleCadastrarNotaFiscal = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await getNotaFiscalValidacoes(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      navigate(ConstanteRotas.NOTA_FISCAL_VALIDACOES);
    }

    setIsLoading(false);
  });

  const handleCadastrarInserirNovoNotaFiscal = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await getNotaFiscalValidacoes(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      reset(formDefaultValues);
    }

    setIsLoading(false);
  });

  return (
    <LayoutFormPage
      onSubmit={() => handleCadastrarNotaFiscal()}
      onResetSubmit={() => handleCadastrarInserirNovoNotaFiscal()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormNotaFiscalValidacoes />
      </FormProvider>
    </LayoutFormPage>
  );
};
