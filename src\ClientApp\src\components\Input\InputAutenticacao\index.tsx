import {
  FormLabel,
  Input,
  FormControl,
  InputProps,
  InputGroup,
  InputRightAddon,
  IconButton,
  Icon,
  TypographyProps,
  TooltipProps,
  Tooltip,
} from '@chakra-ui/react';
import { useState } from 'react';
import { Controller } from 'react-hook-form';
import { FiEyeOff, FiEye } from 'react-icons/fi';

import { useFullScreenContext } from 'store/FullScreen';

export interface InputDefaultProps extends InputProps {
  name: string;
  label?: string;
  colorLabel?: string;
  isPassword?: boolean;
  colorScheme?: string;
  errorText?: string;
  tooltipErrorProps?: Omit<TooltipProps, 'children'>;
  labelFontSize?: TypographyProps['fontSize'];
  considerColorInFocus?: boolean;
}

export const InputAutenticacao = ({
  name,
  label,
  isDisabled,
  variant,
  colorLabel = 'black',
  labelFontSize = 'xs',
  considerColorInFocus,
  tooltipErrorProps,
  isRequired,
  isPassword,
  colorScheme = 'aquamarine.400',
  bg = 'white',
  color = 'white',
  focusBorderColor = 'aquamarine.400',
  errorBorderColor = 'red.300',
  isInvalid,
  ...rest
}: InputDefaultProps) => {
  const [textIsShowing, setTextIsShowing] = useState(true);
  const handleTogglePasswordVisibility = () => {
    setTextIsShowing(!textIsShowing);
  };
  const [isFocus, setIsFocus] = useState(false);

  const { handleFullScreen } = useFullScreenContext();

  return (
    <Controller
      name={name}
      render={({
        field: { onChange, onBlur, value, name },
        fieldState: { error },
      }) => {
        const hasValue = !!value;
        return (
          <Tooltip
            isDisabled={!isInvalid}
            aria-label={error?.message}
            label={error?.message}
            hasArrow
            portalProps={{ containerRef: handleFullScreen.node }}
            textAlign="left"
            placement="bottom-start"
            p={6}
            fontSize="sm"
            fontWeight="normal"
            borderRadius="base"
            maxW="full"
            bg="red.300"
            color="white"
            {...tooltipErrorProps}
          >
            <FormControl
              position="relative"
              variant={variant}
              isInvalid={!!error}
              isRequired={isRequired}
              sx={{
                'input:-webkit-autofill ~ label': {
                  top: !isFocus && !hasValue ? '1px' : '0',
                },
              }}
            >
              <InputGroup size="sm">
                <Input
                  {...rest}
                  isDisabled={isDisabled}
                  onFocus={() => {
                    setIsFocus(true);
                  }}
                  onBlur={() => {
                    setIsFocus(false);
                    onBlur();
                  }}
                  bg={bg}
                  autoComplete="off"
                  _autofill={{
                    border: `1px solid ${colorScheme}`,
                    textFillColor: 'white',
                    boxShadow: '0 0 0px 1000px transparent inset',
                    transition: 'background-color 5000s ease-in-out 0s',
                  }}
                  zIndex="9999"
                  placeholder=" "
                  _focusVisible={{
                    borderColor: `${colorScheme}`,
                  }}
                  _focus={{
                    borderColor: isInvalid
                      ? errorBorderColor
                      : focusBorderColor,
                  }}
                  type={textIsShowing && isPassword ? 'password' : undefined}
                  color={color}
                  errorBorderColor={errorBorderColor}
                  fontSize="md"
                  variant="flushed"
                  onChange={onChange}
                  checked={value}
                  name={name}
                  id={name}
                />

                {label && (
                  <FormLabel
                    htmlFor={name}
                    position="absolute"
                    color={!!error ? errorBorderColor : colorLabel}
                    fontSize="xs"
                    fontWeight="normal"
                    m={0}
                    mr={hasValue ? 3 : 0}
                    transform={
                      hasValue ? 'translateY(-100%)' : 'translateY(0%)'
                    }
                    _focus={{
                      color: isInvalid
                        ? errorBorderColor
                        : considerColorInFocus
                        ? colorLabel
                        : focusBorderColor,
                      fontSize: labelFontSize,
                      mr: 3,
                      transform: 'translateY(-100%)',
                    }}
                  >
                    {label}
                  </FormLabel>
                )}

                {isPassword && (
                  <InputRightAddon
                    bg="none"
                    display="flex"
                    justifyContent="flex-end"
                    border="none"
                    position="absolute"
                    w="full"
                    borderRight="none"
                    children={
                      <IconButton
                        variant=""
                        borderRadius="md"
                        aria-label="Alterar visibilidade da senha"
                        icon={
                          <Icon
                            as={textIsShowing ? FiEye : FiEyeOff}
                            fontSize="lg"
                          />
                        }
                        size="sm"
                        bg="none"
                        zIndex="9999"
                        color="white"
                        id="isPasswordVisibility"
                        onClick={handleTogglePasswordVisibility}
                        tabIndex={-1}
                      />
                    }
                  />
                )}
              </InputGroup>
            </FormControl>
          </Tooltip>
        );
      }}
    />
  );
};
