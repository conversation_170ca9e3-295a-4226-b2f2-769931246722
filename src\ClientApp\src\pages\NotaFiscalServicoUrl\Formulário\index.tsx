import { GridItem } from '@chakra-ui/react';

import { EnumModelosDocumento } from 'constants/Enum/enumModeloDocRegraFiscal';
import { EnumNotaFiscalServicos } from 'constants/Enum/enumNotaFiscalServicos';
import { EnumNotaFiscalAutorizador } from 'constants/Enum/enumNotaFiscalAutorizador';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SelectDefault } from 'components/Select/SelectDefault';

export const FormNotaFiscalServicoURL = () => {
  return (
    <SimpleGridForm>
      <GridItem colSpan={[12, 12, 4]}>
        <SelectDefault
          label="Autorizador"
          name="notaFiscalAutorizador"
          isRequired
          closeMenuOnSelect
          options={EnumNotaFiscalAutorizador.map((autorizador) => autorizador)}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <SelectDefault
          label="Serviço"
          name="servico"
          isRequired
          closeMenuOnSelect
          options={EnumNotaFiscalServicos.map((servico) => servico)}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <SelectDefault
          label="Modelo"
          name="modeloNf"
          isRequired
          closeMenuOnSelect
          options={EnumModelosDocumento}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 6]}>
        <InputDefault
          type="text"
          fontLabel="xs"
          name="versaoWebService"
          placeholder="Informe a versão do webservice"
          label="Versão do WebService"
          isRequired
          maxLength={5}
        />
      </GridItem>

      <GridItem colSpan={[12, 12, 6]}>
        <InputDefault
          name="versaoServico"
          placeholder="Informe a versão do serviço"
          label="Versão do Serviço"
          isRequired
          maxLength={5}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <InputDefault
          fontLabel="xs"
          name="urlProducao"
          placeholder="Informe a URL de produção"
          label="URL de Produção"
          isRequired
          maxLength={100}
        />
      </GridItem>
      <GridItem colSpan={[12, 12, 4]}>
        <InputDefault
          fontLabel="xs"
          name="urlHomologacao"
          placeholder="Informe a URL de homologação"
          label="URL de Homologação"
          isRequired
          maxLength={100}
        />
      </GridItem>
    </SimpleGridForm>
  );
};
