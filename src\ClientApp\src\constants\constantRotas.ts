export const ConstanteRotas = {
  LOGIN: '/',
  RECUPERAR_SENHA: '/recuperar-senha',
  REDEFINIR_SENHA: '/redefinir-senha',
  SENHA_ENVIADA_COM_SUCESSO: '/senha-enviada-sucesso/:email',
  SENHA_REDEFINIDA_COM_SUCESSO: '/senha-redefinida-sucesso',

  HOME: '/home',

  ATUALIZACAO: '/atualizacao',

  ATUALIZACAO_GERENCIAR: '/atualizacao/gerenciar/:id',

  LOG_ERROS: '/log-erros',

  ASSINATURAS: '/assinaturas',
  ASSINATURAS_CADASTRAR: '/assinaturas/cadastrar',
  ASSINATURAS_ALTERAR: '/assinaturas/alterar/:id',
  ASSINATURAS_FATURAMENTO: '/assinaturas/faturamento',
  
  FATURAMENTO_CONFERENCIA: '/faturamento-conferencia',
  FATURAMENTO_EXIBICAO: '/faturamento-exibicao',
  FATURAMENTO_EXIBICAO_DETALHADO: '/faturamento-exibicao/:mesAno',

  REGRAS_FISCAL_UF: '/regras-fiscais-uf',
  REGRAS_FISCAL_UF_CADASTRAR: '/regras-fiscais-uf/cadastrar',
  REGRAS_FISCAL_UF_ALTERAR: '/regras-fiscais-uf/alterar/:id',

  NOTAFISCAL_SERVICO_URL_CADASTRAR: '/nota-fiscal-servico-urls/cadastrar',
  NOTAFISCAL_SERVICO_URL_ALTERAR: '/nota-fiscal-servico-urls/alterar/:id',
  NOTAFISCAL_SERVICO_URL_LISTAGEM: '/nota-fiscal-servico-urls',

  NFCE_QRCODE: '/nfce-qrcode',
  NFCE_QRCODE_CADASTRAR: '/nfce-qrcode/cadastrar',
  NFCE_QRCODE_ALTERAR: '/nfce-qrcode/alterar/:id',

  NOTA_FISCAL_VALIDACOES: '/nota-fiscal-validacoes',
  NOTA_FISCAL_VALIDACOES_CADASTRAR: '/nota-fiscal-validacoes/cadastrar',
  NOTA_FISCAL_VALIDACOES_ALTERAR: '/nota-fiscal-validacoes/alterar/:id',

  NOTAFISCAL_REJEICOES_LISTAR: '/nota-fiscal-rejeicoes',
  NOTAFISCAL_REJEICOES_CADASTRAR: '/nota-fiscal-rejeicoes/cadastrar',
  NOTAFISCAL_REJEICOES_ALTERAR: '/nota-fiscal-rejeicoes/alterar/:id',

  IMPORTACAO_NCM_LISTAR: '/importacao-ncm',

  PRODUTOS: '/produtos',
  PRODUTOS_CADASTRAR: '/produtos/cadastrar',
  PRODUTOS_ALTERAR: '/produtos/alterar/:id',

  SERVICOS: '/servicos',
  SERVICOS_CADASTRAR: '/servicos/cadastrar',
  SERVICOS_ALTERAR: '/servicos/alterar/:id',

  REVENDA: '/revendas',
  REVENDA_ALTERAR: '/revendas/alterar/:id',
  REVENDA_CADASTRAR: '/revendas/cadastrar',

  GRADE_SERVICOS: '/grades-servicos',
  GRADE_SERVICOS_ALTERAR: '/grades-servicos/alterar/:id',
  GRADE_SERVICOS_CADASTRAR: '/grades-servicos/cadastrar',

  TABELA_PRECOS: '/tabela-preco',
  TABELA_PRECOS_ALTERAR: '/tabela-preco/alterar/:id',
  TABELA_PRECOS_CADASTRAR: '/tabela-preco/cadastrar',

  USUARIOS: '/usuarios',
  USUARIOS_CADASTRAR: '/usuarios/cadastrar',
  USUARIOS_ALTERAR: '/usuarios/alterar/:id',

  VIDEOS: '/videos',
  VIDEOS_CADASTRAR: '/videos/cadastrar',
  VIDEOS_ALTERAR: '/videos/alterar/:id',

  ARTIGOS: '/artigos',
  ARTIGOS_CADASTRAR: '/artigos/cadastrar',
  ARTIGOS_ALTERAR: '/artigos/alterar/:id',

  TEMAS: '/temas',
  TEMA_CADASTRAR: '/temas/cadastrar',
  TEMA_ALTERAR: '/temas/alterar/:id',

  CATEGORIAS_TREINAMENTO: '/categorias-treinamento',
  CATEGORIA_TREINAMENTO_CADASTRAR: '/categorias-treinamento/cadastrar',
  CATEGORIA_TREINAMENTO_ALTERAR: '/categorias-treinamento/alterar/:id',

  BANNERS: '/banner',
  BANNER_CADASTRAR: '/banner/cadastrar',
  BANNER_ALTERAR: '/banner/alterar/:id',

};

export const SubstituirParametroRota = (
  route: string,
  paramName: string,
  value: string
) => {
  return route.replace(`:${paramName}`, value);
};
