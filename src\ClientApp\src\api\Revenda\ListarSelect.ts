import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';

type RevendaProps = {
  id: string;
  nomeFantasia: string;
};

export const listarSelectRevenda = (params: { nome: string }) => {
  const { nome } = params;

  return api.get<void, ResponseApi<RevendaProps[]>>(
    ConstantEnderecoWebservice.REVENDAS_LISTAR_SELECT,
    { params: { nome } }
  );
};
