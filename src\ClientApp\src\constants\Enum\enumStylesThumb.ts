type StyleThumbProps = {
  [key: number]: {
    color: string;
    bg: string;
  };
};

export const enumStylesThumb: StyleThumbProps = {
  1: {
    color: 'white',
    bg: 'linear-gradient(230deg, #37FFFF 0%, #0001FF 100%)',
  },
  2: {
    color: 'white',
    bg: 'linear-gradient(230deg, #FF992E 0%, #DA1467 100%)',
  },
  3: {
    color: 'white',
    bg: 'linear-gradient(230deg, #4F01B1 0%, #FF007D 100%)',
  },
  4: {
    color: 'black',
    bg: 'linear-gradient(230deg, #03FFFF 0%, #80FF01 100%)',
  },
  5: {
    color: 'white',
    bg: 'linear-gradient(230deg, #FF1572 0%, #FEC183 100%)',
  },
  6: {
    color: 'white',
    bg: 'linear-gradient(230deg, #638892 0%, #000000 100%)',
  },
  7: {
    color: 'black',
    bg: 'linear-gradient(230deg, #FBA2CC 0%, #00FFE5 100%)',
  },
  8: {
    color: 'white',
    bg: 'linear-gradient(230deg, #F84145 0%, #761106 100%)',
  },
  9: {
    color: 'white',
    bg: 'linear-gradient(230deg, #5C6D89 0%, #1B335A 100%)',
  },
  10: {
    color: 'white',
    bg: 'linear-gradient(230deg, #FF9AFF 0%, #CD11B4 100%)',
  },
  11: {
    color: 'white',
    bg: 'linear-gradient(230deg, #028000 0%, #003A00 100%)',
  },
  12: {
    color: 'white',
    bg: 'linear-gradient(230deg, #C36B5D 0%, #7F0040 100%)',
  },
  13: {
    color: 'black',
    bg: 'linear-gradient(230deg, #97ECC2 0%, #FED12E 100%)',
  },
  14: {
    color: 'white',
    bg: 'linear-gradient(230deg, #BEB7A8 0%, #5D3609 100%)',
  },
  15: {
    color: 'white',
    bg: 'linear-gradient(230deg, #FF9D00 0%, #1A8F92 100%)',
  },
  16: {
    color: 'white',
    bg: 'linear-gradient(230deg, #D7C9DB 0%, #7700C5 100%)',
  },
};
