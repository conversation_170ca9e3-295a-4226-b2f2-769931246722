import { Meta, StoryFn } from '@storybook/react';

import { InputDateRange as Input } from './exampleInput';
import { InputDateRangeProps } from '.';

export default {
  title: 'Components/Input',
  args: {
    name: 'storybookInput',
    startDateName: 'dataInicio',
    endDateName: 'dataFim',
    isClearable: true,
    onConfirm: () => {},
  },
} as Meta;

export const InputDateRange: StoryFn<InputDateRangeProps> = (props) => {
  return <Input isClearable={props.isClearable} {...props} />;
};
