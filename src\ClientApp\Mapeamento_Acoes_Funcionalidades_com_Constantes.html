<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapeamento de Ações e Permissões com Constantes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 11px;
        }
        th {
            background-color: #366092;
            color: white;
            padding: 12px 6px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #ddd;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        td {
            padding: 6px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .acao {
            text-align: left;
            font-weight: bold;
            background-color: #f8f9fa;
            max-width: 400px;
            word-wrap: break-word;
            font-size: 10px;
        }
        .sim {
            background-color: #c6efce;
            color: #006100;
            font-weight: bold;
        }
        .nao {
            background-color: #ffc7ce;
            color: #9c0006;
        }
        .stats {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f8ff;
            border-radius: 5px;
        }
        .copy-btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .copy-btn:hover {
            background-color: #45a049;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Mapeamento de Ações e Permissões por Tipo de Usuário</h1>
        
        <div class="highlight">
            <strong>✨ ATUALIZADO:</strong> Agora incluindo os nomes das constantes para melhor organização!
        </div>
        
        <div class="info">
            <strong>Instruções:</strong>
            <ul>
                <li>Esta tabela mapeia todas as ações definidas na pasta Funcionalidades</li>
                <li>Cada ação agora inclui o nome da constante (ex: CadastroRevendaAcao.CADASTRAR_REVENDAS)</li>
                <li>Cada célula indica se o tipo de usuário tem acesso (SIM) ou não (NÃO) à ação</li>
                <li>Você pode copiar esta tabela diretamente para o Excel</li>
                <li>Total de ações mapeadas: <strong>122</strong></li>
            </ul>
        </div>

        <button class="copy-btn" onclick="copyTableToClipboard()">📋 Copiar Tabela para Excel</button>
        <button class="copy-btn" onclick="downloadCSV()">💾 Baixar CSV</button>

        <table id="permissionsTable">
            <thead>
                <tr>
                    <th>Ação</th>
                    <th>SISTEMA_ADMIN</th>
                    <th>SISTEMA_FINANCEIRO</th>
                    <th>DESENVOLVEDOR</th>
                    <th>REVENDA_ADMIN</th>
                    <th>REVENDA_ASSISTENTE</th>
                    <th>SUPORTE_STI3</th>
                    <th>CANAIS_GERENTE</th>
                    <th>ANALISTA_CONTEUDO</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>

        <div class="stats">
            <h3>Estatísticas:</h3>
            <div id="statistics"></div>
        </div>
    </div>

    <script>
        // Dados das ações e permissões com nomes das constantes
        const acoesData = [
            ['AcessoMenu.MENU_ARTIGOS_VIDEOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['AcessoMenu.MENU_ASSINATURA', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'NÃO'],
            ['AcessoMenu.MENU_ATUALIZACAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_BANNER', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['AcessoMenu.MENU_CADASTRO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AcessoMenu.MENU_CADASTRO_GRADE_SERVICO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AcessoMenu.MENU_CADASTRO_PRODUTO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_CADASTRO_SERVICO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_CADASTRO_USUARIO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AcessoMenu.MENU_CATEGORIAS_TREINAMENTO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['AcessoMenu.MENU_FATURAMENTO_CONFERENCIA', 'SIM', 'SIM', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AcessoMenu.MENU_FATURAMENTO_EXIBICAO', 'SIM', 'SIM', 'SIM', 'SIM', 'NÃO', 'SIM', 'SIM', 'NÃO'],
            ['AcessoMenu.MENU_FISCAL', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_FISCAL_NFCE', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_FISCAL_NF_REJEICAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_FISCAL_NF_VALIDACAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_FISCAL_NOTA_FISCAL_URL', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_FISCAL_REGRA_FISCAL', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_HANG_FIRE', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_IMPORTACAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_LOG', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AcessoMenu.MENU_REVENDA', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'SIM', 'SIM', 'NÃO'],
            ['AcessoMenu.MENU_TEMAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['AcessoMenu.MENU_TREINAMENTOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['AssinaturaAcao.ASSINATURA_ALTERAR', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_ALTERAR_SERVICO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_CADASTRAR', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_CANCELAR_REATIVAR', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_DIA_VENCIMENTO', 'SIM', 'SIM', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_EXCLUIR_SERVICO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_EXIBIR_BANCO_DADOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_EXIBIR_FATURAMENTO', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_GERAR_FATURAMENTO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_LIBERACAO_PROVISORIA', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'NÃO'],
            ['AssinaturaAcao.ASSINATURA_VALIDAR_BLOQUEAR', 'SIM', 'SIM', 'SIM', 'SIM', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AssinaturaAcao.CONTA_CLIENTE_ALTERAR', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AssinaturaAcao.CONTA_CLIENTE_ALTERAR_REVENDA', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AssinaturaAcao.CONTA_CLIENTE_BLOQUEAR_DESBLOQUEAR', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AssinaturaAcao.CONTA_CLIENTE_CADASTRAR_LOJA', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['AssinaturaAcao.CONTA_CLIENTE_VISUALIZAR_BANCO_DADOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AssinaturaAcao.ENVIO_DE_LOGS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AtualizacaoAcao.ALTERAR_ATUALIZACOES', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AtualizacaoAcao.ATUALIZAR_GERENCIAR_ATUALIZACAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AtualizacaoAcao.ATUALIZAR_TODOS_GERENCIAR_ATUALIZACAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AtualizacaoAcao.CADASTRAR_ATUALIZACOES', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AtualizacaoAcao.EXCLUIR_ATUALIZACOES', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['AtualizacaoAcao.VISUALIZAR_GERENCIAR_ATUALIZACAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroGradeServicoAcao.ALTERAR_GRADE_SERVICOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroGradeServicoAcao.CADASTRAR_GRADE_SERVICOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroGradeServicoAcao.EXCLUIR_GRADE_SERVICOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroGradeServicoAcao.VISUALIZAR_GRADE_SERVICOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroProdutoAcao.ALTERAR_PRODUTOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroProdutoAcao.CADASTRAR_PRODUTOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroProdutoAcao.EXCLUIR_PRODUTOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroProdutoAcao.VISUALIZAR_PRODUTOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroRevendaAcao.ALTERAR_REVENDAS', 'SIM', 'NÃO', 'SIM', 'SIM', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['CadastroRevendaAcao.CADASTRAR_REVENDAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['CadastroRevendaAcao.EXCLUIR_REVENDAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['CadastroRevendaAcao.LOGAR_REVENDAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'SIM', 'SIM', 'NÃO'],
            ['CadastroRevendaAcao.PARAMETROS_STI3_REVENDAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroServicoAcao.ALTERAR_SERVICOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroServicoAcao.CADASTRAR_SERVICOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroServicoAcao.EXCLUIR_SERVICOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroServicoAcao.VISUALIZAR_SERVICOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroTabelaPrecoAcao.ALTERAR_INFO_TABELA_PRECOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroTabelaPrecoAcao.ALTERAR_TABELA_PRECOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroTabelaPrecoAcao.CADASTRAR_TABELA_PRECOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroTabelaPrecoAcao.EXCLUIR_TABELA_PRECOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroTabelaPrecoAcao.VISUALIZAR_TABELA_PRECOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['CadastroUsuarioAcao.USUARIO_ALTERAR', 'SIM', 'NÃO', 'SIM', 'SIM', 'NÃO', 'SIM', 'SIM', 'NÃO'],
            ['CadastroUsuarioAcao.USUARIO_CADASTRAR', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['CadastroUsuarioAcao.USUARIO_EXCLUIR', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['CadastroUsuarioAcao.USUARIO_LISTAGEM', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'SIM', 'SIM', 'NÃO'],
            ['FaturamentoAcao.FATURAMENTO_CONFERENCIA', 'SIM', 'SIM', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'SIM', 'NÃO'],
            ['FaturamentoAcao.FATURAMENTO_EXCLUIR', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['FaturamentoAcao.FATURAMENTO_EXIBIR', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'NÃO'],
            ['FaturamentoAcao.FATURAMENTO_LANCAR_VALOR', 'SIM', 'SIM', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['FaturamentoAcao.FATURAMENTO_VER_DETALHES', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'SIM', 'NÃO'],
            ['FicalNFCeAcao.ALTERAR_NFCE', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['FicalNFCeAcao.CADASTRAR_NFCE', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['FicalNFCeAcao.EXCLUIR_NFCE', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['FicalNFCeAcao.VISUALIZAR_NFCE', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['ImportacaoNcmAcao.CADASTRAR_IMPORTACAO_NCM', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['ImportacaoNcmAcao.VISUALIZAR_IMPORTACAO_NCM', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['LogErroAcao.EXCLUIR_LOG_ERROS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['LogErroAcao.EXCLUIR_TODOS_LOG_ERROS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFicalValidacaoAcao.ALTERAR_NF_VALIDACOES', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFicalValidacaoAcao.CADASTRAR_NF_VALIDACOES', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFicalValidacaoAcao.EXCLUIR_NF_VALIDACOES', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFicalValidacaoAcao.VISUALIZAR_NF_VALIDACOES', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalRegraUrlAcao.ALTERAR_REGRAS_FISCAIS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalRegraUrlAcao.CADASTRAR_REGRAS_FISCAIS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalRegraUrlAcao.EXCLUIR_REGRAS_FISCAIS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalRegraUrlAcao.VISUALIZAR_REGRAS_FISCAIS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalRejeicaoAcao.ALTERAR_NF_REJEICAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalRejeicaoAcao.CADASTRAR_NF_REJEICAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalRejeicaoAcao.EXCLUIR_NF_REJEICAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalRejeicaoAcao.VISUALIZAR_NF_REJEICAO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalUrlAcao.ALTERAR_NF_SERVICO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalUrlAcao.CADASTRAR_NF_SERVICO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalUrlAcao.EXCLUIR_NF_SERVICO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['NotaFiscalUrlAcao.VISUALIZAR_NF_SERVICO', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'NÃO'],
            ['Zenflix.ALTERAR_ARTIGOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.ALTERAR_BANNERS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.ALTERAR_CATEGORIAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.ALTERAR_TEMAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.ALTERAR_VIDEOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.CADASTRAR_ARTIGOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.CADASTRAR_BANNERS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.CADASTRAR_CATEGORIAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.CADASTRAR_TEMAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.CADASTRAR_VIDEOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.EXCLUIR_ARTIGOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.EXCLUIR_BANNERS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.EXCLUIR_CATEGORIAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.EXCLUIR_TEMAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.EXCLUIR_VIDEOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.VISUALIZAR_ARTIGOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.VISUALIZAR_BANNERS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.VISUALIZAR_CATEGORIAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.VISUALIZAR_TEMAS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM'],
            ['Zenflix.VISUALIZAR_VIDEOS', 'SIM', 'NÃO', 'SIM', 'NÃO', 'NÃO', 'NÃO', 'NÃO', 'SIM']
        ];

        function populateTable() {
            const tbody = document.querySelector('#permissionsTable tbody');
            
            acoesData.forEach(row => {
                const tr = document.createElement('tr');
                row.forEach((cell, index) => {
                    const td = document.createElement('td');
                    td.textContent = cell;
                    
                    if (index === 0) {
                        td.className = 'acao';
                    } else {
                        td.className = cell === 'SIM' ? 'sim' : 'nao';
                    }
                    
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
        }

        function copyTableToClipboard() {
            const table = document.getElementById('permissionsTable');
            const range = document.createRange();
            range.selectNode(table);
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
            
            try {
                document.execCommand('copy');
                alert('Tabela copiada! Cole no Excel usando Ctrl+V');
            } catch (err) {
                alert('Erro ao copiar. Tente selecionar a tabela manualmente e copiar.');
            }
            
            window.getSelection().removeAllRanges();
        }

        function downloadCSV() {
            const headers = ['Ação', 'SISTEMA_ADMIN', 'SISTEMA_FINANCEIRO', 'DESENVOLVEDOR', 'REVENDA_ADMIN', 'REVENDA_ASSISTENTE', 'SUPORTE_STI3', 'CANAIS_GERENTE', 'ANALISTA_CONTEUDO'];
            let csvContent = headers.join(',') + '\\n';
            
            acoesData.forEach(row => {
                csvContent += row.join(',') + '\\n';
            });
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'Mapeamento_Acoes_Funcionalidades_com_Constantes.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function generateStatistics() {
            const stats = document.getElementById('statistics');
            const userTypes = ['SISTEMA_ADMIN', 'SISTEMA_FINANCEIRO', 'DESENVOLVEDOR', 'REVENDA_ADMIN', 'REVENDA_ASSISTENTE', 'SUPORTE_STI3', 'CANAIS_GERENTE', 'ANALISTA_CONTEUDO'];
            
            let statsHTML = '<ul>';
            userTypes.forEach((userType, index) => {
                const permissions = acoesData.filter(row => row[index + 1] === 'SIM').length;
                statsHTML += `<li><strong>${userType}:</strong> ${permissions} permissões</li>`;
            });
            statsHTML += '</ul>';
            
            stats.innerHTML = statsHTML;
        }

        // Inicializar a página
        document.addEventListener('DOMContentLoaded', function() {
            populateTable();
            generateStatistics();
        });
    </script>
</body>
</html>
