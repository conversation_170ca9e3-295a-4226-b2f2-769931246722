import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

import { SelectOptions } from 'components/Select/SelectDefault';
import { CepValue } from 'components/Input/InputChakra/InputCep';

export interface ServicoProps {
  label: string;
  value: string;
  id: string;
  servicoEstaAdicionado?: boolean;
  nome?: string;
  produto?: string;
}

export interface FormRevendaProps {
  servicosCadastrados?: ServicoProps[] | null;
  cidadeNome?: string;
  setListaServicos: React.Dispatch<React.SetStateAction<ServicoProps[]>>;
  listaServicos: ServicoProps[];
}

export interface GradeServicos {
  id: string;
  nome: string;
  produto: string;
}

export interface FormDataCadastrar {
  ativo: boolean;
  cnpj: string;
  razaoSocial: string;
  nomeFantasia: string;
  inscricaoEstadual: string | null;
  inscricaoMunicipal: string | null;
  cep: CepValue | string;
  logradouro: string;
  numero: string;
  complemento: string | null;
  bairro: string;
  cidadeId: SelectOptions | null | string;
  paisId: SelectOptions | null | number | string;
  celular: string;
  telefone: string | null;
  emailContato: string;
  emailFinanceiro: string;
  cpfResponsavel: string;
  nomeResponsavel: string;
  emailResponsavel: string;
  login: string;
  linkSuporte: string | null;
  telefoneSuporte: string | null;
  movideskToken: string;
  consultaPendenciaWS: string;
  grades?: GradeServicos[] | null;
  horariosFuncionamento?: {
    dataHoraFechamento: string | number | Date;
    dataHoraAbertura: string | number | Date;
    aberto: boolean;
    dia: number;
    abertura: string;
    fechamento: string;
  }[];
}

export interface FormDataAlterarRevenda extends FormDataCadastrar {
  gradesRemovidas: string[] | null;
  dataHoraCancelamento: Date | null;
  motivoCancelamento: string | null;
}

export interface ObterRevenda extends FormDataAlterarRevenda {
  id: string;
  dataHoraCadastro: Date;
  dataHoraUltimaAlteracao: Date;

  cidadeNome: string;
  paisNome: string;
}

export type HorarioProps = {
  aberto: boolean;
  dia: number;
  abertura: string;
  fechamento: string;
};

const formDefaultValues = {
  cnpj: '',
  razaoSocial: '',
  nomeFantasia: '',
  inscricaoEstadual: '',
  inscricaoMunicipal: '',
  cep: '',
  logradouro: '',
  numero: '',
  complemento: '',
  bairro: '',
  cidadeId: null,
  paisId: null,
  celular: '',
  telefone: '',
  emailFinanceiro: '',
  cpfResponsavel: '',
  nomeResponsavel: '',
  emailResponsavel: '',
  login: '',
  linkSuporte: '',
  emailContato: '',
  telefoneSuporte: '',
  movideskToken: '',
  consultaPendenciaWS: '',
};

export const formDefaultValuesCadastrar: FormDataCadastrar = {
  ...formDefaultValues,
  ativo: true,
  grades: null,
};

export const formDefaultValuesAlterar: FormDataAlterarRevenda = {
  ...formDefaultValues,
  ativo: true,
  dataHoraCancelamento: null,
  motivoCancelamento: '',
  gradesRemovidas: null,
  grades: null,
};

const schema = yup.object().shape({
  cnpj: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  razaoSocial: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  nomeFantasia: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  inscricaoEstadual: yup.string().nullable(),
  inscricaoMunicipal: yup.string().nullable(),
  cep: yup.lazy((newValue) => {
    if (newValue?.isGetCep) {
      return yup
        .object({
          value: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
        })
        .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO);
    } else {
      return yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO);
    }
  }),
  logradouro: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  numero: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  complemento: yup.string().nullable(),
  bairro: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  cidadeId: yup
    .object()
    .typeError(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  paisId: yup.object().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  celular: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  telefone: yup.string().nullable(),
  emailContato: yup
    .string()
    .typeError(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .email(EnumValidacoesSistema.EMAIL_INVALIDO)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  emailFinanceiro: yup
    .string()
    .typeError(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .email(EnumValidacoesSistema.EMAIL_INVALIDO)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  cpfResponsavel: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  nomeResponsavel: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  emailResponsavel: yup
    .string()
    .typeError(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  login: yup
    .string()
    .typeError(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  linkSuporte: yup.string().nullable(),
  telefoneSuporte: yup.string().nullable(),
  movideskToken: yup.string().nullable(),
  consultaPendenciaWS: yup.string().nullable(),
  grades: yup
    .array()
    .of(
      yup.object().shape({
        id: yup.string(),
        nome: yup.string(),
        produto: yup.string(),
      })
    )
    .nullable(),
  gradesRemovidas: yup.array().of(yup.string()).nullable(),
  ativo: yup.boolean().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  horariosFuncionamento: yup.array().of(
    yup.object().shape({
      aberto: yup.boolean(),
      abertura: yup.string().when('aberto', {
        is: true,
        then: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
        otherwise: yup.string(),
      }),
      fechamento: yup.string().when('aberto', {
        is: true,
        then: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
        otherwise: yup.string(),
      }),
    })
  ),
  motivoCancelamento: yup
    .string()
    .nullable()
    .when('ativo', {
      is: (value: boolean) => !value,
      then: yup
        .string()
        .nullable()
        .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
    }),
});

export const yupResolver = yupResolverInstance(schema);
