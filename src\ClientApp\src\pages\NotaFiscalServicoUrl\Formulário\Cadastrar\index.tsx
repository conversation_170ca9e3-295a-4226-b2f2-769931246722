import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import api, { ResponseApi } from 'services/api';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormNotaFiscalServicoURL } from '..';
import { FormData, formDefaultValues, yupResolver } from '../validationForm';

export const CadastrarNotaFiscalServicoURL = () => {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });

  const { handleSubmit, reset } = formMethods;

  const getResponseNotaFiscalServico = useCallback(async (data: FormData) => {
    const response = await api.post<void, ResponseApi>(
      ConstantEnderecoWebservice.CADASTRAR_NF_SERVICO_URL,
      {
        ...data,
        modeloNf: data.modeloNf?.value,
        servico: data.servico?.value,
        notaFiscalAutorizador: data.notaFiscalAutorizador?.value,
        ativo: true,
      }
    );
    return response;
  }, []);

  const handleCadastrarNotaFiscalServico = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await getResponseNotaFiscalServico(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      navigate(ConstanteRotas.NOTAFISCAL_SERVICO_URL_LISTAGEM);
    }
    setIsLoading(false);
  });

  const handleCadastrarInserirNovoNotaFiscalServico = handleSubmit(
    async (data) => {
      setIsLoading(true);

      const response = await getResponseNotaFiscalServico(data);

      if (response.sucesso) {
        toast.success('Cadastro realizado com sucesso.');
        reset(formDefaultValues);
      }
      setIsLoading(false);
    }
  );

  return (
    <LayoutFormPage
      onSubmit={() => handleCadastrarNotaFiscalServico()}
      onResetSubmit={() => handleCadastrarInserirNovoNotaFiscalServico()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormNotaFiscalServicoURL />
      </FormProvider>
    </LayoutFormPage>
  );
};
