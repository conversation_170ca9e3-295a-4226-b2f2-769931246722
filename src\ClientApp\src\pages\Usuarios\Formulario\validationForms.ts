import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

export type FormData = {
  tipoUsuario: number | null;
  email: string;
  login: string | null;
  nome: string;
  ativo?: boolean;
};

export const formDefaultValues = {
  tipoUsuario: null,
  email: '',
  login: '',
  nome: '',
  ativo: true,
};

const schema = yup.object().shape({
  nome: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  email: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .email(EnumValidacoesSistema.EMAIL_INVALIDO),
  login: yup
    .string()
    .nullable()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  tipoUsuario: yup
    .mixed()
    .nullable()
    .default(null)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

export const yupResolver = yupResolverInstance(schema);
