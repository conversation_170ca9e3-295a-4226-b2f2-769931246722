import { FormProvider, useForm } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';
import {
  Flex,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalProps,
  Text,
  useDisclosure,
} from '@chakra-ui/react';

import { ButtonDefault } from 'components/Button';

import { ModalDefaultChakra } from '../ModalDefaultChakra';

type ModalDefaultProps = Omit<ModalProps, 'children' | 'isOpen' | 'onClose'> &
  InstanceProps<ModalProps> & {
    descricao: string;
  };

export const ModalDescricaoLogErros = create<ModalDefaultProps>(
  ({ onResolve, onReject, descricao, ...rest }) => {
    const formMethods = useForm();

    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    return (
      <ModalDefaultChakra
        {...rest}
        isCentered
        size={['full', 'full', '2xl']}
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <FormProvider {...formMethods}>
          <ModalContent maxH={['full', 'full', '400px']} bg="gray.50">
            <ModalBody overflowY="auto" p="15px" pt="20px">
              <Flex w="full" bg="white" borderRadius="5px" p="12px">
                <Text w="full">{descricao}</Text>
              </Flex>
            </ModalBody>
            <ModalFooter>
              <Flex
                w="full"
                justifyContent={['center', 'center', 'right']}
                flexDirection={['column', 'row', 'row']}
              >
                <ButtonDefault
                  width={['full', '120px', '120px', '120px']}
                  colorScheme="none"
                  id="buttonCalcelar"
                  variant="outlinePill"
                  onClick={() => onClose()}
                  possuiFuncionalidade={true}
                >
                  Fechar
                </ButtonDefault>
              </Flex>
            </ModalFooter>
          </ModalContent>
        </FormProvider>
      </ModalDefaultChakra>
    );
  }
);
