import { Meta } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';
import { Box, Button, Text } from '@chakra-ui/react';

import { ButtonDefault } from 'components/Button/Button.stories';

import { theme } from '../../../theme/index';
import { ModalDefault } from '.';

export default {
  title: 'Components/Modal',
  component: Button,
  argTypes: getThemingArgTypes(theme as any, 'Modal'),
} as Meta;

export const Modal = ({ ...rest }) => {
  return (
    <Box w="200px">
      <ButtonDefault
        colorScheme="green"
        onClick={() => ModalDefault({ ...rest })}
        possuiFuncionalidade={true}
      >
        Open Modal
      </ButtonDefault>
      <Text w="50px" display="none">
        Não é necessário usar esse {'<FullScreenProvider>'}
      </Text>
      <Text w="50px" display="none">
        Para abrir o modal, passe ele como uma função:{' '}
        {'{onClick: () => ModalDefault()}'}
      </Text>
    </Box>
  );
};
