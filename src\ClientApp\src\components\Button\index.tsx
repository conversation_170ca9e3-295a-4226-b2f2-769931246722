import { Button, ButtonProps, Icon } from '@chakra-ui/react';

export interface ButtonDefaultProps extends ButtonProps {
  children: React.ReactNode;
  funcionalidade?: string;
  iconSide?: string;
  onClick?: () => void;
  possuiFuncionalidade: boolean;
}

export const ButtonDefault = ({
  children,
  id,
  possuiFuncionalidade = true,
  iconSide = 'right',
  variant,
  colorScheme,
  isDisabled,
  rightIcon,
  leftIcon,
  width,
  _hover,
  color = 'black',
  onClick,
  size,
  ...rest
}: ButtonDefaultProps) => {
  return (
    <>
      {possuiFuncionalidade && (
        <Button
          {...rest}
          _hover={{
            ..._hover,
            filter: 'brightness(0.9)',
            transition: 'filter 0.2s',
          }}
          id={id}
          onClick={(event) => {
            if (event.detail === 1 && onClick) {
              onClick();
            }
          }}
          size={size}
          _disabled={{
            backgroundColor: `colorScheme${500}`,
            cursor: 'not-allowed',
            opacity: 0.6,
            '&:hover': { filter: 'none' },
          }}
          _active={{}}
          w={width || 'full'}
          isDisabled={isDisabled}
          variant={variant}
          colorScheme={colorScheme}
          rightIcon={rightIcon}
          leftIcon={leftIcon}
          color={color}
        >
          {children}
        </Button>
      )}
    </>
  );
};
