import { useCallback, useState, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import api, { ResponseApi } from 'services/api';

import { LayoutFormPage } from 'components/Layout/FormPage';
import { SelectOptions } from 'components/Select/SelectDefault';
import { CepValue } from 'components/Input/InputChakra/InputCep';

import { FormRevenda } from '..';
import {
  FormDataAlterarRevenda,
  formDefaultValuesAlterar,
  ObterRevenda,
  ServicoProps,
  yupResolver,
  HorarioProps,
} from '../validationForm';

export const AlterarRevenda = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [servicos, setServicos] = useState<ServicoProps[]>([]);
  const [listaServicos, setListaServicos] = useState<ServicoProps[]>([]);

  const formMethods = useForm<FormDataAlterarRevenda>({
    resolver: yupResolver,
    defaultValues: formDefaultValuesAlterar,
  });

  const gradesSelecionadas = listaServicos.filter(
    (servicos) => servicos.servicoEstaAdicionado
  );

  const { handleSubmit, reset, setValue } = formMethods;
  const navigate = useNavigate();
  const idRouter = useParams();
  const { id: idRota } = idRouter;

  const getRevendas = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<ObterRevenda>>(
      ConstantEnderecoWebservice.OBTER_REVENDA,
      {
        params: { id: idRota },
      }
    );

    if (response.sucesso && response.dados) {
      const {
        dados: {
          cidadeNome,
          grades,
          paisNome,
          cidadeId,
          cep,
          horariosFuncionamento,
        },
      } = response;

      const horariosFormatados = horariosFuncionamento?.map((horario) => {
        return {
          ...horario,
          abertura: new Date(horario.dataHoraAbertura)?.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }),
          fechamento: new Date(horario.dataHoraFechamento)?.toLocaleTimeString(
            [],
            {
              hour: '2-digit',
              minute: '2-digit',
            }
          ),
        };
      });

      reset({
        ...response.dados,
        cidadeId: { label: cidadeNome, value: String(cidadeId) },
        cep: { isGetCep: true, value: String(cep) },
        paisId: paisNome,
        horariosFuncionamento: horariosFormatados,
      });

      const servicosCadastrados = (grades || []).map((grade) => {
        return {
          id: grade.id,
          value: grade.produto,
          label: grade.nome,
        };
      });
      if (servicosCadastrados && servicosCadastrados?.length > 0) {
        setServicos(servicosCadastrados);
      }
    } else {
      navigate(ConstanteRotas.REVENDA);
    }

    setIsLoading(false);
  }, [reset, idRota]);

  const formatDateHorarioFuncionamento = ({
    abertura,
    fechamento,
    aberto,
  }: HorarioProps) => {
    const today = new Date();

    const [horasAbertura, minutosAbertura] = abertura?.split(':') || [];
    const [horasFechamento, minutosFechamento] = fechamento?.split(':') || [];
    const offset = today?.getTimezoneOffset();

    if (horasAbertura && minutosAbertura) {
      today.setHours(parseInt(horasAbertura, 10)); // 10 é a base decimal
      today.setMinutes(parseInt(minutosAbertura, 10));
      today.setMinutes(today?.getMinutes() - offset); // Ajusta a data para o timezone local
    }
    const dataAbertura = today?.toISOString();

    if (horasFechamento && minutosFechamento) {
      today.setHours(parseInt(horasFechamento, 10));
      today.setMinutes(parseInt(minutosFechamento, 10));
      today.setMinutes(today.getMinutes() - offset);
    }

    const dataFechamento = today?.toISOString();

    const isHorarioAberto = aberto || !!aberto;

    const hasDataAbertura = aberto ? dataAbertura : null;

    const hasDataFechamento = aberto ? dataFechamento : null;

    return {
      isHorarioAberto,
      hasDataAbertura,
      hasDataFechamento,
    };
  };

  const handleAlterarRevenda = handleSubmit(async (data) => {
    setIsLoading(true);

    if (servicos) {
      const gradesServicosRemovidas: string[] =
        servicos &&
        servicos
          .filter(
            (servico) =>
              !gradesSelecionadas?.find((grade) => grade.id === servico.id)
          )
          .map((servico) => servico.id);

      setValue('gradesRemovidas', gradesServicosRemovidas);
    }

    const horariosFuncionamento = (data?.horariosFuncionamento || []).map(
      (horario, index) => {
        const { isHorarioAberto, hasDataAbertura, hasDataFechamento } =
          formatDateHorarioFuncionamento(horario);
        return {
          aberto: isHorarioAberto,
          dia: index,
          dataHoraAbertura: hasDataAbertura,
          dataHoraFechamento: hasDataFechamento,
        };
      }
    );
    const cepIsObject = data.cep as CepValue;
    const cepIsString = data.cep as string;

    // Remove the fields from the request payload
    const response = await api.put<void, ResponseApi<FormDataAlterarRevenda>>(
      ConstantEnderecoWebservice.ALTERAR_REVENDA,
      {
        ...data,
        grades: listaServicos.filter(
          (servicos) => servicos.servicoEstaAdicionado
        ),
        cidadeId: (data.cidadeId as SelectOptions).value,
        paisId: (data.paisId as SelectOptions).value,
        cep:
          cepIsObject.value === undefined
            ? cepIsString.replace('.', '')
            : String(cepIsObject?.value).replace('.', ''),
        celular: data.celular.replace(/\s/g, ''),
        telefone: data.telefone ? data.telefone.replace(/\s/g, '') : null,
        telefoneSuporte: data.telefoneSuporte
          ? data.telefoneSuporte.replace(/\s/g, '')
          : null,
        horariosFuncionamento
      }
    );

    if (response.sucesso) {
      toast.success('Cadastro alterado com sucesso.');
      navigate(ConstanteRotas.REVENDA);
    }

    setIsLoading(false);
  });

  useEffect(() => {
    getRevendas();
  }, [getRevendas]);

  return (
    <LayoutFormPage
      onSubmit={() => handleAlterarRevenda()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormRevenda
          listaServicos={listaServicos}
          setListaServicos={setListaServicos}
          servicosCadastrados={servicos}
        />
      </FormProvider>
    </LayoutFormPage>
  );
};
