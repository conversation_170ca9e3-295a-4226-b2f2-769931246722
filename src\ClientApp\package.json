{"name": "clientapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "tsc && vite build", "build:dev": "vite build --mode dev", "preview": "vite preview", "format": "prettier --write src/**/*.ts{,x}", "lint": "tsc --noEmit && eslint src/**/*.ts{,x}", "test": "cypress open", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook"}, "eslintConfig": {"extends": ["react-app", "plugin:cypress/recommended"]}, "dependencies": {"@chakra-ui/react": "^2.4.3", "@chakra-ui/storybook-addon": "^4.0.16", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@hookform/resolvers": "^2.9.10", "@microsoft/signalr": "^6.0.9", "@types/react-input-mask": "^3.0.2", "@types/react-router-dom": "^5.3.3", "axios": "^1.2.2", "chakra-react-select": "^4.4.3", "cypress": "^12.2.0", "date-fns": "^2.29.3", "framer-motion": "^7.9.1", "jsonp": "^0.2.1", "jwt-decode": "^3.1.2", "npm": "^9.2.0", "react": "^18.2.0", "react-date-range": "^1.1.3", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-full-screen": "^1.1.1", "react-hook-form": "^7.40.0", "react-icons": "^4.7.1", "react-input-mask": "^2.0.4", "react-modal-promise": "^1.0.2", "react-router-dom": "^6.4.5", "react-spinners": "^0.13.7", "react-toastify": "^9.1.1", "storybook": "^6.5.16", "tsconfig-paths-webpack-plugin": "^4.0.0", "vite-tsconfig-paths": "^4.0.3", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.20.7", "@storybook/addon-actions": "^6.5.15", "@storybook/addon-essentials": "^6.5.15", "@storybook/addon-interactions": "^6.5.15", "@storybook/addon-links": "^6.5.15", "@storybook/builder-vite": "^0.2.6", "@storybook/react": "^6.5.15", "@storybook/testing-library": "^0.0.13", "@types/jsonp": "^0.2.1", "@types/node": "^18.11.15", "@types/react": "^18.0.26", "@types/react-date-range": "^1.1.4", "@types/react-dom": "^18.0.9", "@typescript-eslint/eslint-plugin": "^3.10.1", "@typescript-eslint/parser": "^3.10.1", "@vitejs/plugin-react": "^3.0.0", "babel-loader": "^8.3.0", "cypress-localstorage-commands": "^2.2.2", "eslint-config-airbnb": "^18.2.1", "eslint-config-airbnb-typescript": "^9.0.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "prettier": "^2.2.1", "typescript": "^4.9.4", "vite": "^4.0.0"}}