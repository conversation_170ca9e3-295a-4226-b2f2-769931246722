import { GroupBase, Props } from 'chakra-react-select';
import { SelectComponents } from 'react-select/dist/declarations/src/components';

export interface LoadOptionsCallbackProps {
  (options: { label: string; value: number }[]): void;
}

export type SelectOptions = {
  label: string;
  value: number;
};

export interface SelectDefaultProps extends Props {
  name: string;
  id?: string;
  label?: string;
  colorLabel?: string;
  options: SelectOptions[];
  filtrosAtivos?: boolean;
  component?: Partial<SelectComponents<any, boolean, GroupBase<any>>>;
  isMulti?: boolean;
  isSearchable?: boolean;
  closeMenuOnSelect?: boolean;
  onSelect?: (newValue: any) => void;
}
