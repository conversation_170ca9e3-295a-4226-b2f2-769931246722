import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import { useEffectDefault } from 'hook/useEffectDefault';
import api, { ResponseApi } from 'services/api';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormGradeServicos } from '..';
import {
  FormData,
  ObterGrade,
  ServicoProps,
  yupResolver,
} from '../validationForm';

export const FormAlterarGrade = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [gradeItensCadastrados, setGradeItensCadastrados] = useState<
    ServicoProps[]
  >([]);
  const [listaServicos, setListaServicos] = useState<ServicoProps[]>([]);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
  });

  const gradeItemsSelecionados = listaServicos.filter(
    (servicos) => servicos.servicoEstaAdicionado
  );

  const { handleSubmit, reset, setValue } = formMethods;
  const navigate = useNavigate();
  const idRouter = useParams();
  const { id: idRota } = idRouter;

  const getGrade = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<ObterGrade>>(
      ConstantEnderecoWebservice.OBTER_GRADE_SERVICOS,
      {
        params: { id: idRota },
      }
    );

    if (response.sucesso) {
      const { produtoId, produtoNome, gradeItem, ativo, nome } = response.dados;

      reset({
        ativo: ativo,
        nome: nome,
        produtoId: {
          label: produtoNome,
          value: produtoId,
        },
      });

      const gradeItems = (gradeItem || []).map((grade) => {
        setValue(`servicosGrade.${grade.servicoId}`, grade.valorRepasse);

        return {
          id: grade.id,
          servicoId: grade.servicoId,
          valorRepasse: grade.valorRepasse,
          label: '',
          value: grade.servicoId,
        };
      });
      if (gradeItems && gradeItems?.length > 0) {
        setGradeItensCadastrados(gradeItems);
      }
    } else {
      navigate(ConstanteRotas.GRADE_SERVICOS);
    }

    setIsLoading(false);
  }, [reset, idRota]);

  const handleAlterarGrade = handleSubmit(async (data) => {
    setIsLoading(true);

    if (gradeItensCadastrados) {
      const itemGradeRemovidos: string[] =
        gradeItensCadastrados &&
        gradeItensCadastrados
          .filter(
            (item) =>
              !gradeItemsSelecionados?.find((grade) => grade.id === item.id)
          )
          .map((item) => item.id);
      setValue('gradeItensRemovidos', itemGradeRemovidos);
    }

    const response = await api.put<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.ALTERAR_GRADE_SERVICOS,
      {
        id: idRota,
        ativo: data.ativo,
        gradeItem: gradeItemsSelecionados.map((grade) => {
          const valorRepasseServico = data?.servicosGrade[grade.servicoId];

          return {
            id: grade.id,
            servicoId: grade.servicoId,
            valorRepasse: valorRepasseServico,
          };
        }),

        gradeItensRemovidos: data.gradeItensRemovidos,
        nome: data.nome,
        produtoId: data.produtoId?.value,
      }
    );

    if (response.sucesso) {
      toast.success('Cadastro alterado com sucesso.');
      navigate(ConstanteRotas.GRADE_SERVICOS);
    }

    setIsLoading(false);
  });

  useEffectDefault(() => {
    getGrade();
  }, [getGrade]);

  return (
    <LayoutFormPage onSubmit={() => handleAlterarGrade()} isLoading={isLoading}>
      <FormProvider {...formMethods}>
        <FormGradeServicos
          listaServicos={listaServicos}
          setListaServicos={setListaServicos}
          gradeItensCadastrados={gradeItensCadastrados}
          isAlterar
        />
      </FormProvider>
    </LayoutFormPage>
  );
};
