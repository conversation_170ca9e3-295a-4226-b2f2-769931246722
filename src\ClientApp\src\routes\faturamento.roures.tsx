
import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { FaturamentoConferencia } from 'pages/FaturamentoConferencia/index';
import { FaturamentoExibicao } from 'pages/FaturamentoExibicao/index';
import { FaturamentoExibicaoDetalhado } from 'pages/FaturamentoExibicaoDetalhado/index';
import LayoutGuard from './LayoutGuard';


export const FaturamentoExibicaoRoutes = [
    <Route
      key={ConstanteRotas.FATURAMENTO_EXIBICAO}
      path={ConstanteRotas.FATURAMENTO_EXIBICAO}
      element={
        <LayoutGuard
          key={ConstanteRotas.FATURAMENTO_EXIBICAO}
          breadcrumb={[
            { title: 'Faturamento' },
          ]}
          component={<FaturamentoExibicao />}
          possuiFuncionalidade={auth.usuarioPossuiPermissao(
            ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_EXIBIR
          )}
        />
      }
    />,
    <Route
      key={ConstanteRotas.FATURAMENTO_EXIBICAO_DETALHADO}
      path={ConstanteRotas.FATURAMENTO_EXIBICAO_DETALHADO}
      element={
        <LayoutGuard
          key={ConstanteRotas.FATURAMENTO_EXIBICAO_DETALHADO}
          breadcrumb={[
            { title: 'Faturamento', path: ConstanteRotas.FATURAMENTO_EXIBICAO },
            { title: 'Detalhes' },
          ]}
          component={<FaturamentoExibicaoDetalhado />}
          possuiFuncionalidade={auth.usuarioPossuiPermissao(
            ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_VER_DETALHES
          )}
        />
      }
    />
];

export const FaturamentoConferenciaRoutes = [
    <Route
      key={ConstanteRotas.FATURAMENTO_CONFERENCIA}
      path={ConstanteRotas.FATURAMENTO_CONFERENCIA}
      element={
        <LayoutGuard
          key={ConstanteRotas.FATURAMENTO_CONFERENCIA}
          breadcrumb={[
            { title: 'Conferência' },
          ]}
          component={<FaturamentoConferencia />}
          possuiFuncionalidade={auth.usuarioPossuiPermissao(
            ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_CONFERENCIA
          )}
        />
      }
    />,
    <Route
      key={ConstanteRotas.FATURAMENTO_EXIBICAO_DETALHADO}
      path={ConstanteRotas.FATURAMENTO_EXIBICAO_DETALHADO}
      element={
        <LayoutGuard
          key={ConstanteRotas.FATURAMENTO_EXIBICAO_DETALHADO}
          breadcrumb={[
            { title: 'Conferência', path: ConstanteRotas.FATURAMENTO_CONFERENCIA },
            { title: 'Detalhes' },
          ]}
          component={<FaturamentoExibicaoDetalhado />}
          possuiFuncionalidade={auth.usuarioPossuiPermissao(
            ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_CONFERENCIA
          )}
        />
      }
    />
];
