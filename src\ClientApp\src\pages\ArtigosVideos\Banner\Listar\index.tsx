import { useCallback, useState } from 'react';
import { Flex, Icon, Link, Td, Tr } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { FiSearch } from 'react-icons/fi';
import { toast } from 'react-toastify';

import auth from 'modules/auth';
import api, { ResponseApi } from 'services/api';
import { EnumStatusCadastros } from 'constants/Enum/enumStatusCadastros';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { formatQueryPagedTableZenflix } from 'helpers/queryParamsFormat';

import { ButtonDefault } from 'components/Button';
import { ActionsMenu } from 'components/ActionsMenu';
import { Pagination } from 'components/Grid/Pagination';
import { ContainerListagem } from 'components/Grid/GridList';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { SelectDefault } from 'components/Select/SelectDefault';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';

type FormData = {
  titulo: string;
  ativo: boolean | null;
};

type BannerProps = {
  id: string;
  ativo: boolean;
  titulo: string;
  sequenciaOrdenacao: number;
};

const ListarBanners = () => {
  const formMethods = useForm<FormData>({
    defaultValues: {
      ativo: EnumStatusCadastros.TODOS.value,
      titulo: '',
    },
  });
  const { watch } = formMethods;
  const tituloWatch = watch('titulo');

  const navigate = useNavigate();

  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [listaBanners, setListaBanners] = useState<BannerProps[]>([]);
  const [currentFilters, setCurrentFilters] = useState({} as FormData);

  const possuiPermissaoAlterarBanner = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.Zenflix.ALTERAR_BANNERS
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<BannerProps>>
      >(
        formatQueryPagedTableZenflix(
          ConstantEnderecoWebservice.LISTAR_BANNERS,
          gridPaginadaConsulta
        ),
        { params: currentFilters }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        if (response.sucesso && response.dados) {
          setListaBanners(response.dados.registros);
          setTotalRegistros(response.dados.total);
        }
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const handleExcluirBanner = useCallback(
    async (bannerId: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'A ação irá excluir este banner!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            `${ConstantEnderecoWebservice.EXCLUIR_BANNER}/${bannerId}`
          );

          if (response) {
            if (response.avisos) {
              response.avisos.forEach((aviso) => toast.warning(aviso));
            }
            if (response.sucesso) {
              setRecarregarListagem(!recarregarListagem);
              toast.success('Cadastro excluído com sucesso.');
              return true;
            }
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );

  const handleInativarBanner = useCallback(
    async (bannerId: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'A ação irá inativar este banner!',
        confirmButtonText: 'Sim, inativar!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.patch<void, ResponseApi>(
            `${ConstantEnderecoWebservice.INATIVAR_BANNER}/${bannerId}/status`
          );

          if (response) {
            if (response.avisos) {
              response.avisos.forEach((aviso) => toast.warning(aviso));
            }
            if (response.sucesso) {
              setRecarregarListagem(!recarregarListagem);
              toast.success('Cadastro inativado com sucesso.');
              return true;
            }
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );

  const handleAlterarBanner = useCallback((bannerId: string) => {
    if (!possuiPermissaoAlterarBanner) return;
    navigate(
      SubstituirParametroRota(ConstanteRotas.BANNER_ALTERAR, 'id', bannerId)
    );
  }, []);

  return (
    <FormProvider {...formMethods}>
      <ContainerListagem
        inputPesquisa={
          <InputDefault
            maxLength={100}
            placeholder="Buscar por titulo"
            iconLeftElement={FiSearch}
            name="titulo"
            onEnterKeyPress={() => {
              setCurrentFilters((filtrosJaAdicionados) => ({
                ...filtrosJaAdicionados,
                titulo: tituloWatch,
              }));
            }}
          />
        }
        filtrosListagem={
          <SelectDefault
            name="ativo"
            placeholder="Selecione um filtro"
            filtrosAtivos
            asControlledByObject={false}
            onSelect={(optionSelecionada) =>
              setCurrentFilters((filtrosJaAdicionados) => ({
                ...filtrosJaAdicionados,
                ativo: optionSelecionada?.value,
              }))
            }
            options={EnumStatusCadastros.properties.map((status) => status)}
          />
        }
        buttonCadastrar={
          <ButtonDefault
            onClick={() => navigate(ConstanteRotas.BANNER_CADASTRAR)}
            width={['full', 'full', 'full', '220px']}
            color="white"
            colorScheme="secondary"
            leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
            possuiFuncionalidade={auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.Zenflix.CADASTRAR_BANNERS
            )}
          >
            Cadastrar novo
          </ButtonDefault>
        }
      >
        <Pagination
          currentPage={page}
          isLoading={isLoading}
          nPages={totalRegistros}
          setCurrentPage={setPage}
          loadColumnsData={paginationHandle}
          defaultKeyOrdered="titulo"
          defaultOrderDirection="asc"
          tableHeaders={[
            {
              content: <StatusCircle hasValue={false} />,
              key: 'ativo',
              width: '1px',
              isOrderable: false,
            },
            {
              content: 'Título',
              key: 'titulo',
              width: 'auto',
              isOrderable: false,
            },
            {
              content: 'Ordem',
              key: 'ordem',
              width: '100px',
              isOrderable: false,
            },
            {
              content: 'Ações',
              key: 'acoes',
              isOrderable: false,
              width: '10px',
            },
          ]}
          renderTableRows={listaBanners.map((banner) => (
            <Tr key={banner.id}>
              <Td>
                <StatusCircle isActive={banner.ativo} />
              </Td>
              <Td pb="3px" pt="3px">
                <Flex w="full">
                  <Link
                    lineHeight="12.5px"
                    maxW="full"
                    whiteSpace="pre-line"
                    cursor="pointer"
                    onClick={() => handleAlterarBanner(banner.id)}
                  >
                    {banner.titulo}
                  </Link>
                </Flex>
              </Td>
              <Td pb="3px" pt="3px">
                {banner.sequenciaOrdenacao}
              </Td>
              <Td>
                <Flex justifyContent="right">
                  <ActionsMenu
                    id="videoAcoes"
                    items={[
                      {
                        content: 'Editar',
                        onClick: () => handleAlterarBanner(banner.id),
                        possuiFuncionalidade: possuiPermissaoAlterarBanner,
                      },
                      {
                        content: 'Excluir',
                        onClick: () => handleExcluirBanner(banner.id),
                        possuiFuncionalidade: auth.usuarioPossuiPermissao(
                          ConstantFuncionalidades.Zenflix.EXCLUIR_BANNERS
                        ),
                      },
                      {
                        content: 'Inativar',
                        onClick: () => handleInativarBanner(banner.id),
                        possuiFuncionalidade: auth.usuarioPossuiPermissao(
                          ConstantFuncionalidades.Zenflix.ALTERAR_BANNERS
                        ),
                      },
                    ]}
                  />
                </Flex>
              </Td>
            </Tr>
          ))}
        />
      </ContainerListagem>
    </FormProvider>
  );
};

export default ListarBanners;
