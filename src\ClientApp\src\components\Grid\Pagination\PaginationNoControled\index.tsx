import {
  Box,
  Flex,
  HStack,
  Table,
  TableColumnHeaderProps,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  ThemingProps,
  Tr,
} from '@chakra-ui/react';
import { ReactNode, useCallback } from 'react';

import { LoadingDefault } from 'components/Loading';

import { PaginationItem } from '../PaginationItem';

export interface PaginationNoControledProps {
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
  currentPage: number;
  nPages: number;
  asSiblingsCountFixed?: boolean;
  isLoading?: boolean;
  tableHeaders: TableHeader[];
  renderTableRows?: ReactNode;
  size?: ThemingProps['size'];
  itemsPerPage: number;
  variant?: ThemingProps['variant'];
}

interface TableHeader extends Omit<TableColumnHeaderProps, 'children'> {
  key: string;
  content: ReactNode;
  isOrderable?: boolean;
}

function generatePagesArray(from: number, to: number) {
  return [...new Array(to - from)]
    .map((_, index) => from + index + 1)
    .filter((page) => page > 0);
}

const siblingsCount = 1;

export const PaginationNoControled = ({
  nPages,
  currentPage,
  setCurrentPage,
  isLoading,
  itemsPerPage,
  asSiblingsCountFixed,
  renderTableRows,
  tableHeaders,
  size,
  variant,
}: PaginationNoControledProps) => {
  const itemsTotalCount = useCallback(() => {
    return Math.ceil(nPages / itemsPerPage);
  }, [nPages, itemsPerPage])();

  const hasRows = itemsTotalCount > 0;
  const lastPage = Math.ceil(nPages / itemsPerPage);

  let previousPagesFromSiblings = currentPage - 1 - siblingsCount;
  let nextPagesToSiblings = currentPage + siblingsCount;

  if (lastPage < currentPage + siblingsCount && asSiblingsCountFixed) {
    previousPagesFromSiblings -= currentPage + siblingsCount - lastPage;
  }

  if (currentPage <= siblingsCount && asSiblingsCountFixed) {
    nextPagesToSiblings += siblingsCount + 1 - currentPage;
  }
  const previousPages =
    currentPage > 1
      ? generatePagesArray(
          Math.max(previousPagesFromSiblings, 0),
          currentPage - 1
        )
      : [];

  const nextPages =
    currentPage < itemsTotalCount
      ? generatePagesArray(
          currentPage,
          Math.min(nextPagesToSiblings, itemsTotalCount)
        )
      : [];

  return (
    <Box mt="20px" h="full" w="full" color="white">
      <Box borderTopRadius="4px" w="full" overflow="auto">
        {isLoading && <LoadingDefault />}
        <Table variant={variant} size={size}>
          <Thead>
            <Tr>
              {tableHeaders.map(
                ({ content, key, width, fontSize, ...restOfHeader }) => {
                  return (
                    <Th
                      width={width}
                      key={key}
                      userSelect="none"
                      fontWeight="bold"
                    >
                      <Flex fontSize={fontSize} {...restOfHeader}>
                        {content}
                      </Flex>
                    </Th>
                  );
                }
              )}
            </Tr>
          </Thead>

          <Tbody>
            {hasRows ? (
              renderTableRows
            ) : (
              <Tr>
                <Td whiteSpace="nowrap" colSpan={9999} color="black">
                  Nenhum resultado foi encontrado
                </Td>
              </Tr>
            )}
          </Tbody>
        </Table>
      </Box>

      <Flex pt="20px" pb="15px" justifyContent="center" alignItems="center">
        <Flex justifyContent="center" alignItems="center">
          <Text
            fontSize="xs"
            onClick={() => setCurrentPage(1)}
            cursor="pointer"
            p="5px"
            id="firstPage"
            mt="5px"
            color="primary.700"
            h="32px"
            transition="all ease 1s"
          >
            Início
          </Text>
          <HStack spacing="0px" alignItems="center" justifyContent="center">
            <PaginationItem
              color="black"
              bg="gray.50"
              isDisabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              &laquo;
            </PaginationItem>

            {previousPages.length > 0 &&
              previousPages.map((page) => (
                <PaginationItem
                  key={page}
                  id="previousPage"
                  color="black"
                  bg="gray.50"
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </PaginationItem>
              ))}

            <PaginationItem
              colorScheme="none"
              color="primary.600"
              bgColor="none"
              isCurrent
            >
              {currentPage}
            </PaginationItem>

            {nextPages.length > 0 &&
              nextPages.map((page) => (
                <PaginationItem
                  bg="gray.50"
                  color="black"
                  id="nextPage"
                  key={page}
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </PaginationItem>
              ))}

            <PaginationItem
              isDisabled={
                currentPage === itemsTotalCount ||
                nPages === currentPage ||
                itemsTotalCount === 0
              }
              onClick={() => {
                setCurrentPage(currentPage + 1);
              }}
              bg="gray.50"
              color="black"
            >
              &raquo;
            </PaginationItem>
          </HStack>

          <Text
            cursor="pointer"
            h="32px"
            color="primary.700"
            fontSize="xs"
            onClick={() => {
              if (currentPage !== lastPage && nPages !== 0) {
                setCurrentPage(lastPage);
              }
            }}
            p="5px"
            mt="5px"
            id="lastPage"
            transition="all ease 1s"
          >
            Última
          </Text>
        </Flex>
      </Flex>
    </Box>
  );
};
