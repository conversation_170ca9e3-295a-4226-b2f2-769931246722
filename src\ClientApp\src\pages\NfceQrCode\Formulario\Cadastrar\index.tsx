import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import api, { ResponseApi } from 'services/api';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormNfceQrCode } from '..';
import { FormData, formDefaultValues, yupResolver } from '../validationForm';

export const CadastrarNfceQrCode = () => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });

  const { handleSubmit, reset } = formMethods;
  const navigate = useNavigate();

  const getResponseCadastroNfce = useCallback(async (data: FormData) => {
    const response = await api.post<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.CADASTRAR_NFCE_QRCODE,
      {
        ...data,
        servico: data.servico?.value,
        estadoCodigo: data?.estadoCodigo?.value,
        ativo: true,
      }
    );
    return response;
  }, []);

  const handleCadastrarNfceQrCode = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await getResponseCadastroNfce(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      navigate(ConstanteRotas.NFCE_QRCODE);
    }

    setIsLoading(false);
  });

  const handleResetNfceQrCode = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await getResponseCadastroNfce(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      reset(formDefaultValues);
    }

    setIsLoading(false);
  });

  return (
    <LayoutFormPage
      onSubmit={() => handleCadastrarNfceQrCode()}
      onResetSubmit={() => handleResetNfceQrCode()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormNfceQrCode />
      </FormProvider>
    </LayoutFormPage>
  );
};
