import { useCallback, useState } from 'react';
import { Flex, Icon, Link, Td, Tr } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { FiSearch } from 'react-icons/fi';
import { toast } from 'react-toastify';

import auth from 'modules/auth';
import api, { ResponseApi } from 'services/api';

import { EnumStatusCadastros } from 'constants/Enum/enumStatusCadastros';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { formatQueryPagedTableZenflix } from 'helpers/queryParamsFormat';

import { ButtonDefault } from 'components/Button';
import { ActionsMenu } from 'components/ActionsMenu';
import { Pagination } from 'components/Grid/Pagination';
import { ContainerListagem } from 'components/Grid/GridList';
import { SelectDefault } from 'components/Select/SelectDefault';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { ModalCancelar } from 'components/Modal/ModalCancelar';

type FormData = {
  titulo: string;
  ativo: boolean | null;
};

type ArtigoProps = {
  id: string;
  ativo: boolean;
  titulo: string;
  link: string;
};

const ListarArtigos = () => {
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [listaArtigos, setListaArtigos] = useState<ArtigoProps[]>([]);
  const [filtrosAtuais, setFiltrosAtuais] = useState({} as FormData);

  const navigate = useNavigate();

  const formMethods = useForm<FormData>({
    defaultValues: {
      ativo: EnumStatusCadastros.TODOS.value,
      titulo: '',
    },
  });
  const { watch } = formMethods;
  const tituloWatch = watch('titulo');

  const possuiPermissaoAlterarArtigo = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.Zenflix.ALTERAR_ARTIGOS
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<ArtigoProps>>
      >(
        formatQueryPagedTableZenflix(
          ConstantEnderecoWebservice.LISTAR_ARTIGOS,
          gridPaginadaConsulta
        ),
        {
          params: filtrosAtuais,
        }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        if (response.sucesso && response.dados) {
          setListaArtigos(response.dados.registros);
          setTotalRegistros(response.dados.total);
        }
      }

      setIsLoading(false);
    },
    [filtrosAtuais, recarregarListagem]
  );

  const handleExcluir = useCallback(
    async (itemId: string) => {
      ModalCancelar({
        title: 'Você tem certeza?',
        description: 'A ação irá excluir este artigo!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        colorScheme: 'red',
        isVisibleMotivo: false,
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi<ArtigoProps>>(
            `${ConstantEnderecoWebservice.EXCLUIR_ARTIGO}/${itemId}`
          );

          if (response) {
            if (response.avisos) {
              response.avisos.forEach((aviso) => toast.warning(aviso));
            }
            if (response.sucesso) {
              setRecarregarListagem(!recarregarListagem);
              toast.success('Cadastro excluído com sucesso.');
              return true;
            }
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );

  const handleAlterar = useCallback((itemId: string) => {
    if (!possuiPermissaoAlterarArtigo) return;
    navigate(
      SubstituirParametroRota(ConstanteRotas.ARTIGOS_ALTERAR, 'id', itemId)
    );
  }, []);

  return (
    <FormProvider {...formMethods}>
      <ContainerListagem
        inputPesquisa={
          <InputDefault
            maxLength={100}
            placeholder="Buscar por titulo"
            iconLeftElement={FiSearch}
            name="titulo"
            onEnterKeyPress={() => {
              setFiltrosAtuais((filtrosJaAdicionados) => ({
                ...filtrosJaAdicionados,
                titulo: tituloWatch,
              }));
            }}
          />
        }
        filtrosListagem={
          <SelectDefault
            name="ativo"
            placeholder="Selecione um filtro"
            filtrosAtivos
            asControlledByObject={false}
            onSelect={(optionSelecionada) =>
              setFiltrosAtuais((filtrosJaAdicionados) => ({
                ...filtrosJaAdicionados,
                ativo: optionSelecionada?.value,
              }))
            }
            options={EnumStatusCadastros.properties.map((status) => status)}
          />
        }
        buttonCadastrar={
          <ButtonDefault
            onClick={() => navigate(ConstanteRotas.ARTIGOS_CADASTRAR)}
            width={['full', 'full', 'full', '220px']}
            color="white"
            colorScheme="secondary"
            leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
            possuiFuncionalidade={auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.Zenflix.CADASTRAR_ARTIGOS
            )}
          >
            Cadastrar novo
          </ButtonDefault>
        }
      >
        <Pagination
          currentPage={page}
          isLoading={isLoading}
          nPages={totalRegistros}
          setCurrentPage={setPage}
          loadColumnsData={paginationHandle}
          defaultKeyOrdered="titulo"
          defaultOrderDirection="asc"
          tableHeaders={[
            {
              content: <StatusCircle hasValue={false} />,
              key: 'ativo',
              width: '1px',
              isOrderable: false,
            },
            {
              content: 'Título',
              key: 'titulo',
              width: '45%',
              isOrderable: false,
            },
            {
              content: 'Link',
              key: 'link',
              width: '50%',
              isOrderable: false,
            },

            {
              content: 'Ações',
              key: 'acoes',
              isOrderable: false,
              width: '10px',
            },
          ]}
          renderTableRows={listaArtigos?.map((item) => (
            <Tr key={item.id}>
              <Td>
                <StatusCircle isActive={item.ativo} />
              </Td>
              <Td pb="3px" pt="3px">
                <Flex w="full">
                  <Link
                    lineHeight="12.5px"
                    maxW="full"
                    whiteSpace="pre-line"
                    cursor="pointer"
                    onClick={() => handleAlterar(item.id)}
                  >
                    {item.titulo}
                  </Link>
                </Flex>
              </Td>
              <Td pb="3px" pt="3px">
                <Link
                  onClick={() => window.open(item.link, '_blank')}
                  color="blue.500"
                >
                  Ir para link do artigo
                </Link>
              </Td>
              <Td>
                <Flex justifyContent="right">
                  <ActionsMenu
                    id="videoAcoes"
                    items={[
                      {
                        content: 'Editar',
                        onClick: () => handleAlterar(item.id),
                        possuiFuncionalidade: possuiPermissaoAlterarArtigo,
                      },
                      {
                        content: 'Excluir',
                        onClick: () => handleExcluir(item.id),
                        possuiFuncionalidade: auth.usuarioPossuiPermissao(
                          ConstantFuncionalidades.Zenflix.EXCLUIR_ARTIGOS
                        ),
                      },
                    ]}
                  />
                </Flex>
              </Td>
            </Tr>
          ))}
        />
      </ContainerListagem>
    </FormProvider>
  );
};

export default ListarArtigos;
