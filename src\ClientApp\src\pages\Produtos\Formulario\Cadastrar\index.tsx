import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import api, { ResponseApi } from 'services/api';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormCadastrarProduto } from '..';
import { formDefaultValues, yupResolver, FormData } from '../validationForms';

export const CadastrarProdutos = () => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });

  const { handleSubmit, reset } = formMethods;

  const navigate = useNavigate();

  const getCadastroProdutos = useCallback(async (data: FormData) => {
    const response = await api.post<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.CADASTRAR_PRODUTO,
      {
        ...data,
        tipoProduto: data.tipoProduto?.value,
      }
    );
    return response;
  }, []);

  const handleCadastrarProduto = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await getCadastroProdutos(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      navigate(ConstanteRotas.PRODUTOS);
    }

    setIsLoading(false);
  });

  const handleCadastrarInserirNovoProduto = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await getCadastroProdutos(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      reset(formDefaultValues);
    }

    setIsLoading(false);
  });

  return (
    <LayoutFormPage
      onSubmit={() => handleCadastrarProduto()}
      onResetSubmit={() => handleCadastrarInserirNovoProduto()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormCadastrarProduto />
      </FormProvider>
    </LayoutFormPage>
  );
};
