import cypress from 'cypress';

import { hexToRgbA } from 'store/getHexDecimalColor';

import { InputRg as Input } from './exampleInput';

const isCnpj = true;
const valueColorLabel = '#6502b2';

describe('Testing Rg input', () => {
  beforeEach(() => {
    cy.mount(
      <Input
        name="rg"
        isCnpj
        label={isCnpj ? 'Inscrição estadual' : undefined}
        colorLabel={valueColorLabel}
      />
    );
  });

  const valueInput = '12112312312323';

  const validateLabelInput = isCnpj
    ? 'Inscrição estadual'
    : valueInput.length > 9
    ? 'Inscrição estadual'
    : 'RG';

  it('Field formatting is being rendered', () => {
    cy.get('input[name=rg]')
      .type(valueInput, {
        delay: 0,
      })
      .invoke('val')
      .should((value) => {
        expect(String(value).includes('-')).to.eq(isCnpj ? false : true); // passes
      });
  });

  it('Input label is showing', () => {
    cy.testLabelInput(validateLabelInput);
  });

  it('Validating the color of the label', () => {
    cy.testColorLabel(validateLabelInput, hexToRgbA(valueColorLabel));
  });

  it('Render only numbers', () => {
    cy.get('input[name=rg]')
      .type(valueInput, {
        delay: 0,
      })
      .invoke('val')
      .should((value) => {
        expect(value === '').to.eq(false); // passes
      });
  });

  it('Number of correct characters in the input', () => {
    cy.get('input[name=rg]')
      .type(valueInput, {
        delay: 0,
      })
      .invoke('val')
      .should((value) => {
        expect(value ? String(value).length : 0).to.eq(isCnpj ? 14 : 12); // passes
      });
  });
});
