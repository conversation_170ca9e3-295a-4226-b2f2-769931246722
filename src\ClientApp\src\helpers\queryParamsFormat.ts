import { GridPaginadaConsulta } from 'components/Grid/Pagination/Types/validationForms';

export function formatQueryGridPaginadaConsulta(
  gridPaginadaConsulta: GridPaginadaConsulta
): string {
  return (
    `paginaAtual=${gridPaginadaConsulta.currentPage}` +
    `&tamanhoPagina=${gridPaginadaConsulta.pageSize}` +
    `&campoOrdenacao=${gridPaginadaConsulta.orderColumn}` +
    `&direcaoOrdenacao=${gridPaginadaConsulta.orderDirection}`
  );
}

export function formatQueryPaginacaoZenflix(
  gridPaginadaConsulta: GridPaginadaConsulta
): string {
  const skipValue =
    gridPaginadaConsulta.currentPage === 1
      ? 0
      : (gridPaginadaConsulta.currentPage - 1) * gridPaginadaConsulta.pageSize;
  return (
    `tamanhoPagina=${gridPaginadaConsulta.pageSize}` + `&skip=${skipValue}`
  );
}

export function formatQueryObject(obj: any): string {
  return new URLSearchParams(obj).toString();
}

/**
 * Função utilizada para formatar a url de paginação
 * @param  {[string]} url
 * @param  {[GridPaginadaConsulta]} gridPaginadaConsulta
 * @param  {[any]} obj Recebe um objeto preenchido para conversão ou uma string com os parâmetros já formatados. Ex: {nome: 'cor'} ou 'nome=cor'
 * @return {[string]} Url formatada com os parâmetros
 */
export function formatQueryPagedTable(
  url: string,
  gridPaginadaConsulta: GridPaginadaConsulta,
  obj?: any
): string {
  return `${url}?${formatQueryGridPaginadaConsulta(gridPaginadaConsulta)}${
    obj ? `&${formatQueryObject(obj)}` : ''
  }`;
}

export function formatQueryPagedTableZenflix(
  url: string,
  gridPaginadaConsulta: GridPaginadaConsulta,
  obj?: any
): string {
  return `${url}?${formatQueryPaginacaoZenflix(gridPaginadaConsulta)}${
    obj ? `&${formatQueryObject(obj)}` : ''
  }`;
}
