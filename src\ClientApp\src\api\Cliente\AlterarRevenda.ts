import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';

export const clienteAlterarRevenda = (params: {
  clienteId: string;
  revendaId: string;
}) => {
  const { clienteId, revendaId } = params;

  return api.put<void, ResponseApi<void>>(
    ConstantEnderecoWebservice.CLIENTE_ALTERAR_REVENDA.replace(
      '{contaClienteId}',
      clienteId
    ).replace('{revendaId}', revendaId)
  );
};
