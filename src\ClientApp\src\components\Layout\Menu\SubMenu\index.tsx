import { useEffect, useState } from 'react';
import { Box, Flex, Image, Text, useMediaQuery } from '@chakra-ui/react';

import { useLayoutContext } from 'store/Layout';

import IconMultiEmpresa from 'assets/logo.png';

import { ContainerMenu } from './ContainerMenu';
import { NavContantItemMenu } from './NavContantItemMenu';
import { ItemMenu } from './NavContantItemMenu/ItemMenu';

type LayouSubMenuProps = {
  setSizeSubMenu: React.Dispatch<React.SetStateAction<boolean>>;
};

export const LayouSubMenu = ({ setSizeSubMenu }: LayouSubMenuProps) => {
  const { isMenuAberto } = useLayoutContext();

  const [animation, setAnimation] = useState(isMenuAberto);

  const [isLargerThan800] = useMediaQuery('(min-width: 768px)');

  useEffect(() => {
    setAnimation(isLargerThan800);
  }, [isLargerThan800]);

  return (
    <Flex position="relative" h="full">
      <Box position="relative" h="full" w="full" maxWidth="200px">
        <ContainerMenu 
          background="primary.900"
          h="full"
          color="white"
          transform={!animation ? 'translateX(0)' : 'translateX(-100%)'}
          width="50px"
          pt="20px"
        >
          <Flex
            onClick={() => {
              if (isLargerThan800) {
                setTimeout(() => {
                  setAnimation(!animation);
                  setSizeSubMenu(false);
                }, 800);
              }
            }}
            pb="15px"
            justifyContent="center"
            alignItems="center"
            borderBottom="1px"
            borderColor="secondary.900"
          >
            <Image
              cursor="pointer"
              objectFit="cover"
              h="30px"
              w="30px"
              src={IconMultiEmpresa}
            />
          </Flex>
          <NavContantItemMenu hasDisplayIcon={true} />
        </ContainerMenu>

        <ContainerMenu
          transform={animation ? 'translateX(0)' : 'translateX(-100%)'}
          w="200px"
          pt="5px"
          h="full"
          color="white"
          boxShadow="5px 0px 6px 0px rgba(0, 0, 0, 0.2) !important"
          background="primary.900"
        >
          <Flex
            onClick={() => {
              setAnimation(!animation);
              setSizeSubMenu(true);
            }}
            pb="10px"
            pt="18px"
            pl="6px"
            alignItems="center"
            borderBottom="1px"
            borderColor="secondary.900"
          >
            <Image
              cursor="pointer"
              objectFit="cover"
              h="30px"
              mr="10px"
              src={IconMultiEmpresa}
            />
            <Text
              cursor="pointer"
              mr="25px"
              fontFamily="Montserrat"
              fontWeight="extrabold"
              fontSize="lg"
              textAlign="center"
            >
              Stargate
            </Text>
          </Flex>
          <NavContantItemMenu hasDisplayIcon={false} />
        </ContainerMenu>
      </Box>

      <ItemMenu animation={animation} />
    </Flex>
  );
};
