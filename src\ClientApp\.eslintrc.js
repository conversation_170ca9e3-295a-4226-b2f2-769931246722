module.exports = {
  extends: [
    'airbnb-typescript',
    'airbnb/hooks',
    'plugin:@typescript-eslint/recommended',
    'plugin:jest/recommended',
    'prettier',
    'prettier/react',
    'prettier/@typescript-eslint',
    'plugin:prettier/recommended',
    'plugin:cypress/recommended',
  ],
  plugins: ['react', '@typescript-eslint', 'jest', 'react-hooks', 'cypress'],
  env: {
    browser: true,
    es6: true,
    jest: true,
    'cypress/globals': true,
  },
  globals: {
    Atomics: 'readonly',
    SharedArrayBuffer: 'readonly',
    cy: true,
  },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 2020,
    sourceType: 'module',
    project: './tsconfig.json',
    createDefaultProgram: true,
  },
  rules: {
    'prettier/prettier': [
      'error',
      {
        endOfLine: 'auto',
      },
    ],
    'react/require-default-props': 0,
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    // "@typescript-eslint/explicit-function-return-type": "off",
    // "@typescript-eslint/explicit-member-accessibility": "off",
    // "react-hooks/rules-of-hooks": "error",
    'react-hooks/exhaustive-deps': 'warn',
    // "jsx-a11y/label-has-associated-control": "off",
    'react/jsx-props-no-spreading': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    // "no-param-reassign": ["error", { "props": false }]
    // "jsx-a11y/anchor-is-valid": "off",
    'react/prop-types': 0,
    'import/prefer-default-export': 'off',
    'no-debugger': 'warn',
    'react/react-in-jsx-scope': 'off',
    'no-nested-ternary': 'off',
    'cypress/no-assigning-return-values': 'error',
    'cypress/no-unnecessary-waiting': 'off',
    'cypress/assertion-before-screenshot': 'warn',
    'cypress/no-force': 'warn',
    'cypress/no-async-tests': 'error',
    'cypress/no-pause': 'error',
    'jest/expect-expect': [
      'error',
      {
        assertFunctionNames: ['expect', '**.should'],
      },
    ],
  },
};
