import { useState } from 'react';
import { create, InstanceProps } from 'react-modal-promise';
import {
  Flex,
  Text,
  useDisclosure,
  useMediaQuery,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalProps,
} from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import { LoadingDefault } from 'components/Loading';
import { ModalDefaultChakra } from 'components/Modal/ModalDefaultChakra';
import { Calendario } from 'components/Calendario';

type ModalBaixarFaturamento = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> & {
  atualizarListagem: () => void;
  faturamentoId: string;
  dataEmissao: string;
} & InstanceProps<ModalProps>;

export const ModalBaixarFaturamento = create<
  ModalBaixarFaturamento,
  ModalProps
>(
  ({
    onResolve,
    onReject,
    dataEmissao,
    faturamentoId,
    atualizarListagem,
    ...rest
  }) => {
    const [isLoading, setIsLoading] = useState(false);

    const formMethods = useForm();
    const { handleSubmit } = formMethods;

    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });
    const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
    

    const handleBaixarFaturamento = handleSubmit(async (data) => {
      const { dataBaixa } = data;
      if (dataBaixa) {
        setIsLoading(true);
        const response = await api.put<void, ResponseApi>(
          `${
            ConstantEnderecoWebservice.BAIXAR_FATURAMENTO_ASSINATURA
          }?faturamentoId=${faturamentoId}&dataBaixa=${dataBaixa.toISOString()}`
        );
        if (response.sucesso) {
          toast.success('Faturamento baixado com sucesso!');
          atualizarListagem();
          onClose();
        }
        setIsLoading(false);
      }
    });

    return (
      <ModalDefaultChakra
        {...rest}
        isCentered
        size={isLargerThan900 ? '6xl' : 'full'}
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <FormProvider {...formMethods}>
          <ModalContent
            h={['full', 'fit-content']}
            maxH="full"
            w={['full', '5xl', 'lg']}
            bg="gray.50"
          >
            {isLoading && <LoadingDefault />}
            <ModalHeader
              borderTopRadius="5px"
              bg="primary.500"
              pb="15px"
              borderBottom="1px solid"
              borderColor="gray.50"
            >
              <Flex
                color="white"
                pt="5px"
                justifyContent="space-between"
                alignItems="center"
              >
                <Text pl="2px" color="white">
                  Selecione a data da baixa
                </Text>
                <ModalCloseButton
                  id="closeButton"
                  mt="13px"
                  mr="10px"
                  color="gray.50"
                />
              </Flex>
            </ModalHeader>
            <ModalBody backgroundColor="white" p="0">
              <Flex alignItems="center" justifyContent="center" padding="24px">
                <Calendario
                  name="dataBaixa"
                  secondaryStyle
                  minDate={new Date(dataEmissao)}
                  maxDate={new Date()}
                  onConfirmButton={() => handleBaixarFaturamento()}
                  onCancelButton={() => onClose()}
                  onConfirmButtonText="Baixar"
                />
              </Flex>
            </ModalBody>
          </ModalContent>
        </FormProvider>
      </ModalDefaultChakra>
    );
  }
);
