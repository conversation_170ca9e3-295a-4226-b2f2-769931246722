import { ChakraProvider } from '@chakra-ui/react';

import { theme } from 'theme';

import { PaginationNoControled } from './PaginationNoControled.stories';

describe('Testing pagination component', () => {
  beforeEach(() => {
    cy.mount(
      <ChakraProvider theme={theme}>
        <PaginationNoControled />
      </ChakraProvider>
    );
  });

  it('validating click on next pages', () => {
    cy.get('#nextPage').should('be.visible').click();
  });

  it('validating click on previous pages', () => {
    cy.get('#nextPage').should('be.visible').click();
    cy.get('#previousPage').should('be.visible').click();
  });

  it('validating click to last page', () => {
    cy.get('#lastPage').should('be.visible').click();
  });

  it('validating click to first page', () => {
    cy.get('#lastPage').should('be.visible').click();
    cy.get('#firstPage').should('be.visible').click();
  });
});
