import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';
import { SelectOptions } from 'components/Select/SelectDefault';

export type FormData = {
  tituloThumbnail: string;
  titulo: string;
  ativo: boolean;
  video: string;
  descricao: string;
  tema: string | null;
  categoriaTreinamento: string | null;
  sequenciaOrdenacao: number;
  dataHoraPublicacao: Date;
  planos: SelectOptions[] | null;
  sistemas: SelectOptions[] | null;
  telas: SelectOptions[] | null;
};

export interface AulaResponse
  extends Omit<FormData, 'tema' | 'categoriaTreinamento'> {
  temaId: string;
  categoriaTreinamentoId: string;
}

export const formDefaultValues = {
  tituloThumbnail: '',
  titulo: '',
  ativo: true,
  video: '',
  descricao: '',
  tema: null,
  categoriaTreinamento: null,
  sequenciaOrdenacao: 0,
  dataHoraPublicacao: new Date(),
  planos: null,
  sistemas: null,
  telas: null,
};

const schema = yup.object().shape({
  video: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  dataHoraPublicacao: yup
    .date()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  tituloThumbnail: yup
    .string()
    .nullable()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  titulo: yup
    .string()
    .max(100, EnumValidacoesSistema.MAX_LENGTH_100)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  descricao: yup
    .string()
    .max(5000, EnumValidacoesSistema.MAX_LENGTH_5000)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  tema: yup
    .string()
    .nullable()
    .default(null)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  categoriaTreinamento: yup.string().default(null).nullable(),
  sequenciaOrdenacao: yup
    .number()
    .integer()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  planos: yup
    .array()
    .nullable()
    .min(1, EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  sistemas: yup.array().nullable(),
  telas: yup.array().nullable(),
});

export const yupResolver = yupResolverInstance(schema);
