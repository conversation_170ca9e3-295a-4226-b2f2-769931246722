import JwtDecode from 'jwt-decode';

import { EnumTipoUsuario } from 'constants/Enum/enumTipoUsuario';
import { formattingCodeInLabel } from 'helpers/format/formattingCodeInLabel';

const TOKEN = 'token';
const REFRESH_TOKEN = 'refreshToken';
export interface GetDadosTokenResponse {
  sub: string;
  jti: string;
  nbf: number;
  iat: number;
  unique_name: string;
  userId: string;
  userName: string;
  revendaId: string;
  revendaNome: string;
  tipoUsuario: string;
  exp: number;
  iss: string;
  aud: string;
  produtoIdentificadorUrl: string;
}

export default {
  setToken(dados: any) {
    localStorage.setItem(TOKEN, dados.token);
    localStorage.setItem(REFRESH_TOKEN, dados.refreshToken);
  },

  getDadosToken(): any {
    if (localStorage.getItem(TOKEN) !== null) {
      return JwtDecode(this.getToken());
    }
    return false;
  },

  getToken(): any {
    return localStorage.getItem(TOKEN);
  },

  getRefreshToken(): any {
    return localStorage.getItem(REFRESH_TOKEN);
  },

  isAuthenticated(): boolean {
    return this.getToken() != null;   
  },

  isDevMode(): boolean {
    if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
      return true;
    } else {
      return false;
    }
  },

  getUserName(): string {
    if (this.isAuthenticated()) {
      const getDataUser: GetDadosTokenResponse = this.getDadosToken();
      return getDataUser.userName;
    }

    return '';
  },

  getRevendaName(): string {
    if (this.isAuthenticated()) {
      const getDataUser: GetDadosTokenResponse = this.getDadosToken();
      return getDataUser.revendaNome;
    }

    return '';
  },

  getRevendaId(): string {
    if (this.isAuthenticated()) {
      const getDataUser: GetDadosTokenResponse = this.getDadosToken();
      return getDataUser.revendaId;
    }

    return '';
  },

  getExpSessao(): boolean {
    if (this.isAuthenticated()) {
      const getDataUser: GetDadosTokenResponse = this.getDadosToken();

      const dataExp = new Date(getDataUser.exp * 1000);

      return new Date() > dataExp;
    }

    return true;
  },

  getUserNameDominio(): string {
    if (this.isAuthenticated()) {
      const getDataUser: GetDadosTokenResponse = this.getDadosToken();
      return getDataUser.produtoIdentificadorUrl;
    }

    return '';
  },

  getTipoUsuarioValue(): number {
    const getDataUser: GetDadosTokenResponse = this.getDadosToken();

    if (getDataUser) {
      return Number(getDataUser.tipoUsuario);
    }
    return EnumTipoUsuario.REVENDA_ADMIN;
  },

  getTipoUsuarioName(): string {
    const getDataUser: GetDadosTokenResponse = this.getDadosToken();
    if (getDataUser) {
      return formattingCodeInLabel(
        EnumTipoUsuario,
        Number(getDataUser.tipoUsuario)
      ) as string;
    }
    return EnumTipoUsuario.properties[4].label;
  },

  usuarioPossuiPermissao(funcionalidade: number[]): boolean {
    const tipoDeUsuario = this.getTipoUsuarioValue();

    return funcionalidade.includes(tipoDeUsuario);
  },

  clearToken() {
    localStorage.removeItem(TOKEN);
    localStorage.removeItem(REFRESH_TOKEN);
  },

  clearTokenAndRedirect() {
    this.clearToken();
    window.location.reload();
  },

  getUserId(): string {
    const getDataUser: GetDadosTokenResponse = this.getDadosToken();
    if (getDataUser) {
      return getDataUser.userId;
    }
    return '';
  },
};
