import { VStack } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import auth from 'modules/auth';
import api, { ResponseApi } from 'services/api';
import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { submitInputOnEnter } from 'hook/submitInputOnEnter';

import { ButtonDefault } from 'components/Button';
import { InputAutenticacao } from 'components/Input/InputAutenticacao';
import { Header } from 'components/Layout/Header';

import { yupResolver } from './validationForms';

export const Login = () => {
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  const formMethods = useForm({
    resolver: yupResolver,
  });

  const {
    handleSubmit,
    formState: { isValid, errors },
  } = formMethods;

  const handleSubmitLogin = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await api.post<void, ResponseApi<any>>(
      ConstantEnderecoWebservice.AUTENTICACAO_LOGIN,
      data
    );

    if (response.sucesso) {
      auth.setToken(response.dados);
      navigate(ConstanteRotas.ASSINATURAS);
      window.location.reload();
      return;
    }

    setIsLoading(false);
  });

  const handleEsqueciSenha = () => {
    navigate(ConstanteRotas.RECUPERAR_SENHA);
  };

  const isAuthenticated = (() => {
    return auth.isAuthenticated();
  })();

  useEffect(() => {
    if (isAuthenticated) {
      navigate(ConstanteRotas.ASSINATURAS);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    submitInputOnEnter(handleSubmitLogin);
  }, []);

  return (
    <VStack spacing={9}>
      <FormProvider {...formMethods}>
        <VStack as="form" w="full" spacing={8}>
          <Header />
          <VStack spacing={7} w="full">
            <InputAutenticacao
              name="usuario"
              variant="floating"
              colorLabel="white"
              id="usuario"
              autoFocus
              label="Login"
              bg="none"
              autoComplete="usuario"
              isDisabled={isLoading}
              isInvalid={!!errors?.usuario?.message}
              maxLength={256}
            />

            <InputAutenticacao
              id="senha"
              name="senha"
              type="password"
              label="Senha"
              bg="none"
              autoComplete="senha"
              variant="floating"
              colorLabel="white"
              isPassword
              isInvalid={!!errors?.senha?.message}
              isDisabled={isLoading}
              maxLength={50}
            />
          </VStack>

          <ButtonDefault
            onClick={() => handleSubmitLogin()}
            color={!isValid ? 'primary' : 'white'}
            colorScheme="secondary"
            w="full"
            id="button"
            isDisabled={isLoading || !isValid}
            isLoading={isLoading}
            possuiFuncionalidade={true}
          >
            Entrar
          </ButtonDefault>
        </VStack>

        <VStack spacing={3.5}>
          <ButtonDefault
            textDecoration="none"
            variant="link"
            fontSize="xs"
            width="220px"
            _hover={{
              color: 'aquamarine.400',
            }}
            onClick={() => handleEsqueciSenha()}
            color="white"
            possuiFuncionalidade={true}
          >
            Esqueci minha senha {'>'}
          </ButtonDefault>
        </VStack>
      </FormProvider>
    </VStack>
  );
};
