import { useCallback, useEffect, useRef, useState } from 'react';
import { Flex, GridItem, Td, Tr, Text, Tag } from '@chakra-ui/react';
import { toast } from 'react-toastify';
import { useLocation } from 'react-router-dom';
import { isSameMonth } from 'date-fns';

import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { EnumSituacaoFaturamento } from 'constants/Enum/enumSituacaoFaturamento';
import { moneyMask } from 'helpers/format/fieldsMasks';
import formatUTCToLocateDate from 'helpers/format/formatUTCToLocateDate';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import { formatDateMesAno } from 'helpers/format/formatStringDate';

import { ActionsMenu } from 'components/ActionsMenu';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { LoadingDefault } from 'components/Loading';
import { ButtonDefault } from 'components/Button';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { Pagination } from 'components/Grid/Pagination';
import { ModalDetalhesFaturamento } from 'components/Modal/ModalDetalhesFaturamento';
import { ModalLancarValorFaturamento } from 'components/Modal/ModalLancarValorFaturamento';

import { FaturamentoAssinaturaProps, FaturamentoInfoProps } from './validationForm';

type UrlParams = {
  state: { assinaturaId: string; fantasia: string };
};

export const FaturamentoAssinatura = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [listaFaturamentos, setListaFaturamentos] = useState<FaturamentoAssinaturaProps[]>([]);
  const [page, setPage] = useState(1);
  const [totalRegistros, setTotalRegistros] = useState(0);

  const faturamentoInfo = useRef<FaturamentoInfoProps | null>(null);

  const { state: dadosFaturamento }: UrlParams = useLocation();

  const nomeFantasia = faturamentoInfo.current?.nomeFantasia;

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      if (!faturamentoInfo.current) return;
      setIsLoading(true);

      const { idAssinatura } = faturamentoInfo.current;

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<FaturamentoAssinaturaProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_FATURAMENTOS_ASSINATURA,
          gridPaginadaConsulta
        ),
        {
          params: {
            assinaturaId: idAssinatura,
          },
        }
      );

      if (response.sucesso) {
        // Processar os dados para adicionar subTotal e acrescimoDesconto se necessário
        const registrosProcessados = response.dados.registros.map(faturamento => {
          return {
            ...faturamento
          };
        });

        setTotalRegistros(response.dados.total);
        setListaFaturamentos(registrosProcessados);
      }

      setIsLoading(false);
    },
    [recarregarListagem]
  );

  const handleGerarFatura = useCallback(
    async () => {
      if (!faturamentoInfo.current) return;

      const { idAssinatura } = faturamentoInfo.current;
      const dataAtual = new Date();

      ModalWarning({
        title: 'Você tem certeza?',
        description: `Confirma a Geração da Fatura para ${formatDateMesAno(dataAtual)}`,
        confirmButtonText: 'Sim, confirmar!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.post<void, ResponseApi>(
            `${ConstantEnderecoWebservice.ASSINATURA_GERAR_FATURA}?assinaturaID=${idAssinatura}&data=${dataAtual.toISOString()}`
          );
          if (response.sucesso) {
            toast.success('A Fatura foi gerada com sucesso');
            setRecarregarListagem(!recarregarListagem);
            return true;
          }
          return true;
        },
      });
    },
    [recarregarListagem]
  );

  const handleExcluirFaturamento = useCallback(
    async (id: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'A ação vai excluir esse faturamento!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            ConstantEnderecoWebservice.EXCLUIR_FATURAMENTO_ASSINATURA,
            { params: { faturamentoId: id } }
          );

          if (response.sucesso) {
            setRecarregarListagem(!recarregarListagem);
            toast.success('O faturamento foi excluído com sucesso');
            return true;
          }
          return true;
        },
      });
    },
    [recarregarListagem]
  );

  const atualizarListagem = useCallback(() => {
    setRecarregarListagem(!recarregarListagem);
  }, [recarregarListagem]);

  useEffect(() => {
    if (dadosFaturamento) {
      const { fantasia, assinaturaId } = dadosFaturamento;
      faturamentoInfo.current = {
        nomeFantasia: fantasia,
        idAssinatura: assinaturaId,
      };
    }
  }, [dadosFaturamento]);

  return (
    <>
      {isLoading && <LoadingDefault />}

      <SimpleGridForm borderRadius="md" py="12px">
        <GridItem colSpan={[12, 12, 8, 8]}>
          <Flex direction="column" gap="10px" w="full">
            <Text color="secondary.500" fontSize="sm">
              Assinatura
            </Text>
            <Text>{nomeFantasia}</Text>
          </Flex>
        </GridItem>
        <GridItem colSpan={[12, 12, 4, 4]}>
          <Flex w="full" alignItems="center" justifyContent="right" h="full">
            <ButtonDefault
              onClick={handleGerarFatura}
              width="220px"
              color="white"
              colorScheme="secondary"
              aria-label="Gerar nova fatura"
              possuiFuncionalidade={auth.usuarioPossuiPermissao(
                ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_GERAR_FATURAMENTO
              )}
            >
              Gerar Fatura
            </ButtonDefault>
          </Flex>
        </GridItem>
      </SimpleGridForm>
      <Pagination
        loadColumnsData={paginationHandle}
        nPages={totalRegistros}
        currentPage={page}
        setCurrentPage={setPage}
        isLoading={isLoading}
        defaultOrderDirection="desc"
        defaultKeyOrdered="dataEmissao"
        tableHeaders={[
          {
            content: 'Ações',
            key: 'Acoes',
            isOrderable: false,
            isNumeric: true,
            width: '0.5px',
          },
          {
            content: 'Competência',
            key: 'dataEmissao',
            isOrderable: true,
            width: '10%',
          },
          {
            content: 'Pagamento',
            key: 'dataPagamento',
            isOrderable: true,
            width: '10%',
          },
          {
            content: 'SubTotal',
            key: 'subTotal',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '15%',
          },
          {
            content: 'Acrésc/Desc',
            key: 'acrescimoDesconto',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '15%',
          },
          {
            content: 'Valor',
            key: 'valor',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '15%',
          },
          {
            content: '',
            key: 'espaco',
            isOrderable: false,
            isNumeric: true,
            width: '100%',
          },
        ]}
        renderTableRows={listaFaturamentos.map((faturamento) => (
          <Tr key={faturamento.id}>
            <Td isNumeric>
              <Flex justifyContent="flex-end">
                <ActionsMenu
                  id="mostrarMais"
                  menuZIndex="modal"
                  items={[
                    {
                      content: 'Ver detalhes',
                      onClick: () => {
                        if (faturamentoInfo.current) {
                          ModalDetalhesFaturamento({
                            faturamentoId: faturamento.id,
                          });
                        }
                      },
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_VER_DETALHES
                      ),
                    },
                    {
                      content: 'Lançar Valor',
                      onClick: () => {
                        ModalLancarValorFaturamento({
                          faturamentoId: faturamento.id,
                          atualizarListagem: atualizarListagem,
                        });
                      },
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_LANCAR_VALOR
                      ),
                    },
                    {
                      content: 'Excluir',
                      onClick: () => {
                        handleExcluirFaturamento(faturamento.id);
                      },
                      isDisabled: !auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_EXCLUIR
                      ) 
                      || !(isSameMonth(new Date(faturamento.dataEmissao), new Date()) 
                      || new Date(faturamento.dataEmissao) > new Date()),
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_EXCLUIR
                      ),
                    },
                  ]}
                />
              </Flex>
            </Td>
            <Td>{ formatDateMesAno(faturamento.dataEmissao) }</Td>
            <Td>
              { formatDateMesAno(faturamento.dataVencimento) }
            </Td>
            <Td isNumeric>
              {moneyMask(faturamento.subTotal || 0, true)}
            </Td>
            <Td isNumeric>
              {moneyMask(faturamento.valorAcrescimoDesconto || 0, true)}
            </Td>
            <Td w="15%" isNumeric>
              {moneyMask(faturamento.valorTotalRepasse, true)}
            </Td>

            <Td w="100%" />
          </Tr>
        ))}
      />
    </>
  );
};
