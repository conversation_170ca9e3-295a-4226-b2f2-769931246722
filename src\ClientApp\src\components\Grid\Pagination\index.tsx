import {
  Box,
  Divider,
  Flex,
  H<PERSON><PERSON>ck,
  Icon,
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  SystemStyleObject,
  Table,
  TableColumnHeaderProps,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  ThemingProps,
  Tr,
} from '@chakra-ui/react';
import { ReactNode, useEffect, useRef, useState } from 'react';
import { FiArrowDown } from 'react-icons/fi';
import { FormProvider, useForm } from 'react-hook-form';

import { EnumPageSizeOptions } from 'constants/Enum/enumPageSizeOptionsEnum';

import { LoadingDefault } from 'components/Loading';

import { PaginationItem } from './PaginationItem';
import { PaginationData, SelectResultPageProps } from './Types/validationForms';

interface PaginationProps {
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
  currentPage: number;
  nPages: number;
  asSiblingsCountFixed?: boolean;
  isLoading?: boolean;
  tableHeaders: TableHeader[];
  renderTableRows?: ReactNode;
  size?: ThemingProps['size'];
  variant?: ThemingProps['variant'];
  canResetOrderColumnWhenLoad?: boolean;
  defaultKeyOrdered: string;
  defaultOrderDirection: 'asc' | 'desc';
  w?: string | string[];
  sx?: SystemStyleObject;
  isBorderWidth?: boolean;
  loadColumnsData: (paginationData: PaginationData) => Promise<void>;
}

type FormData = {
  resultPage: SelectResultPageProps;
};

interface TableHeader extends Omit<TableColumnHeaderProps, 'children'> {
  key: string;
  content: ReactNode;
  isOrderable?: boolean;
}

function generatePagesArray(from: number, to: number) {
  return [...new Array(to - from)]
    .map((_, index) => from + index + 1)
    .filter((page) => page > 0);
}

const siblingsCount = 1;

export const Pagination = ({
  nPages,
  currentPage,
  setCurrentPage,
  isBorderWidth = true,
  isLoading,
  defaultKeyOrdered,
  defaultOrderDirection,
  w = undefined,
  sx,
  canResetOrderColumnWhenLoad = true,
  loadColumnsData,
  asSiblingsCountFixed,
  renderTableRows,
  tableHeaders,
  size,
  variant = 'filled',
}: PaginationProps) => {
  const [paginationData, setPaginationData] = useState<PaginationData>({
    currentPage,
    orderColumn: '',
    orderDirection: 'asc',
    pageSize: 50,
  });
  const [atualizarPaginacao, setAtualizarPaginacao] = useState(false);
  const [loadingPagination, setLoadingPagination] = useState(false);

  const latestProps = useRef({ loadColumnsData });
  const firstUpdate = useRef(true);

  const formMethods = useForm<FormData>({
    defaultValues: {
      resultPage: EnumPageSizeOptions[50],
    },
  });

  const resultPageWatch = formMethods.watch('resultPage');

  const hasRows = nPages > 0;
  const lastPage = Math.ceil(nPages / resultPageWatch?.value);

  let previousPagesFromSiblings = currentPage - 1 - siblingsCount;
  let nextPagesToSiblings = currentPage + siblingsCount;

  if (lastPage < currentPage + siblingsCount && asSiblingsCountFixed) {
    previousPagesFromSiblings -= currentPage + siblingsCount - lastPage;
  }

  if (currentPage <= siblingsCount && asSiblingsCountFixed) {
    nextPagesToSiblings += siblingsCount + 1 - currentPage;
  }

  const previousPages =
    currentPage > 1
      ? generatePagesArray(
          Math.max(previousPagesFromSiblings, 0),
          currentPage - 1
        )
      : [];

  const nextPages =
    currentPage < lastPage
      ? generatePagesArray(currentPage, Math.min(nextPagesToSiblings, lastPage))
      : [];

  const handleRefreshPage = (valuePage: number) => {
    setCurrentPage(valuePage);
    setPaginationData((prev) => ({ ...prev, currentPage: valuePage }));
  };

  function handleChangeOrderColumn(newOrderColumn: string) {
    setPaginationData((prev) => ({
      ...prev,
      orderColumn: newOrderColumn,
      orderDirection:
        prev.orderColumn === newOrderColumn && prev.orderDirection === 'asc'
          ? 'desc'
          : 'asc',
    }));
  }

  useEffect(() => {
    setLoadingPagination(true);
    setTimeout(() => {
      setAtualizarPaginacao((valorAnterior) => !valorAnterior);
      setLoadingPagination(false);
    }, 1000);
  }, []);

  useEffect(() => {
    if (firstUpdate.current) {
      firstUpdate.current = false;
    } else {
      setCurrentPage(1);
      setPaginationData((prev) => ({
        ...prev,
        currentPage: 1,
        orderColumn: canResetOrderColumnWhenLoad
          ? defaultKeyOrdered
          : prev.orderColumn,
        orderDirection: canResetOrderColumnWhenLoad
          ? defaultOrderDirection
          : prev.orderDirection,
      }));
    }
  }, [loadColumnsData, atualizarPaginacao]);

  useEffect(() => {
    latestProps.current = { loadColumnsData };
  }, [loadColumnsData]);

  useEffect(() => {
    if (paginationData.orderColumn !== '') {
      latestProps.current.loadColumnsData(paginationData);
    }
  }, [paginationData, atualizarPaginacao]);

  return (
    <Box
      bg="white"
      w="full"
      borderWidth={isBorderWidth ? '0.7px' : undefined}
      borderRadius="8px"
      borderColor="gray.100"
      marginTop={['4', '0']}
    >
      <FormProvider {...formMethods}>
        {(isLoading || loadingPagination) && <LoadingDefault />}
        <Box overflow="auto" borderTopRadius="4px" w="full">
          <Table sx={sx} w={w} variant={variant} size={size}>
            <Thead>
              <Tr>
                {tableHeaders.map(
                  ({ content, key, isOrderable = true, ...restOfHeader }) => {
                    const isOrdered = key === paginationData.orderColumn;

                    return (
                      <Th
                        key={key}
                        whiteSpace="nowrap"
                        userSelect="none"
                        {...restOfHeader}
                        fontWeight={isOrdered ? 'bold' : 'normal'}
                        onClick={
                          isOrderable && !isLoading
                            ? () => {
                                handleChangeOrderColumn(key);
                              }
                            : undefined
                        }
                      >
                        <Flex alignItems="center" minW="24px">
                          {content}
                          {isOrderable &&
                            key === paginationData.orderColumn && (
                              <Icon
                                as={FiArrowDown}
                                transform={`rotate(${
                                  paginationData.orderDirection === 'asc'
                                    ? '0deg'
                                    : '-180deg'
                                })`}
                                transition="transform .3s"
                                ml="1"
                              />
                            )}
                        </Flex>
                      </Th>
                    );
                  }
                )}
              </Tr>
            </Thead>
            <Tbody>
              {hasRows ? (
                renderTableRows
              ) : (
                <Tr>
                  <Td whiteSpace="nowrap" fontSize="xs" colSpan={9999}>
                    Nenhum resultado foi encontrado
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>

        <Flex
          pt="15px"
          pb="15px"
          borderTop={isBorderWidth ? '1px' : '0'}
          borderColor="gray.100"
          ml={isBorderWidth ? [7, 7, 6, 6, 7] : undefined}
          mr={isBorderWidth ? [7, 7, 6, 6, 7] : undefined}
          justifyContent="space-between"
          alignItems="center"
          direction={['column', 'column', 'row']}
        >
          <HStack
            color="gray.700"
            fontSize={['2xs', 'xs']}
            pl={isBorderWidth ? [0, 9, 1] : undefined}
            spacing={2}
          >
            <Text>Mostrando</Text>
            <Box>
              <Menu>
                <MenuButton
                  as={IconButton}
                  borderRadius="6px"
                  aria-label="Options"
                  icon={<>{paginationData.pageSize}</>}
                  variant="outline"
                />
                <MenuList>
                  <MenuItem
                    onClick={() => {
                      setPaginationData((prev) => ({
                        ...prev,
                        pageSize: EnumPageSizeOptions[10].value,
                      }));
                    }}
                  >
                    10
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      setPaginationData((prev) => ({
                        ...prev,
                        pageSize: EnumPageSizeOptions[25].value,
                      }));
                    }}
                  >
                    25
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      setPaginationData((prev) => ({
                        ...prev,
                        pageSize: EnumPageSizeOptions[50].value,
                      }));
                    }}
                  >
                    50
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      setPaginationData((prev) => ({
                        ...prev,
                        pageSize: EnumPageSizeOptions[100].value,
                      }));
                    }}
                  >
                    100
                  </MenuItem>
                    <MenuItem
                    onClick={() => {
                      setPaginationData((prev) => ({
                        ...prev,
                        pageSize: EnumPageSizeOptions[300].value,
                      }));
                    }}
                  >
                    300
                  </MenuItem>
                    <MenuItem
                    onClick={() => {
                      setPaginationData((prev) => ({
                        ...prev,
                        pageSize: EnumPageSizeOptions[500].value,
                      }));
                    }}
                  >
                    500
                  </MenuItem>
                </MenuList>
              </Menu>
            </Box>

            <Text>resultados por página</Text>
          </HStack>
          <Flex pt={[3, 3, 0]} justifyContent="center" alignItems="center">
            <Text
              fontSize="xs"
              color="secondary.600"
              onClick={() => {
                if (currentPage !== 1) {
                  setCurrentPage(1);
                  handleRefreshPage(1);
                }
              }}
              cursor="pointer"
              borderWidth="2px"
              borderTopLeftRadius="8px"
              borderBottomStartRadius="8px"
              borderColor="white"
              p="5px"
              id="firstPage"
              h="32px"
              _hover={{
                borderColor: 'secondary.600',
                background: 'gray.50',
              }}
              transition="all ease 1s"
            >
              Início
            </Text>
            <HStack spacing="0px" alignItems="center" justifyContent="center">
              <PaginationItem
                isDisabled={currentPage === 1}
                color="secondary.800"
                onClick={() => handleRefreshPage(currentPage - 1)}
              >
                &laquo;
              </PaginationItem>

              {previousPages.length > 0 &&
                previousPages.map((page) => (
                  <PaginationItem
                    key={page}
                    color="secondary.500"
                    id="previousPage"
                    onClick={() => handleRefreshPage(page)}
                  >
                    {page}
                  </PaginationItem>
                ))}

              <PaginationItem bgColor="secondary.500" color="white" isCurrent>
                {currentPage}
              </PaginationItem>

              {nextPages.length > 0 &&
                nextPages.map((page) => (
                  <PaginationItem
                    key={page}
                    color="secondary.500"
                    id="nextPage"
                    onClick={() => handleRefreshPage(page)}
                  >
                    {page}
                  </PaginationItem>
                ))}

              <PaginationItem
                isDisabled={
                  currentPage === lastPage || nPages < paginationData.pageSize
                }
                color="secondary.800"
                onClick={() => handleRefreshPage(currentPage + 1)}
              >
                &raquo;
              </PaginationItem>
            </HStack>

            <Text
              cursor="pointer"
              h="32px"
              color="secondary.600"
              fontSize="xs"
              onClick={() => {
                if (currentPage !== lastPage && nPages !== 0) {
                  setCurrentPage(lastPage);
                  handleRefreshPage(lastPage);
                }
              }}
              borderWidth="2px"
              borderTopRightRadius="8px"
              borderBottomEndRadius="8px"
              borderColor="white"
              p="5px"
              id="lastPage"
              _hover={{
                borderColor: 'secondary.600',
                background: 'gray.50',
              }}
              transition="all ease 1s"
            >
              Última
            </Text>
          </Flex>
          <Flex
            pt={[3, 3, 0]}
            whiteSpace="nowrap"
            color="gray.700"
            pr={isBorderWidth ? [0, 0, 1] : undefined}
            fontSize="xs"
          >
            Foram encontrados {nPages} resultados
          </Flex>
        </Flex>
      </FormProvider>
    </Box>
  );
};
