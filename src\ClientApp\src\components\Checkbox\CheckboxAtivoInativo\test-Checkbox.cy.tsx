import { ChakraProvider } from '@chakra-ui/provider';

import { hexToRgbA } from 'store/getHexDecimalColor';
import { getFontSize } from 'store/getFontSize';
import { theme } from 'theme';

import { CheckboxAtivoInativo as Checkbox } from './exampleCheckboxAtivoInativo';

const valueLabel = 'Label';
const valueColorLabel = '#6502b2';
const valueColorAtiva = '#38B7BF';
const fontSizeLabel = getFontSize('sm');

describe('Testing default CheckboxAtivoInativo', () => {
  beforeEach(() => {
    cy.mount(
      <ChakraProvider theme={theme}>
        <Checkbox
          label={valueLabel}
          colorLabel={valueColorLabel}
          id="checkboxAtivoInativo"
          name="checkboxAtivoInativo"
          fontLabel={fontSizeLabel}
        />
      </ChakraProvider>
    );
  });

  it('renders a label with correct color and text', () => {
    cy.testColorLabel(valueLabel, hexToRgbA(valueColorLabel));
  });
  it('renders a label with correct fontSize', () => {
    cy.testFontSizeLabel(valueLabel, fontSizeLabel);
  });

  it('Testing component functionality stay active and change color', () => {
    cy.get('#checkboxAtivoInativo')
      .click()
      .should('have.css', 'background-color')
      .and('eq', hexToRgbA(valueColorAtiva));
  });

  it('Testing if the component is inactive', () => {
    cy.get('#textCheckboxAtivoInativo').should(($div) => {
      const text = $div.text();

      expect(text).include('INATIVO');
    });
  });

  it('Testing if the component is active', () => {
    cy.get('#textCheckboxAtivoInativo')
      .click()
      .should(($div) => {
        const text = $div.text();

        expect(text).include('ATIVO');
      });
  });
});
