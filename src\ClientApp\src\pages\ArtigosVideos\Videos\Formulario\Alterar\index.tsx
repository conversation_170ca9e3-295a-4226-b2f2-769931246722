import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { PlanoEnum } from 'constants/Enum/enumPlano';
import { ConstanteRotas } from 'constants/constantRotas';
import { enumSistemas } from 'constants/Enum/enumSistemas';
import { enumTelasExibicao } from 'constants/Enum/enumTelasExibicao';
import { formatEnumTelasExibicao } from 'helpers/format/formatEnum';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import { LayoutFormPage } from 'components/Layout/FormPage';

import FormTreinamento from '..';
import {
  formDefaultValues,
  yupResolver,
  FormData,
  AulaResponse,
} from '../validationForm';
import { videoAdapter } from '../VideoAdapter';

type RegrasExibicao = {
  planos: string;
  sistemas: string;
  telas: string;
};

const AlterarTreinamento = () => {
  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, reset } = formMethods;

  const { id: idRota } = useParams();
  const navigate = useNavigate();

  const planos = PlanoEnum.options;
  const telas = formatEnumTelasExibicao(enumTelasExibicao);
  const sistemas = enumSistemas.options;

  const [isLoading, setIsLoading] = useState(false);

  const getVideo = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<
      void,
      ResponseApi<AulaResponse & RegrasExibicao>
    >(`${ConstantEnderecoWebservice.OBTER_VIDEO}/${idRota}`);

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso && response.dados) {
        const { dados } = response;
        const telasExibidas = telas.filter((telaItem) =>
          dados.telas?.includes(telaItem.value.toString())
        );

        const sistemasExibidos = sistemas.filter((sistemaItem) =>
          dados.sistemas?.includes(sistemaItem.label)
        );

        reset({
          ...dados,
          planos: planos.filter((planoItem) =>
            dados.planos?.includes(planoItem.label)
          ),
          tema: dados.temaId,
          sistemas: sistemasExibidos,
          telas: telasExibidas,
          categoriaTreinamento: dados.categoriaTreinamentoId,
        });
      }
    }
    setIsLoading(false);
  }, [idRota]);

  const getCadastroVideo = useCallback(async (data: FormData) => {
    const video = videoAdapter(data);

    const response = await api.put<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.ALTERAR_VIDEO,
      {
        ...video,
        id: idRota,
      }
    );

    return response;
  }, []);

  const handleAlterarVideo = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await getCadastroVideo(data);

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso) {
        toast.success('Cadastro alterado com sucesso');
        navigate(ConstanteRotas.VIDEOS);
      }
    }
    setIsLoading(false);
  });

  useEffect(() => {
    getVideo();
  }, [getVideo]);

  return (
    <LayoutFormPage onSubmit={() => handleAlterarVideo()} isLoading={isLoading}>
      <FormProvider {...formMethods}>
        <FormTreinamento />
      </FormProvider>
    </LayoutFormPage>
  );
};

export default AlterarTreinamento;
