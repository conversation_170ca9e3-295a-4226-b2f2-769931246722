import {
  Tooltip,
  Icon,
  IconProps,
  Text,
  PlacementWithLogical,
  ComponentWithAs,
} from '@chakra-ui/react';
import { IconType } from 'react-icons';

export interface IconTooltipProps extends IconProps {
  icon: IconType | ComponentWithAs<'svg', IconProps> | undefined;
  labelTooltip: string;
  hasArrow?: boolean;
  placement?: PlacementWithLogical;
}

export const IconTooltip = ({
  icon,
  labelTooltip,
  placement = 'bottom',
  hasArrow = true,
  boxSize = '15px',
  ...rest
}: IconTooltipProps) => {
  return (
    <Tooltip label={labelTooltip} placement={placement} hasArrow={hasArrow}>
      <Text>
        <Icon boxSize={boxSize} as={icon} {...rest} cursor="pointer" />
      </Text>
    </Tooltip>
  );
};
