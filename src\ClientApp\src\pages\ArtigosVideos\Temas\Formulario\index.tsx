import React from 'react';
import { GridItem } from '@chakra-ui/react';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { CheckboxAtivoInativo } from 'components/Checkbox/CheckboxAtivoInativo';

const FormTema = () => {
  return (
    <SimpleGridForm>
      <GridItem colSpan={{ base: 12, md: 12, lg: 7 }}>
        <InputDefault
          label="Título"
          name="titulo"
          placeholder="Digite o título do tema"
        />
      </GridItem>
      <GridItem colSpan={{ base: 12, md: 4, lg: 2 }}>
        <CheckboxAtivoInativo label="Ativo" name="ativo" />
      </GridItem>
    </SimpleGridForm>
  );
};

export default FormTema;
