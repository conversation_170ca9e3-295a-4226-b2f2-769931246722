import { Box, ChakraProvider } from '@chakra-ui/react';

import { hexToRgbA } from 'store/getHexDecimalColor';
import FullScreenProvider from 'store/FullScreen';
import { theme } from 'theme';

import { InputAutenticacao } from './exampleInput';

const valueLabel = 'Label';
const valueColorLabel = '#6502b2';
const isPassword = true;

describe('Testing input authentication', () => {
  beforeEach(() => {
    cy.mount(
      <ChakraProvider theme={theme}>
        <Box bg="gray.100" p="40px">
          <FullScreenProvider>
            <InputAutenticacao
              colorScheme={valueColorLabel}
              colorLabel={valueColorLabel}
              label={valueLabel}
              isPassword={isPassword}
              considerColorInFocus
            />
          </FullScreenProvider>
        </Box>
      </ChakraProvider>
    );
  });

  it('Testing value input', () => {
    cy.get('input[name=autenticacao]')
      .type('Testing input')
      .should((value) => {
        expect(value).to.equal(value);
      });
  });

  it('Cleaning input and validating if error message show', () => {
    cy.get('input[name=autenticacao]').type('Testing input');
    cy.wait(500);

    cy.get('input[name=autenticacao]')
      .clear()
      .trigger('mouseover')
      .should('be.visible', 'O campo é obrigatório');
  });

  it('Input label is showing', () => {
    cy.testLabelInput(valueLabel);
  });

  it('Validating the color of the label', () => {
    cy.testColorLabel(valueLabel, hexToRgbA(valueColorLabel));
  });

  it('There is a see password button', () => {
    cy.get('#isPasswordVisibility').should('be.visible');
  });

  it('Input is password', () => {
    cy.get('input[name=autenticacao]').type('Testing input');

    cy.get('[type=password]').type('Testing input');
  });
});
