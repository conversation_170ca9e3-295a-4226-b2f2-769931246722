import {
  Button,
  Icon,
  IconButton,
  InputProps,
  MenuButton,
  MenuList,
  useToken,
} from '@chakra-ui/react';
import { Calendar } from 'react-date-range';
import { ptBR } from 'date-fns/locale';

import MenuListas from 'components/MenuList';
import { CalendarioIcon } from 'icons';

export interface RangeWithKey extends Range {
  key: 'selection';
}

export type OnChangeProps = Range | { selection: RangeWithKey } | Date;

interface MenuCalendarProps extends Omit<InputProps, 'value' | 'onChange'> {
  isDisabled: boolean | undefined;
  asButton: boolean | undefined;
  value: Date | null;
  minDate?: Date;
  maxDate?: Date;
  handleOnChange: (year: number, month: number, day: number) => void;
  handleGetFormattedDate: (date: Date, type?: string) => string;
}

export const MenuCalendar = ({
  isDisabled,
  asButton,
  minDate,
  maxDate,
  size,
  handleOnChange,
  h,
  handleGetFormattedDate,
  value,
  name,
}: MenuCalendarProps) => {
  const [primary500, gray50, gray100, gray700] = useToken('colors', [
    'primary.500',
    'gray.50',
    'gray.100',
    'gray.700',
  ]);

  const handleGetDateValuesCalendar = (newDate: Date) => {
    const dateObj = new Date(newDate);

    const year = dateObj.getUTCFullYear();
    const month = dateObj.getUTCMonth();
    const day = dateObj.getUTCDate();

    return { year, month, day };
  };
  return (
    <MenuListas
      isDisabled={isDisabled}
      MenuButton={() =>
        asButton ? (
          <MenuButton
            tabIndex={-1}
            as={Button}
            aria-label="Calendário"
            size={size}
            fontSize="sm"
            className={name}
            variant=""
            isDisabled={isDisabled}
            h={h}
            _disabled={{ opacity: '1 !important' }}
            _hover={{
              bg: isDisabled ? 'gray.50' : 'gray.100',
            }}
            borderRadius="md"
          >
            {value ? handleGetFormattedDate(value) : '00/00/0000'}
          </MenuButton>
        ) : (
          <MenuButton
            _focus={{
              outiline: 'none',
            }}
            tabIndex={-1}
            as={IconButton}
            aria-label="Calendário"
            size={size}
            className={name}
            variant=""
            borderRadius="md"
            icon={<Icon as={CalendarioIcon} color="gray.700" />}
            isDisabled={isDisabled}
            h={h}
            w={h}
            _disabled={{ '*': { opacity: 1 } }}
          />
        )
      }
      MenuList={() => (
        <MenuList
          zIndex="popover"
          py={0}
          sx={{
            '& .rdrCalendarWrapper': {
              display: 'flex',
            },
            '& .rdrMonthAndYearWrapper': {
              pt: 0,
              borderTopRadius: 'md',
            },
            '& .rdrMonths': {
              borderBottomRadius: 'md',
            },
            '& .rdrMonthName': {
              color: primary500,
            },
            '& .rdrDateDisplayWrapper, & .rdrStaticRange, & .rdrDefinedRangesWrapper':
              {
                bg: gray50,
                borderColor: gray100,
              },
            '& .rdrDayToday > .rdrDayNumber > span::after': {
              bg: primary500,
            },
            '& .rdrNextPrevButton.rdrPprevButton': {
              bg: gray100,
              '& > i': {
                borderColor: `transparent ${gray700} transparent transparent`,
              },
            },
            '& .rdrNextPrevButton.rdrNextButton': {
              bg: gray100,
              '& > i': {
                borderColor: `transparent transparent transparent ${gray700}`,
              },
            },
          }}
        >
          <Calendar
            onChange={(range: OnChangeProps) => {
              const dateObj = handleGetDateValuesCalendar(range as Date);

              handleOnChange(dateObj.year, dateObj.month, dateObj.day);
            }}
            className={`calendar-${name}`}
            showMonthAndYearPickers={false}
            weekdayDisplayFormat="EEEEEE"
            locale={ptBR}
            // @ts-ignore
            date={value ? new Date(value) : null}
            color={primary500}
            maxDate={maxDate}
            minDate={minDate}
          />
        </MenuList>
      )}
    />
  );
};
