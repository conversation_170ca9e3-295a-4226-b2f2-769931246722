import { ChakraProvider } from '@chakra-ui/react';

import { EnumPageSizeOptions } from 'constants/Enum/enumPageSizeOptionsEnum';
import { theme } from 'theme';

import { Pagination } from './Pagination.stories';

describe('Testing pagination component', () => {
  beforeEach(() => {
    cy.mount(
      <ChakraProvider theme={theme}>
        <Pagination />
      </ChakraProvider>
    );
  });

  it('validating click on next pages', () => {
    cy.get('#nextPage').should('be.visible').click();
  });

  it('validating click on previous pages', () => {
    cy.get('#nextPage').should('be.visible').click();
    cy.get('#previousPage').should('be.visible').click();
  });

  it('validating click to last page', () => {
    cy.get('#lastPage').should('be.visible').click();
  });

  it('validating click to first page', () => {
    cy.get('#lastPage').should('be.visible').click();
    cy.get('#firstPage').should('be.visible').click();
  });

  EnumPageSizeOptions.properties.forEach((item) => {
    it('Test by selecting all select options', () => {
      cy.get('#resultPage').click();
      cy.get(`.react-select__option${item.label}`)
        .scrollIntoView()
        .click()
        .should(() => {
          expect(item.label).to.equal(item.label);
        });
    });
  });
});
