import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { Revendas } from 'pages/Revendas/Listar';
import { CadastrarRevenda } from 'pages/Revendas/Formulario/Cadastrar';
import { AlterarRevenda } from 'pages/Revendas/Formulario/Alterar';

import LayoutGuard from './LayoutGuard';

export const RevendasRoutes = [
  <Route
    key={ConstanteRotas.REVENDA}
    path={ConstanteRotas.REVENDA}
    element={
      <LayoutGuard
        key={ConstanteRotas.REVENDA}
        breadcrumb={[
          { title: 'Revendas' },
        ]}
        component={<Revendas />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_REVENDA)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.REVENDA_CADASTRAR}
    path={ConstanteRotas.REVENDA_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.REVENDA_CADASTRAR}
        breadcrumb={[
          { title: 'Revendas', path: ConstanteRotas.REVENDA },
          { title: 'Cadastrar' },
        ]}
        component={<CadastrarRevenda />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroRevendaAcao.CADASTRAR_REVENDAS)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.REVENDA_ALTERAR}
    path={ConstanteRotas.REVENDA_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.REVENDA_ALTERAR}
        breadcrumb={[
          { title: 'Revendas', path: ConstanteRotas.REVENDA },
          { title: 'Alterar' },
        ]}
        component={<AlterarRevenda />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroRevendaAcao.ALTERAR_REVENDAS)}
      />
    }
  />,
];
