import planosEnum, { PlanoProps } from 'constants/Enum/enumPlanos';

const getPlanos = (plano: string | undefined) => {
  const planos = planosEnum.properties;
  const currentPlanos: PlanoProps[] = [];

  if (plano?.includes('SMARTPOS'))
    currentPlanos.push(planos[planosEnum.SMARTPOS]);
  if (plano?.includes('START')) currentPlanos.push(planos[planosEnum.START]);
  if (plano?.includes('PRO')) currentPlanos.push(planos[planosEnum.PRO]);
  if (plano?.includes('PRIME')) currentPlanos.push(planos[planosEnum.PRIME]);

  return currentPlanos;
};

export default getPlanos;
