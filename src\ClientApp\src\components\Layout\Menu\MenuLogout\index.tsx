import {
  Button,
  <PERSON>con,
  <PERSON>u,
  <PERSON>u<PERSON>utton,
  <PERSON>u<PERSON><PERSON>,
  MenuList,
} from '@chakra-ui/react';
import { ConstanteRotas, SubstituirParametroRota } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';
import { AiOutlineLogout, AiOutlineUser, AiOutlineEdit } from 'react-icons/ai';
import { useNavigate } from 'react-router-dom';

export const MenuLogout = () => {
  const userName = `Olá ${auth.getUserName()}!`;  
  const navigate = useNavigate();

  async function logOff() {
    auth.clearTokenAndRedirect();
    navigate(ConstanteRotas.LOGIN);
  }

  function goToUserData() {
    const userId = auth.getUserId();
    navigate(
      SubstituirParametroRota(
        ConstanteRotas.USUARIOS_ALTERAR,
        'id',
        userId
      )
    );
  }

  function goToRevendaData() {
    const revendaId = auth.getRevendaId();
    navigate(
      SubstituirParametroRota(
        ConstanteRotas.REVENDA_ALTERAR,
        'id',
        revendaId
      )
    );
  }

  return (
    <Menu>
      <MenuButton
        variant=""
        as={Button}
        paddingRight="0"
        rightIcon={<Icon fontSize="18px" as={AiOutlineUser} />}
      >
       {auth.getRevendaName()} | {userName}
      </MenuButton>
      <MenuList borderColor="primary.900" bg="primary.900">
        <MenuItem
          _active={{
            background: 'none',
          }}
          _focus={{
            background: 'none',
          }}
          _hover={{
            backgroundColor: 'gray.400',
          }}
          bg="primary.900"
          color="white"
          onClick={() => goToUserData()}
        >
          <Icon
            mr="10px"
            fontSize="18px"
            as={AiOutlineUser}
          />
          Dados Usuário
        </MenuItem>
        {auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.CadastroRevendaAcao.ALTERAR_REVENDAS
        ) && (
          <MenuItem
            _active={{
              background: 'none',
            }}
            _focus={{
              background: 'none',
            }}
            _hover={{
              backgroundColor: 'gray.400',
            }}
            bg="primary.900"
            color="white"
            onClick={() => goToRevendaData()}
          >
            <Icon
              mr="10px"
              fontSize="18px"
              as={AiOutlineEdit}
            />
            Dados Revenda
          </MenuItem>
        )}
        <MenuItem
          _active={{
            background: 'none',
          }}
          _focus={{
            background: 'none',
          }}
          _hover={{
            backgroundColor: 'gray.400',
          }}
          bg="primary.900"
          color="white"
          onClick={() => logOff()
          }
        >
          <Icon
            mr="10px"
            fontSize="18px"
            transform={'rotate(-90deg)'}
            as={AiOutlineLogout}
          />
          Sair
        </MenuItem>
      </MenuList>
    </Menu>
  );
};
