import { FormProvider, useForm } from 'react-hook-form';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import 'react-date-range/dist/styles.css'; // main css file
import 'react-date-range/dist/theme/default.css';

import { InputDateRange as InputDate } from './index';

export const InputDateRange = ({ isClearable = true, ...rest }) => {
  return (
    <FormProvider {...useForm()}>
      <InputDate
        {...rest}
        name="dataInicio"
        startDateName="dataInicio"
        endDateName="dataFim"
        w="full"
        borderRadius="5px"
        borderColor="gray.100"
        onConfirm={() =>
          toast.success('Data foi confirmada', {
            toastId: 'dateConfirm',
          })
        }
        isClearable={isClearable}
        inputValueIsSmaller={false}
      />

      <ToastContainer />
    </FormProvider>
  );
};
