import React, { ChangeEvent, useRef, useState } from 'react';
import { Controller } from 'react-hook-form';
import { toast } from 'react-toastify';
import { FlexProps, AspectRatio } from '@chakra-ui/react';

import { CampoPrototipo } from 'components/Layout/CampoPrototipo';

import { SemImagem } from './components/SemImagem';
import { ImagemUpada } from './components/ImagemUpada';

export interface ImageUploaderProps extends FlexProps {
  name: string;
  id?: string;
  label?: string;
  sizeIcon?: string;
  maxBytes?: number;
  maxResolucao?: number;
  alturaMinRecomendada?: number;
  naoValidarTamanhos?: boolean;
  isAlterarImagem?: boolean;
  isRequired?: boolean;
  ratio?: number;
}

export const ImageUploaderForm = ({
  name,
  id,
  isAlterarImagem = false,
  label,
  sizeIcon = '40px',
  maxBytes = 500,
  maxResolucao = 1800,
  alturaMinRecomendada = 225,
  naoValidarTamanhos,
  isRequired,
  ratio = 16 / 9,
  maxH,
  width,
  ...rest
}: ImageUploaderProps) => {
  const handleFile = (file: File, onChange: (value: string | null) => void) => {
    const valorCalculaBytesPadrao = 1024;

    if (file) {
      if (file.type.startsWith('image/')) {
        const image = new Image();

        image.onload = () => {
          if (
            (image.width <= maxResolucao &&
              image.height <= alturaMinRecomendada) ||
            naoValidarTamanhos
          ) {
            if (file.size <= maxBytes * valorCalculaBytesPadrao) {
              const reader = new FileReader();

              reader.onload = () => {
                onChange(reader.result as string);
              };

              reader.readAsDataURL(file);
            } else {
              toast.warning(
                `Este campo aceita somente imagens de até ${maxBytes} bytes`
              );
              onChange(null);
            }
          } else {
            toast.warning(
              `Este campo aceita somente imagens de até ${maxResolucao} px`
            );
            onChange(null);
          }
        };

        image.src = URL.createObjectURL(file);
      } else {
        toast.warning(
          'A extensão do arquivo selecionado é inválida, este campo aceita somente imagens'
        );
      }
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  return (
    <Controller
      name={name}
      render={({ field, fieldState: { error } }) => {
        const inputFileRef = useRef<HTMLInputElement>(null);
        const [onMouseOver, setOnMouseOver] = useState(false);

        return (
          <CampoPrototipo
            id={id}
            label={label}
            error={error}
            isRequired={isRequired}
          >
            <AspectRatio ratio={ratio} {...rest}>
              <>
                {field.value && isAlterarImagem ? (
                  <ImagemUpada
                    field={field}
                    handleFile={handleFile}
                    inputFileRef={inputFileRef}
                    handleDragOver={handleDragOver}
                    setOnMouseOver={setOnMouseOver}
                    onMouseOver={onMouseOver}
                    maxH={maxH}
                    width={width}
                  />
                ) : (
                  <SemImagem
                    field={field}
                    handleFile={handleFile}
                    inputFileRef={inputFileRef}
                    sizeIcon={sizeIcon}
                    handleDragOver={handleDragOver}
                  />
                )}

                <input
                  tabIndex={-1}
                  ref={inputFileRef}
                  type="file"
                  accept=".jpg, .jpge, .png"
                  style={{
                    display: 'none',
                  }}
                  onChange={(event: ChangeEvent<HTMLInputElement>) => {
                    const { files } = event.target;

                    if (!files || files.length === 0) return;

                    const file = files[0];

                    if (file) {
                      handleFile(file, field.onChange);
                    }
                  }}
                />
              </>
            </AspectRatio>
          </CampoPrototipo>
        );
      }}
    />
  );
};
