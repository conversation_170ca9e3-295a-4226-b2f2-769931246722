import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { Servicos } from 'pages/Servicos/Listar';
import { CadastrarServicos } from 'pages/Servicos/Formulario/Cadastrar';
import { AlterarServicos } from 'pages/Servicos/Formulario/Alterar';

import LayoutGuard from './LayoutGuard';

export const ServicosRoutes = [
  <Route
    key={ConstanteRotas.SERVICOS}
    path={ConstanteRotas.SERVICOS}
    element={
      <LayoutGuard
        key={ConstanteRotas.SERVICOS}
        breadcrumb={[
          { title: 'Cadastro' },
          {
            title: 'Serviços',
            path: ConstanteRotas.SERVICOS,
          },
        ]}
        component={<Servicos />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_CADASTRO_SERVICO)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.SERVICOS_CADASTRAR}
    path={ConstanteRotas.SERVICOS_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.SERVICOS_CADASTRAR}
        breadcrumb={[
          { title: 'Cadastro' },
          {
            title: 'Serviços',
            path: ConstanteRotas.SERVICOS,
          },
          {
            title: 'Cadastrar',
          },
        ]}
        component={<CadastrarServicos />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroServicoAcao.CADASTRAR_SERVICOS)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.SERVICOS_ALTERAR}
    path={ConstanteRotas.SERVICOS_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.SERVICOS_ALTERAR}
        breadcrumb={[
          { title: 'Cadastro' },
          {
            title: 'Serviços',
            path: ConstanteRotas.SERVICOS,
          },
          {
            title: 'Alterar',
          },
        ]}
        component={<AlterarServicos />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroServicoAcao.ALTERAR_SERVICOS)}
      />
    }
  />,
];
