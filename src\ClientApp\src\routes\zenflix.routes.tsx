import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import auth from 'modules/auth';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';

import ListarArtigos from 'pages/ArtigosVideos/Artigos/Listar';
import CadastrarArtigo from 'pages/ArtigosVideos/Artigos/Formulario/Cadastrar';
import AlterarArtigo from 'pages/ArtigosVideos/Artigos/Formulario/Alterar';
import ListarTreinamentos from 'pages/ArtigosVideos/Videos/Listar';
import AlterarTreinamento from 'pages/ArtigosVideos/Videos/Formulario/Alterar';
import CadastrarTreinamento from 'pages/ArtigosVideos/Videos/Formulario/Cadastrar';
import ListarTemas from 'pages/ArtigosVideos/Temas/Listar';
import AlterarTema from 'pages/ArtigosVideos/Temas/Formulario/Alterar';
import CadastrarTema from 'pages/ArtigosVideos/Temas/Formulario/Cadastrar';
import ListarCategoriasTreinamento from 'pages/ArtigosVideos/Categorias/Listar';
import AlterarCategoriaTreinamento from 'pages/ArtigosVideos/Categorias/Formulario/Alterar';
import CadastraCategoriaTreinamento from 'pages/ArtigosVideos/Categorias/Formulario/Cadastrar';
import ListarBanners from 'pages/ArtigosVideos/Banner/Listar';
import AlterarBanner from 'pages/ArtigosVideos/Banner/Formulario/Alterar';
import CadastrarBanner from 'pages/ArtigosVideos/Banner/Formulario/Cadastrar';

import LayoutGuard from './LayoutGuard';

export const ZenflixRoutes = [
  <Route
    key={ConstanteRotas.VIDEOS}
    path={ConstanteRotas.VIDEOS}
    element={
      <LayoutGuard
        key={ConstanteRotas.VIDEOS}
        breadcrumb={[{ title: 'Vídeos' }]}
        component={<ListarTreinamentos />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.VISUALIZAR_VIDEOS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.VIDEOS_CADASTRAR}
    path={ConstanteRotas.VIDEOS_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.VIDEOS_CADASTRAR}
        breadcrumb={[
          { title: 'Vídeos', path: ConstanteRotas.VIDEOS },
          { title: 'Cadastrar' },
        ]}
        component={<CadastrarTreinamento />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.CADASTRAR_VIDEOS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.VIDEOS_ALTERAR}
    path={ConstanteRotas.VIDEOS_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.VIDEOS_ALTERAR}
        breadcrumb={[
          { title: 'Vídeos', path: ConstanteRotas.VIDEOS },
          { title: 'Alterar' },
        ]}
        component={<AlterarTreinamento />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.ALTERAR_VIDEOS
        )}
      />
    }
  />,

  <Route
    key={ConstanteRotas.ARTIGOS}
    path={ConstanteRotas.ARTIGOS}
    element={
      <LayoutGuard
        key={ConstanteRotas.ARTIGOS}
        breadcrumb={[{ title: 'Artigos' }]}
        component={<ListarArtigos />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.VISUALIZAR_ARTIGOS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.ARTIGOS_CADASTRAR}
    path={ConstanteRotas.ARTIGOS_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.ARTIGOS_CADASTRAR}
        breadcrumb={[
          { title: 'Artigos', path: ConstanteRotas.ARTIGOS },
          { title: 'Cadastrar' },
        ]}
        component={<CadastrarArtigo />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.CADASTRAR_ARTIGOS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.ARTIGOS_ALTERAR}
    path={ConstanteRotas.ARTIGOS_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.ARTIGOS_ALTERAR}
        breadcrumb={[
          { title: 'Artigos', path: ConstanteRotas.ARTIGOS },
          { title: 'Alterar' },
        ]}
        component={<AlterarArtigo />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.ALTERAR_ARTIGOS
        )}
      />
    }
  />,

  <Route
    key={ConstanteRotas.TEMAS}
    path={ConstanteRotas.TEMAS}
    element={
      <LayoutGuard
        key={ConstanteRotas.TEMAS}
        breadcrumb={[{ title: 'Temas' }]}
        component={<ListarTemas />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.VISUALIZAR_TEMAS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.TEMA_CADASTRAR}
    path={ConstanteRotas.TEMA_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.TEMA_CADASTRAR}
        breadcrumb={[
          { title: 'Temas', path: ConstanteRotas.TEMAS },
          { title: 'Cadastrar' },
        ]}
        component={<CadastrarTema />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.CADASTRAR_TEMAS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.TEMA_ALTERAR}
    path={ConstanteRotas.TEMA_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.TEMA_ALTERAR}
        breadcrumb={[
          { title: 'Temas', path: ConstanteRotas.TEMAS },
          { title: 'Alterar' },
        ]}
        component={<AlterarTema />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.ALTERAR_TEMAS
        )}
      />
    }
  />,

  <Route
    key={ConstanteRotas.CATEGORIAS_TREINAMENTO}
    path={ConstanteRotas.CATEGORIAS_TREINAMENTO}
    element={
      <LayoutGuard
        key={ConstanteRotas.CATEGORIAS_TREINAMENTO}
        breadcrumb={[{ title: 'Categorias para treinamento' }]}
        component={<ListarCategoriasTreinamento />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.VISUALIZAR_CATEGORIAS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.CATEGORIA_TREINAMENTO_CADASTRAR}
    path={ConstanteRotas.CATEGORIA_TREINAMENTO_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.CATEGORIA_TREINAMENTO_CADASTRAR}
        breadcrumb={[
          {
            title: 'Categorias para treinamento',
            path: ConstanteRotas.CATEGORIAS_TREINAMENTO,
          },
          { title: 'Cadastrar' },
        ]}
        component={<CadastraCategoriaTreinamento />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.CADASTRAR_CATEGORIAS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.CATEGORIA_TREINAMENTO_ALTERAR}
    path={ConstanteRotas.CATEGORIA_TREINAMENTO_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.CATEGORIA_TREINAMENTO_ALTERAR}
        breadcrumb={[
          {
            title: 'Categorias para treinamento',
            path: ConstanteRotas.CATEGORIAS_TREINAMENTO,
          },
          { title: 'Alterar' },
        ]}
        component={<AlterarCategoriaTreinamento />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.ALTERAR_CATEGORIAS
        )}
      />
    }
  />,

  <Route
    key={ConstanteRotas.BANNERS}
    path={ConstanteRotas.BANNERS}
    element={
      <LayoutGuard
        key={ConstanteRotas.BANNERS}
        breadcrumb={[{ title: 'Banners' }]}
        component={<ListarBanners />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.VISUALIZAR_BANNERS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.BANNER_CADASTRAR}
    path={ConstanteRotas.BANNER_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.BANNER_CADASTRAR}
        breadcrumb={[
          {
            title: 'Banner',
            path: ConstanteRotas.BANNERS,
          },
          { title: 'Cadastrar' },
        ]}
        component={<CadastrarBanner />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.CADASTRAR_BANNERS
        )}
      />
    }
  />,
  <Route
    key={ConstanteRotas.BANNER_ALTERAR}
    path={ConstanteRotas.BANNER_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.BANNER_ALTERAR}
        breadcrumb={[
          {
            title: 'Banner',
            path: ConstanteRotas.BANNERS,
          },
          { title: 'Alterar' },
        ]}
        component={<AlterarBanner />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.Zenflix.ALTERAR_BANNERS
        )}
      />
    }
  />,
];
