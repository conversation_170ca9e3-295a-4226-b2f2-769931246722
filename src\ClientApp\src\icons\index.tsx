import { createIcon } from '@chakra-ui/react';
import { CSSProperties } from 'react';

interface IconInterface extends React.SVGAttributes<SVGElement> {
  size?: number;
}

export type IconType = (props: IconInterface) => JSX.Element;

const Stroke: CSSProperties = {
  fill: 'none',
  stroke: 'currentColor',
};

const StrokeRounded: CSSProperties = {
  ...Stroke,
  strokeLinecap: 'round',
  strokeLinejoin: 'round',
};

const Stroke075Rounded: CSSProperties = {
  ...StrokeRounded,
  strokeWidth: '0.75px',
};

const Stroke150Rounded: CSSProperties = {
  ...StrokeRounded,
  strokeWidth: '1.5px',
};

export const BuscaIcon = createIcon({
  displayName: 'buscaIcon',
  viewBox: '0 0 23 23',
  path: (
    <g style={Stroke075Rounded}>
      <path d="M 18.08,11.50 C 18.08,12.98 16.88,14.18 15.40,14.18 13.92,14.18 12.72,12.98 12.72,11.50 12.72,10.02 13.92,8.82 15.40,8.82 16.88,8.82 18.08,10.02 18.08,11.50 18.08,11.50 18.08,11.50 18.08,11.50 Z M 22.46,11.50 C 22.46,11.50 17.96,11.50 17.96,11.50M 12.72,11.50 C 12.72,11.50 0.54,11.50 0.54,11.50M 10.40,19.78 C 10.40,19.78 10.40,19.78 10.40,19.78 10.40,21.26 9.20,22.46 7.72,22.46 7.72,22.46 7.72,22.46 7.72,22.46 6.24,22.46 5.04,21.26 5.04,19.78 5.04,19.78 5.04,19.78 5.04,19.78 5.04,18.30 6.24,17.10 7.72,17.10 7.72,17.10 7.72,17.10 7.72,17.10 9.20,17.10 10.40,18.30 10.40,19.78 10.40,19.78 10.40,19.78 10.40,19.78 10.40,19.78 10.40,19.78 10.40,19.78 Z M 0.54,19.78 C 0.54,19.78 5.04,19.78 5.04,19.78M 10.28,19.78 C 10.28,19.78 22.46,19.78 22.46,19.78M 10.40,3.22 C 10.40,3.22 10.40,3.22 10.40,3.22 10.40,4.70 9.20,5.90 7.72,5.90 7.72,5.90 7.72,5.90 7.72,5.90 6.24,5.90 5.04,4.70 5.04,3.22 5.04,3.22 5.04,3.22 5.04,3.22 5.04,1.74 6.24,0.54 7.72,0.54 7.72,0.54 7.72,0.54 7.72,0.54 9.20,0.54 10.40,1.74 10.40,3.22 10.40,3.22 10.40,3.22 10.40,3.22 10.40,3.22 10.40,3.22 10.40,3.22 Z M 0.54,3.22 C 0.54,3.22 5.04,3.22 5.04,3.22M 10.28,3.22 C 10.28,3.22 22.46,3.22 22.46,3.22" />
    </g>
  ),
});

export const SemPermissaoPlanoIcon = createIcon({
  displayName: 'SemPermissaoPlanoIcon',
  viewBox: '0 0 23 23',
  path: (
    <g style={Stroke075Rounded}>
      <path d="M21.4,8.8l-6-6.4a1.7,1.7,0,0,0-2.3-.1,1.6,1.6,0,0,0-.5,1.2V6.7c-8,1.6-10.7,10-11.3,12.6s-.1,2.3.5,1.3c3.3-5.3,7.3-7.3,10.8-7.5v3.3a1.7,1.7,0,0,0,1.6,1.7,1.8,1.8,0,0,0,1.2-.5l6-6.5A1.7,1.7,0,0,0,21.4,8.8Z" />
    </g>
  ),
});

export const AtencaoAvisoIcon = createIcon({
  displayName: 'AtencaoAvisoIcon',
  viewBox: '0 0 62.4 62.3',
  path: (
    <g style={Stroke150Rounded}>
      <g>
        <path d="M31.2,38.5V15" />
        <path d="M31.2,47.3V45.2" />
      </g>
      <path d="M31.2,1.2a30,30,0,1,1-30,30A30,30,0,0,1,31.2,1.2Z" />
    </g>
  ),
});

export const SemPermissaoIcon = createIcon({
  displayName: 'SemPermissaoIcon',
  viewBox: '0 0 23 23',
  path: (
    <g style={Stroke075Rounded}>
      <path d="M5.1,9.5H17.9A1.1,1.1,0,0,1,19,10.6V20.9A1.1,1.1,0,0,1,17.9,22H5.1A1.1,1.1,0,0,1,4,20.9H4V10.6A1.1,1.1,0,0,1,5.1,9.5Z" />
      <path d="M5.5,9.5V6.4a5.8,5.8,0,0,1,6-5.4,5.8,5.8,0,0,1,6,5.4V9.5" />
    </g>
  ),
});

export const FiscalIcon = createIcon({
  displayName: 'fiscal icon',
  viewBox: '0 0 23 23',
  path: (
    <g style={Stroke075Rounded}>
      <path d="M4.1,18.3A11.1,11.1,0,0,1,.4,10.1,10.8,10.8,0,0,1,4.3,1.7" />
      <path d="M18.8,1.7a11.3,11.3,0,0,1,3.8,8.4,11.3,11.3,0,0,1-3.7,8.2" />
      <path d="M7.3,15.2a6.6,6.6,0,0,1-2.4-5.1A6.4,6.4,0,0,1,7.4,4.9" />
      <path d="M15.6,4.8a6.9,6.9,0,0,1,2.5,5.3,6.4,6.4,0,0,1-2.4,5.1" />
      <path d="M13.4,10.1a1.8,1.8,0,0,1-.7,1.5,2,2,0,0,1-2.4,0,1.8,1.8,0,0,1-.7-1.5,1.9,1.9,0,0,1,.7-1.6,2,2,0,0,1,2.4,0A1.9,1.9,0,0,1,13.4,10.1Z" />
      <line x1="11.5" y1="12" x2="11.5" y2="21.3" />
      <line x1="9.2" y1="21.3" x2="13.8" y2="21.3" />
    </g>
  ),
});

export const ClientesIcon = createIcon({
  displayName: 'clientes icon',
  viewBox: '0 0 23 23',
  path: (
    <g style={Stroke075Rounded}>
      <line x1="6.8" y1="22.3" x2="6.8" y2="12.6" />
      <line x1="9.3" y1="22.3" x2="9.3" y2="17.7" />
      <line x1="11.9" y1="22.3" x2="11.9" y2="12.6" />
      <path d="M6.8,17.2s-1.7-.3-1.7-1.7V12.6A2.9,2.9,0,0,1,8,9.7l1.3,1.6,1.4-1.6s2.7.2,2.7,2.6v3.3s.3,1.2-1.5,1.8" />
      <ellipse cx="9.4" cy="6.6" rx="2.1" ry="2.5" />
      <ellipse cx="13.9" cy="3.1" rx="2.1" ry="2.5" />
      <path d="M13.8,8l1.5-1.8A2.7,2.7,0,0,1,18,9v3.2s.2,1.2-1.5,1.5" />
      <line x1="16.4" y1="9.1" x2="16.4" y2="18.8" />
    </g>
  ),
});

export const UsuarioIcon = createIcon({
  displayName: 'clientes icon',
  viewBox: '0 0 19.1 19.1',
  path: (
    <g style={Stroke075Rounded}>
      <g>
        <g transform="translate(-310 -31.699)">
          <path d="M328.5,41.2c0,5-4,9-9,9s-9-4.1-9-9s4-9,9-9S328.5,36.2,328.5,41.2z" />
          <path
            d="M319.5,35.8c-2.7,0-3.8,1.8-3.8,4.4c0,2.6,1,3.3,1.5,3.8s-0.3,0.8-0.6,1.1
			c-0.9,0.5-1.9,1-2.9,1.4c-0.3,0.1-0.6,0.3-0.9,0.5"
          />
          <path
            d="M319.5,35.8c2.7,0,3.8,1.8,3.8,4.4c0,2.6-1,3.3-1.5,3.8s0.3,0.8,0.6,1.1
			c0.9,0.5,1.9,1,2.9,1.4c0.3,0.1,0.6,0.3,0.9,0.5"
          />
        </g>
      </g>
    </g>
  ),
});

export const CalendarioIcon = createIcon({
  displayName: 'Calendario icon',
  viewBox: '0 0 25.3 25.2',
  path: (
    <g style={Stroke075Rounded}>
      <g>
        <line x1="6.1" y1="0.6" x2="6.1" y2="4.7" />
        <line x1="19.2" y1="0.6" x2="19.2" y2="4.7" />
      </g>
      <line x1="0.8" y1="9.5" x2="24.6" y2="9.5" />
      <rect x="0.8" y="3.2" width="23.8" height="21.16" rx="1.8" />
    </g>
  ),
});

export const CadastroRevendas = createIcon({
  displayName: 'Cadastro de Revendas',
  viewBox: '0 0 23 23',
  path: (
    <g style={Stroke075Rounded}>
      <path d="M19.5,9.9V5.7H14.2V.5H3.2a.8.8,0,0,0-.8.7V21.8a.8.8,0,0,0,.8.7h9.2" />
      <line x1="14.2" y1="0.5" x2="19.5" y2="5.7" />
      <line x1="5.9" y1="5.7" x2="10.1" y2="5.7" />
      <line x1="5.9" y1="8.7" x2="15.8" y2="8.7" />
      <line x1="5.9" y1="11.8" x2="15.8" y2="11.8" />
      <line x1="5.9" y1="14.9" x2="12.6" y2="14.9" />
      <line x1="5.9" y1="17.9" x2="11.5" y2="17.9" />
      <g>
        <path d="M14.9,19.5l4.8-6.8c.2-.3.6-.3,1-.1l1.3.9a.8.8,0,0,1,.2,1l-4.7,6.7" />
        <polygon points="14.6 22.5 14.9 19.5 17.5 21.2 14.6 22.5" />
        <line x1="18.6" y1="14.4" x2="21.1" y2="16" />
      </g>
    </g>
  ),
});

export const LixeiraIcon = createIcon({
  displayName: 'lixeiraIcon',
  viewBox: '0 0 23 23',
  path: (
    <g style={Stroke075Rounded}>
      <polygon points="19.1 5.2 4 5.2 5.5 22 17.5 22 19.1 5.2" />
      <path d="M11.7,18.2V9.5a1,1,0,0,0-1-1" />
      <path d="M8.7,18.2,8.1,9.5A1,1,0,0,0,7,8.6" />
      <path d="M14.5,18.2l.6-8.7a.9.9,0,0,1,1-.9" />
      <rect x="2.6" y="3.1" width="17.8" height="2.13" rx="1" />
      <path d="M13.3,3.1v-1A1.1,1.1,0,0,0,12.2,1H10.8A1.1,1.1,0,0,0,9.7,2.1v1" />
    </g>
  ),
});

export const FaturamentoAssinaturaIcon = createIcon({
  displayName: 'FaturamentoAssinaturaIcon',
  viewBox: '0 0 23 23',
  path: (
    <g style={Stroke150Rounded}>
      <path d="M9.4,6.4s-4.6,5.5-5.7,7.4.8,8.4,7.8,8.4" />
      <path d="M13.4,6.4S18,11.9,19,13.8s-.5,8.4-7.5,8.4" />
      <line x1="15.2" y1="6.4" x2="7.8" y2="6.4" />
      <path d="M9.4,6.4S4.9,2.5,5.9,1.2,9.6,2.5,9.6,2.5s1.8-3.5,3.7.1C14.4,1.8,16.4.5,17,1.2s-.4,2.3-3.6,5.2" />
      <g>
        <path d="M13.5,12.3a1.7,1.7,0,0,0-1.9-1.7c-2,0-2.1,1.1-2.1,1.6s.3,1.4,1.8,1.8,2.6.5,2.6,2.3-1,1.6-2.4,1.6a2.2,2.2,0,0,1-2.4-2.3" />
        <line x1="11.5" y1="9.7" x2="11.5" y2="19" />
      </g>
    </g>
  ),
});

export const EditarSituacaoIcon = createIcon({
  displayName: 'EditarSituacaoIcon',
  viewBox: '0 0 16.6 20.5',
  path: (
    <g
      style={{
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        strokeWidth: '1px',
        fill: 'none',
        stroke: 'currentColor',
      }}
    >
      <g>
        <rect
          className="cls-1"
          fill="none"
          stroke="#000"
          strokeLinecap="round"
          strokeLinejoin="round"
          x="4.6"
          y="3.4"
          width="6.8"
          height="12.9"
          transform="translate(7.4 -2.8) rotate(36.3)"
        />
        <path
          className="cls-1"
          fill="none"
          stroke="#000"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M9,2.7l.8-1c.7-1,2.2-1.2,3.2-.5l1.8,1.4c1,.7,1.2,2.2.5,3.2l-.8,1"
        />
        <polygon
          className="cls-1"
          fill="none"
          stroke="#000"
          strokeLinecap="round"
          strokeLinejoin="round"
          points="1.4 13.1 .8 19.7 6.9 17.1 1.4 13.1"
        />
      </g>
      <line
        id="Linha_866"
        className="cls-1"
        fill="none"
        stroke="#000"
        strokeLinecap="round"
        strokeLinejoin="round"
        x1="8.7"
        y1="19.7"
        x2="15.1"
        y2="19.7"
      />
    </g>
  ),
});

export const NaoSelecionadoIcon = createIcon({
  displayName: 'NaoSelecionadoIcon',
  viewBox: '0 0 23.2 23.4',
  path: (
    <g style={{ ...Stroke075Rounded, strokeWidth: '2px' }}>
      <circle cx="11.6" cy="11.7" r="10.3" />
      <line x1="4.3" y1="4.4" x2="18.8" y2="19" />
    </g>
  ),
});
