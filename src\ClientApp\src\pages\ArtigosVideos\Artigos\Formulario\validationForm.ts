import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';
import { SelectOptions } from 'components/Select/SelectDefault';

export type FormData = {
  titulo: string;
  ativo: boolean;
  link: string;
  planos: SelectOptions[] | null;
  sistemas: SelectOptions[] | null;
  telas: SelectOptions[] | null;
};

export const formDefaultValues = {
  titulo: '',
  ativo: true,
  link: '',
  planos: null,
  sistemas: null,
  telas: null,
};

const schema = yup.object().shape({
  link: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  titulo: yup
    .string()
    .max(100, EnumValidacoesSistema.MAX_LENGTH_100)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  planos: yup
    .array()
    .nullable()
    .min(1, EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  sistemas: yup.array().nullable(),
  telas: yup.array().nullable(),
});

export const yupResolver = yupResolverInstance(schema);
