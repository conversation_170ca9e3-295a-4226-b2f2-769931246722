const enumTipoServicoPlano = [
  { label: 'Teste', value: 0 },
  { label: 'Start', value: 1 },
  { label: 'Basic', value: 22 },
  { label: 'Pro', value: 2 },
  { label: 'Prime', value: 3 },
  { label: 'Cortesia', value: 4 },
  { label: 'Demonstração', value: 5 },
];

const enumTipoServicoIntegracao = [
  { label: 'Tray', value: 6 },
  { label: 'Mercado Livre', value: 7 },
  { label: 'Shopee', value: 8 },
  { label: 'NuvemShop', value: 9 },
  { label: 'OnPedido', value: 20 },
  { label: 'Fomer Delivery', value: 21 },
  { label: 'Cardápio Digital', value: 26 },
];

const enumTipoServicoUsuario = [{ label: 'Licença', value: 10 }];

const enumTipoServicoModulo = [
  { label: 'Dashboard', value: 11 },
  { label: 'Comanda Garcom', value: 23 },
  { label: 'Frente de caixa', value: 24 },
];

const enumTipoServicoDispositivo = [
  { label: 'PDV', value: 12 },
  { label: 'SMART POS', value: 13 },
  { label: 'Frente de caixa', value: 16 },
  { label: 'Comanda de garçom', value: 17 },
  { label: 'Autoatendimento', value: 18 },
  { label: 'TEF', value: 19 },
];
const enumTipoServicoAvulso = [
  { label: 'Individual', value: 14 },
  { label: 'Recorrente', value: 15 },
];

export const enumTipoServico = {
  PLANO: 0,
  INTEGRACAO: 1,
  USUARIO: 2,
  MODULO: 3,
  DISPOSITIVO: 4,
  AVULSO: 5,

  options: [
    { label: 'Plano', value: 0, properties: enumTipoServicoPlano },
    { label: 'Integração', value: 1, properties: enumTipoServicoIntegracao },
    { label: 'Usuário', value: 2, properties: enumTipoServicoUsuario },
    { label: 'Módulo', value: 3, properties: enumTipoServicoModulo },
    { label: 'Dispositivo', value: 4, properties: enumTipoServicoDispositivo },
    { label: 'Avulso', value: 5, properties: enumTipoServicoAvulso },
  ],
};
