import {
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Switch as ChakraSwitch,
  SwitchProps,
} from '@chakra-ui/react';
import { Controller } from 'react-hook-form';

export interface SwitchDefaultProps extends SwitchProps {
  name: string;
  id: string;
  label?: string;
  isRequired?: boolean;
  colorLabel?: string;
  colorScheme?: string;
  color?: string;
  fontLabel?: string;
  isChecked?: boolean;
  size?: string;
  fontSize?: string;
  fontWeight?: string;
  isDisabled?: boolean;
  textoAuxiliar?: string;
  fontWeightTextoAuxiliar?: string;
  fontSizeTextoAuxiliar?: string;
  fontColorTextoAuxiliar?: string;
  width?: string | string[];
}

export const SwitchDefault = ({
  name,
  id,
  label,
  isRequired,
  color = 'black',
  colorLabel = 'black',
  colorScheme = 'blue',
  isChecked,
  size,
  fontLabel = 'xs',
  fontSize = 'sm',
  fontWeight = 'medium',
  isDisabled,
  textoAuxiliar,
  fontWeightTextoAuxiliar = 'normal',
  fontSizeTextoAuxiliar = 'sm',
  fontColorTextoAuxiliar = 'black',
  width,
  ...rest
}: SwitchDefaultProps) => {
  return (
    <Controller
      name={name}
      render={({ field: { onChange, name, value }, fieldState: { error } }) => {
        return (
          <FormControl
            isInvalid={!!error}
            isRequired={isRequired}
            display="flex"
            flexDirection="column"
            width={width}
          >
            {label && (
              <FormLabel
                cursor="pointer"
                htmlFor={name}
                userSelect="none"
                fontWeight={fontWeight}
                fontSize={fontLabel}
                color={colorLabel}
                onClick={() => {
                  !value;
                }}
              >
                {label}
              </FormLabel>
            )}
            <Flex
              justifyContent={textoAuxiliar ? 'flex-end' : 'undefined'}
              alignItems={textoAuxiliar ? 'center' : 'undefined'}
              flexDirection={textoAuxiliar ? 'row-reverse' : 'row'}
            >
              {textoAuxiliar && (
                <FormLabel
                  cursor="pointer"
                  htmlFor={name}
                  userSelect="none"
                  fontWeight={fontWeightTextoAuxiliar}
                  fontSize={fontSizeTextoAuxiliar}
                  color={fontColorTextoAuxiliar}
                  mb="0px"
                  ml="12px"
                >
                  {textoAuxiliar}
                </FormLabel>
              )}
              <ChakraSwitch
                mt="6px"
                id={id}
                colorScheme={colorScheme}
                size={size}
                name={name}
                value={value}
                fontSize={fontSize}
                color={color}
                isDisabled={isDisabled}
                isChecked={isChecked}
                onChange={onChange}
                checked={value}
                fontWeight=""
                {...rest}
              />
            </Flex>
            {!!error && <FormErrorMessage>{error.message}</FormErrorMessage>}
          </FormControl>
        );
      }}
    />
  );
};
