import { useCallback, useEffect, useState } from 'react';
import { Flex, Icon, Link, Td, Tr, Text, Tooltip, Box } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { FiSearch } from 'react-icons/fi';
import { toast } from 'react-toastify';

import auth from 'modules/auth';
import api, { ResponseApi } from 'services/api';

import { EnumStatusCadastros } from 'constants/Enum/enumStatusCadastros';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { formatQueryPagedTableZenflix } from 'helpers/queryParamsFormat';

import { ButtonDefault } from 'components/Button';
import { ActionsMenu } from 'components/ActionsMenu';
import { Pagination } from 'components/Grid/Pagination';
import { ContainerListagem } from 'components/Grid/GridList';
import { ModalCancelar } from 'components/Modal/ModalCancelar';
import { SelectDefault } from 'components/Select/SelectDefault';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { BiCopy } from 'react-icons/bi';
import { MdOutlineContentCopy } from 'react-icons/md';

type FormData = {
  titulo: string;
  ativo: boolean | null;
};

type VideoProps = {
  id: string;
  ativo: boolean;
  titulo: string;
  categoriaTreinamentoId: string;
  temaId: string;
  video: string;
};

type Options = {
  label: string;
  value: string;
};

interface OptionsResponse {
  id: string;
  titulo: string;
}

const ListarTreinamentos = () => {
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [listaVideos, setListaVideos] = useState<VideoProps[]>([]);
  const [currentFilters, setCurrentFilters] = useState({} as FormData);
  const [temas, setTemas] = useState<Options[]>([]);
  const [categorias, setCategorias] = useState<Options[]>([]);

  const navigate = useNavigate();

  const formMethods = useForm<FormData>({
    defaultValues: {
      ativo: EnumStatusCadastros.TODOS.value,
      titulo: '',
    },
  });
  const { watch } = formMethods;
  const tituloWatch = watch('titulo');

  const possuiPermissaoAlterarVideo = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.Zenflix.ALTERAR_VIDEOS
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<VideoProps>>
      >(
        formatQueryPagedTableZenflix(
          ConstantEnderecoWebservice.LISTAR_VIDEOS,
          gridPaginadaConsulta
        ),
        {
          params: currentFilters,
        }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        if (response.sucesso && response.dados) {
          setListaVideos(response.dados.registros);
          setTotalRegistros(response.dados.total);
        }
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const getTemas = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<OptionsResponse[]>>(
      ConstantEnderecoWebservice.OBTER_TEMA
    );

    if (response.sucesso) {
      const { dados } = response;

      const listTemas = dados.map((tema) => {
        return {
          label: tema.titulo,
          value: tema.id,
        };
      });

      setTemas(listTemas);
    }

    setIsLoading(false);
  }, []);

  const getCategorias = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<OptionsResponse[]>>(
      ConstantEnderecoWebservice.OBTER_CATEGORIA
    );

    if (response.sucesso) {
      const { dados } = response;

      const listCategorias = dados.map((tema) => {
        return {
          label: tema.titulo,
          value: tema.id,
        };
      });

      setCategorias(listCategorias);
    }

    setIsLoading(false);
  }, []);

  const handleExcluirVideo = useCallback(
    async (videoId: string) => {
      ModalCancelar({
        title: 'Você tem certeza?',
        description: 'A ação irá excluir este vídeo!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        colorScheme: 'red',
        isVisibleMotivo: false,
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi<VideoProps>>(
            `${ConstantEnderecoWebservice.EXCLUIR_VIDEO}/${videoId}`
          );

          if (response) {
            if (response.avisos) {
              response.avisos.forEach((aviso) => toast.warning(aviso));
            }
            if (response.sucesso) {
              setRecarregarListagem(!recarregarListagem);
              toast.success('Cadastro excluído com sucesso.');
              return true;
            }
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );

  const handleAlterarVideo = useCallback((videoId: string) => {
    if (!possuiPermissaoAlterarVideo) return;
    navigate(
      SubstituirParametroRota(ConstanteRotas.VIDEOS_ALTERAR, 'id', videoId)
    );
  }, []);

  const getTemaById = useCallback(
    (id: string) => {
      return temas?.find((tema) => tema?.value === id)?.label;
    },
    [temas]
  );

  const getCategoriaById = useCallback(
    (id: string) => {
      return categorias?.find((categoria) => categoria?.value === id)?.label;
    },
    [categorias]
  );

  useEffect(() => {
    getTemas();
  }, [getTemas]);

  useEffect(() => {
    getCategorias();
  }, [getCategorias]);

  return (
    <FormProvider {...formMethods}>
      <ContainerListagem
        inputPesquisa={
          <InputDefault
            maxLength={100}
            placeholder="Buscar por titulo"
            iconLeftElement={FiSearch}
            name="titulo"
            onEnterKeyPress={() => {
              setCurrentFilters((filtrosJaAdicionados) => ({
                ...filtrosJaAdicionados,
                titulo: tituloWatch,
              }));
            }}
          />
        }
        filtrosListagem={
          <SelectDefault
            name="ativo"
            placeholder="Selecione um filtro"
            filtrosAtivos
            asControlledByObject={false}
            onSelect={(optionSelecionada) =>
              setCurrentFilters((filtrosJaAdicionados) => ({
                ...filtrosJaAdicionados,
                ativo: optionSelecionada?.value,
              }))
            }
            options={EnumStatusCadastros.properties.map((status) => status)}
          />
        }
        buttonCadastrar={
          <ButtonDefault
            onClick={() => navigate(ConstanteRotas.VIDEOS_CADASTRAR)}
            width={['full', 'full', 'full', '220px']}
            color="white"
            colorScheme="secondary"
            leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
            possuiFuncionalidade={auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.Zenflix.CADASTRAR_VIDEOS
            )}
          >
            Cadastrar novo
          </ButtonDefault>
        }
      >
        <Pagination
          currentPage={page}
          isLoading={isLoading}
          nPages={totalRegistros}
          setCurrentPage={setPage}
          loadColumnsData={paginationHandle}
          defaultKeyOrdered="titulo"
          defaultOrderDirection="asc"
          tableHeaders={[
            {
              content: <StatusCircle hasValue={false} />,
              key: 'ativo',
              width: '1px',
              isOrderable: false,
            },
            {
              content: 'Título',
              key: 'titulo',
              width: '40%',
              isOrderable: false,
            },
            {
              content: 'Categoria',
              key: 'categoria',
              width: '15%',
              isOrderable: false,
            },
            {
              content: 'Tema',
              key: 'tema',
              isOrderable: false,
              width: '15%',
            },
            {
              content: 'Plataforma de treinamento',
              key: 'id',
              isOrderable: false,
              width: '15%',
            },
            {
              content: 'Youtube',
              key: 'video',
              isOrderable: false,
              width: '15%',
            },
            {
              content: 'Ações',
              key: 'acoes',
              isOrderable: false,
              width: '10px',
            },
          ]}
          renderTableRows={listaVideos?.map((videoItem) => (
            <Tr key={videoItem.id}>
              <Td>
                <StatusCircle isActive={videoItem.ativo} />
              </Td>
              <Td pb="3px" pt="3px">
                <Flex w="full">
                  <Link
                    lineHeight="12.5px"
                    maxW="full"
                    whiteSpace="pre-line"
                    cursor="pointer"
                    onClick={() => handleAlterarVideo(videoItem.id)}
                  >
                    {videoItem.titulo}
                  </Link>
                </Flex>
              </Td>
              <Td>{getCategoriaById(videoItem?.categoriaTreinamentoId)}</Td>
              <Td>{getTemaById(videoItem?.temaId)}</Td>
              <Td>
                <Flex alignItems="center" gap="12px">
                  <Text
                    onClick={() => {
                      const url = `http://localhost:7000/aula/${videoItem.id}`;
                      const urlProducao = `https://zenflix.zendar.app/aula/${videoItem.id}`;

                      if (auth.isDevMode()) {
                        window.open(url, '_blank');
                        return;
                      }
                      window.open(urlProducao, '_blank');
                    }}
                    cursor="pointer"
                    color="blue.500"
                    _hover={{ textDecoration: 'underline' }}
                  >
                    Ver na nossa plataforma
                  </Text>
                  <Tooltip
                    label="Copiar link do vídeo"
                    placement="right-end"
                    borderRadius="6px"
                    bg="black"
                    hasArrow
                    padding="12px"
                  >
                    <Box mt="6px">
                      <Icon
                        cursor="pointer"
                        as={MdOutlineContentCopy}
                        boxSize="14px"
                        fill="blue.500"
                        onClick={() => {
                          navigator.clipboard.writeText(videoItem.id);
                          toast.success('Link copiado com sucesso!');
                        }}
                      />
                    </Box>
                  </Tooltip>
                </Flex>
              </Td>
              <Td>
                <Text
                  onClick={() => window.open(videoItem?.video, '_blank')}
                  cursor="pointer"
                  color="blue.500"
                  _hover={{
                    textDecoration: videoItem?.video ? 'underline' : '',
                  }}
                >
                  {videoItem?.video ? 'Ver no Youtube' : 'Sem link no Youtube'}
                </Text>
              </Td>
              <Td>
                <Flex justifyContent="right">
                  <ActionsMenu
                    id="videoAcoes"
                    items={[
                      {
                        content: 'Editar',
                        onClick: () => handleAlterarVideo(videoItem.id),
                        possuiFuncionalidade: possuiPermissaoAlterarVideo,
                      },
                      {
                        content: 'Excluir',
                        onClick: () => handleExcluirVideo(videoItem.id),
                        possuiFuncionalidade: auth.usuarioPossuiPermissao(
                          ConstantFuncionalidades.Zenflix.EXCLUIR_VIDEOS
                        ),
                      },
                    ]}
                  />
                </Flex>
              </Td>
            </Tr>
          ))}
        />
      </ContainerListagem>
    </FormProvider>
  );
};

export default ListarTreinamentos;
