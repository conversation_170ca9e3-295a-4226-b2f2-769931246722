import React from 'react';
import { useCallback, useEffect, useState } from 'react';
import {
  Box,
  Divider,
  Flex,
  FormLabel,
  GridItem,
  Icon,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
} from '@chakra-ui/react';
import { Controller, useFormContext } from 'react-hook-form';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { addDays } from 'date-fns';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { moneyMask } from 'helpers/format/fieldsMasks';
import auth from 'modules/auth';
import api, { ResponseApi } from 'services/api';
import { useAssinaturasContext } from 'store/Assinaturas/AssinaturaContext';
import { enumTipoServico } from 'constants/Enum/enumTipoServicos';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import {
  DominiosDeliveryEnum,
  optionsFomerZendar,
  optionsPowerStockChef,
} from 'constants/Enum/enumDominios';
import { formatDate } from 'helpers/format/formatStringDate';

import { AccordionDefault, ContentAccordionProps } from 'components/Accordion';
import { ActionsMenu } from 'components/ActionsMenu';
import { ButtonDefault } from 'components/Button';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { InputPhone } from 'components/Input/InputChakra/InputPhone';
import { ModalAdicionarServicoAssinatura } from 'components/Modal/ModalAdicionarServicoAssinatura';
import { ModalConfirmacao } from 'components/Modal/ModalConfirmacao';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { SelectDefault } from 'components/Select/SelectDefault';
import { TextAreaDefault } from 'components/TextArea';
import { EditarSituacaoIcon, NaoSelecionadoIcon } from 'icons';

import { InfoLoja } from './InfoLoja';
import { ServicoAdicionalProps } from './validationForms';
import { useAssinaturaFormulario } from './hook';
import InputDate from 'components/Input/InputDate';
import { IconTooltip } from 'components/IconTooltip';
import { FiAlertTriangle, FiInfo } from 'react-icons/fi';
import { SwitchDefault } from 'components/Switch';

type SelectProps = {
  nome: string;
  id: string;
};

type FormAssinaturasProps = {
  isAlterar?: boolean;
  isDominio?: boolean;
  isCadastrar?: boolean;
};


export const FormAssinaturas = ({
  isAlterar = false,
  isDominio = false,
}: FormAssinaturasProps) => {
  const [listarTabelaPreco, setListarTabelaPreco] = useState<SelectProps[]>([]);
  const [podeAlterarDominioDelivery, setPodeAlterarDominioDelivery] =
    useState(false);

  const {
    isCriarNovaLoja,
    listServicosAdicionais,
    listServicosContratados,
    setListServicosAdicionais,
    dadosCancelamento,
    existeDominioDelivery,
  } = useAssinaturasContext();

  const {
    opcoesDominiosDelivery,
    usuarioNomeDominio,
    usuarioRevendaPrincipal,
  } = useAssinaturaFormulario();

  const { watch, setValue } = useFormContext();

  const tabelaPrecoWatch = watch('tabelaPrecoId');
  const existeServico = listServicosAdicionais.length > 0;

  const possuiPermissaoAlterarContaCliente = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.AssinaturaAcao.CONTA_CLIENTE_ALTERAR
  );

  const diaVencimentoWatch = watch('diaVencimento');

  useEffect(() => {
    if (diaVencimentoWatch && !isDominio) {
      const diaVencimentoNumber = typeof diaVencimentoWatch === 'string'
        ? parseInt(diaVencimentoWatch, 10)
        : diaVencimentoWatch;

      if (!isNaN(diaVencimentoNumber)) {
        const today = new Date();
        const currentYear = today.getFullYear();
        const nextMonth = today.getMonth() + 1;

        // Calculate expiration date: diaVencimento + 10 days in next month
        const dataExpiracao = new Date(currentYear, nextMonth, diaVencimentoNumber + 10);

        setValue('dataExpiracao', dataExpiracao);
      }
    }
  }, [diaVencimentoWatch, isDominio, setValue]);
  const optionsDominios = usuarioRevendaPrincipal
    ? optionsFomerZendar
    : optionsPowerStockChef;

  const defaultDominio =
    optionsDominios.find((item) => item.value === usuarioNomeDominio) ??
    optionsDominios[0];

  const isCadastrar = useCallback(() => {
    if (isAlterar) {
      return false;
    } else if (isCriarNovaLoja) {
      return false;
    }
    return true;
  }, [isCriarNovaLoja, isAlterar])();

  const handleAdicionarNovoServico = useCallback(
    (itensServico: ServicoAdicionalProps) => {
      const newService = itensServico;
      const itemJaFoiAdicionado = itensServico?.serviceIndex !== undefined;

      setListServicosAdicionais((prev) => {
        const { serviceIndex } = itensServico;

        if (!itemJaFoiAdicionado || serviceIndex === undefined) {
          return [...prev, newService];
        }

        const prevUpdate = [...prev];
        prevUpdate.splice(serviceIndex, 1, {
          ...newService,
        });

        return [...prevUpdate];
      });
    },
    []
  );

  const handleAbrirModalServicoAssinatura = useCallback(
    (index?: number, isEditable?: boolean) => {
      let propsModalServico;

      const data = {
        handleAdicionarNovoServico,
        listServicos: listServicosAdicionais,
        tabelaPrecoId: tabelaPrecoWatch,
      };

      if (
        isEditable &&
        index !== undefined &&
        listServicosAdicionais.length > 0
      ) {
        propsModalServico = {
          ...data,
          isEditable: listServicosAdicionais[index],
          serviceIndex: index,
        };
      } else {
        propsModalServico = data;
      }

      ModalAdicionarServicoAssinatura(propsModalServico);
    },
    [listServicosAdicionais, handleAdicionarNovoServico, tabelaPrecoWatch]
  );

  const excluirServico = useCallback(
    (index: number) => {
      const newListServico = [...listServicosAdicionais];
      newListServico.splice(index, 1);
      setListServicosAdicionais(newListServico);
    },
    [listServicosAdicionais]
  );

  const handleExcluirServico = useCallback(
    (index: number, tipo: number) => {
      if (tipo === enumTipoServico.PLANO) {
        ModalWarning({
          title: 'Você tem certeza?',
          description:
            'Se você excluir esse plano terá que adicionar um novo para completar o cadastro',
          confirmButtonText: 'Sim, continuar!',
          cancelButtonText: 'Cancelar',
          onConfirm: async () => {
            await excluirServico(index);
            return true;
          },
        });
      } else {
        excluirServico(index);
      }
    },
    [excluirServico]
  );

  const getTabelaPreco = useCallback(async () => {
    const response = await api.get<void, ResponseApi<SelectProps[]>>(
      ConstantEnderecoWebservice.TABELA_PRECO_LISTAR_SELECT
    );

    if (response.sucesso) {
      const { dados } = response;
      setListarTabelaPreco(dados);
    }
  }, []);

  const desabilitarCampos = useCallback(
    (campoComPermissao?: boolean) => {
      if (isCadastrar) return false;
      if (!isDominio) return true;

      if (!possuiPermissaoAlterarContaCliente && campoComPermissao) return true;

      return false;
    },
    [isCadastrar, isDominio, possuiPermissaoAlterarContaCliente]
  );

  const getDominioDefault = useCallback(() => {
    const nameDominio = auth.getUserNameDominio();

    const optionsDominios = usuarioRevendaPrincipal
      ? optionsFomerZendar
      : optionsPowerStockChef;

    const defaultDominio =
      optionsDominios.find((item) => item.value === nameDominio) ??
      optionsDominios[0];
    setValue('urlDominio', defaultDominio.value);
  }, []);

  useEffect(() => {
    setPodeAlterarDominioDelivery(!existeDominioDelivery);
  }, [existeDominioDelivery]);

  useEffect(() => {
    getTabelaPreco();
  }, [getTabelaPreco]);

  useEffect(() => {
    getDominioDefault();
  }, [getDominioDefault]);

  return (
    <AccordionDefault
      defaultIndex={[0, 1, 2, 3]}
      contentAccordion={[
        {
          title: 'Dados do responsável da conta',
          children: (
            <SimpleGridForm>
              <GridItem colSpan={[12, 12, 9]}>
                <InputDefault
                  name="nome"
                  placeholder="Informe o nome"
                  label="Nome"
                  isRequired
                  maxLength={60}
                  isDisabled={desabilitarCampos(false)}
                />
              </GridItem>
              {isCadastrar && (
                <GridItem colSpan={[12, 12, 3]}>
                  <InputDefault
                    name="usuario"
                    blockSpecialCharacters
                    placeholder="Informe o usuário"
                    label="Usuário"
                    isRequired
                    isDisabled={desabilitarCampos(false)}
                  />
                </GridItem>
              )}
              <GridItem colSpan={[12, 12, 3]}>
                <InputPhone
                  name="telefoneResponsavel"
                  placeholder="Informe o celular/telefone"
                  label="Telefone/celular"
                  isRequired
                  maxLength={15}
                  isDisabled={desabilitarCampos(false)}
                />
              </GridItem>
              <GridItem colSpan={[12, 12, 9]}>
                <InputDefault
                  name="email"
                  placeholder="Informe o e-mail"
                  label="E-mail"
                  isRequired
                  maxLength={80}
                  isDisabled={desabilitarCampos(false)}
                />
              </GridItem>
              <GridItem colSpan={12}>
                <Flex whiteSpace="nowrap" w="full" gap="24px">
                  <Box w="80%">
                    <InputDefault
                      name="dominio"
                      placeholder=""
                      label="Identificador na URL de acesso (domínio)"
                      blockSpecialCharacters
                      maxLength={45}
                      isRequired
                      isDisabled={desabilitarCampos(false)}
                    />
                  </Box>
                  <Box w="20%" cursor="pointer">
                    <SelectDefault
                      name="urlDominio"
                      label="Domínio"
                      isDisabled={
                        (isAlterar && !isDominio) ||
                        (isAlterar && !usuarioRevendaPrincipal)
                      }
                      asControlledByObject={false}
                      defaultValue={isCadastrar ? defaultDominio : null}
                      options={optionsDominios}
                    />
                  </Box>
                </Flex>
              </GridItem>
              {isAlterar && isDominio && (
                <GridItem colSpan={12}>
                  <Flex whiteSpace="nowrap" w="full" gap="24px">
                    <Flex w="80%" justify="flex-end" align="flex-end" gap={4}>
                      <InputDefault
                        name="dominioDelivery"
                        placeholder=""
                        label="Identificador na URL do cardápio digital (domínio)"
                        blockSpecialCharacters
                        maxLength={45}
                        isDisabled={!podeAlterarDominioDelivery}
                      />
                      {existeDominioDelivery && (
                        <Tooltip
                          label="Alterar identificador na URL do cardápio digital"
                          bg="black"
                          py="18px"
                          px="18px"
                          placement="auto"
                          borderRadius="md"
                          isDisabled={podeAlterarDominioDelivery}
                          hasArrow
                        >
                          <Flex
                            height="36px"
                            width="36px"
                            bg="white"
                            borderWidth="1px"
                            borderColor="gray.200"
                            borderRadius="md"
                            cursor="pointer"
                            justify="center"
                            align="center"
                            onClick={() => {
                              if (podeAlterarDominioDelivery) {
                                setPodeAlterarDominioDelivery((prev) => !prev);
                                return;
                              }

                              ModalConfirmacao({
                                texto:
                                  'A alteração do campo de domínio do cardápio digital fará com que o link atual e o QrCode, caso tenha sido gerado, deixem de funcionar pois essa ação irá alterar o endereço de acesso do cardápio. Se mesmo assim quiser alterar o campo.',
                                aoConfirmar: async ({ onClose }) => {
                                  setPodeAlterarDominioDelivery(
                                    (prev) => !prev
                                  );
                                  onClose();
                                },
                              });
                            }}
                            _hover={{ borderColor: 'gray.300' }}
                          >
                            <Icon
                              as={
                                podeAlterarDominioDelivery
                                  ? NaoSelecionadoIcon
                                  : EditarSituacaoIcon
                              }
                            />
                          </Flex>
                        </Tooltip>
                      )}
                    </Flex>
                    <Box w="20%" cursor="pointer">
                      <SelectDefault
                        name="urlDominioDelivery"
                        label="Domínio"
                        options={opcoesDominiosDelivery}
                        isDisabled
                        asControlledByObject={false}
                      />
                    </Box>
                  </Flex>
                </GridItem>
              )}
              {auth.usuarioPossuiPermissao(
                ConstantFuncionalidades.AssinaturaAcao
                  .ASSINATURA_EXIBIR_BANCO_DADOS
              ) && (
                <GridItem colSpan={12}>
                  <InputDefault
                    name="bancoDados"
                    placeholder="Informe o banco de dados ou irá gerar automaticamente (somente Admin e Dev)"
                    label="Banco de dados"
                    maxLength={32}
                    isDisabled={isAlterar || desabilitarCampos(true)}
                  />
                </GridItem>
              )}
            </SimpleGridForm>
          ),
        },
        isDominio
          ? ({} as ContentAccordionProps)
          : {
              title: 'Informações da loja',
              children: (
                <SimpleGridForm>
                  <GridItem colSpan={12}>
                    <InfoLoja />
                  </GridItem>
                  <GridItem colSpan={12}>
                    <TextAreaDefault
                      name="observacao"
                      id="observacao"
                      placeholder="Informe a observação"
                      label="Observação"
                    />
                  </GridItem>
                  {dadosCancelamento &&
                    dadosCancelamento.dataCancelamento !== null && (
                      <GridItem color="red.300" colSpan={12}>
                        <Text textTransform="capitalize">
                          Motivo do cancelamento:{' '}
                          {dadosCancelamento?.motivoCancelamento}
                        </Text>
                        <Text>
                          Data do cancelamento:{' '}
                          {formatDate(dadosCancelamento?.dataCancelamento)}
                        </Text>
                      </GridItem>
                    )}
                </SimpleGridForm>
              ),
            },
            isDominio
          ? ({} as ContentAccordionProps)
          : {
  title: 'Validação',
  children: (
    <SimpleGridForm>
      <Flex
        direction={{ base: 'column', md: 'row' }} // Stack vertically on small screens, row on medium and larger
        gap={{ base: 4, md: 6 }} // Smaller gap on mobile, larger on desktop
        alignItems={{ base: 'stretch', md: 'flex-end' }} // Stretch on mobile, align at bottom on desktop
        width="100%"
      >
        <Box flex={{ md: 1 }} minWidth={{ md: '150px' }}> {/* Flexible width, minimum for readability */}
          <Flex align="center" gap="6px">
            <Box flex="1">
              <SelectDefault
                name="diaVencimento"
                label="Dia Vencimento"
                isRequired={true}
                isDisabled={!auth.usuarioPossuiPermissao(ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_DIA_VENCIMENTO)}
                options={Array.from({ length: 28 }, (_, i) => ({
                  value: String(i + 1),
                  label: String(i + 1)
                }))}
                asControlledByObject={false}
                defaultValue={{ value: '10', label: '10' }}
              />
            </Box>
            {
              auth.usuarioPossuiPermissao(ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_DIA_VENCIMENTO) && (
                <Flex align="center" alignSelf={{ base: 'flex-start', md: 'flex-end' }} mt={{ base: '24px', md: '0' }}>
              <IconTooltip
                icon={FiInfo}
                color="yellow.700"
                labelTooltip="Data Expiração (bloqueio) ocorre 10 dias após o Dia de Vencimento."
              />
            </Flex>
            )}
          </Flex>
        </Box>

        <Box flex={{ md: 1 }} minWidth={{ md: '250px' }}>
          <FormLabel id="dataExpiracao" fontSize="xs" color="black">
            Próxima Data de Expiração
          </FormLabel>
          <Controller
            name="dataExpiracao"
            render={({ field: { onChange, value, name } }) => (
              <InputDate
                name={name}
                value={value}
                onChange={(newValue) => onChange(newValue)}
                isDisabled={true}
              />
            )}
          />
        </Box>

        <Box flex={{ md: 1 }} minWidth={{ md: '250px' }}>
          <FormLabel id="dataUltimaValidacao" fontSize="xs" color="black">
            Data Última Validação
          </FormLabel>
          <Controller
            name="dataUltimaValidacao"
            render={({ field: { onChange, value, name } }) => (
              <InputDate
                name={name}
                value={value}
                onChange={(newValue) => onChange(newValue)}
                isDisabled={true}
              />
            )}
          />
        </Box>

        <Box
          flex={{ md: 1 }}
          minWidth={{ md: '150px' }}
          display="flex"
          alignItems="flex-end"
          justifyContent={{ base: 'flex-start', md: 'flex-end' }}
        >
          <Controller
            name="validacaoAutomatica"
            render={({ field: { onChange, value, name } }) => (
              <SwitchDefault
                name={name}
                id="validacaoAutomatica"
                label="Validação Automática"
                isChecked={value}
                onChange={onChange}
              />
            )}
          />
        </Box>
      </Flex>
    </SimpleGridForm>
  ),
},
        isDominio
          ? ({} as ContentAccordionProps)
          : {
              title: 'Planos e demais serviços',
              children: (
                <Box w="full">
                  <Flex
                    flexDirection={[
                      'column-reverse',
                      'column-reverse',
                      'column-reverse',
                      'initial',
                    ]}
                    direction={['column', 'column', 'column', 'row']}
                    justifyContent="space-between"
                    alignItems="center"
                  >
                    <Box w={['full', 'full', 'full', '50%']}>
<Flex
  mt={['10px', '10px', '10px', '0']}
  mr={['0', '0', '0', '20px']}
  justifyContent="space-between" // Distribui o espaço entre os elementos
  alignItems="flex-end" // Alinha os itens na parte inferior
  w="full" // Garante que o Flex ocupe toda a largura disponível
>
  <Box w={['full', 'full', 'full', '50%']}> {/* Mantém o combo ocupando 50% da largura em telas grandes */}
    <SelectDefault
      name="tabelaPrecoId"
      label="Tabela de preço"
      asControlledByObject={false}
      isDisabled={existeServico}
      isRequired
      options={listarTabelaPreco.map((tabelaItem) => ({
        value: tabelaItem.id,
        label: tabelaItem.nome,
      }))}
    />
  </Box>
  <ButtonDefault
    colorScheme="green"
    color="white"
    width={['full', 'full', 'full', '200px']} // Mantém a largura do botão

    possuiFuncionalidade={dadosCancelamento?.dataCancelamento === null && auth.usuarioPossuiPermissao(
      ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_ALTERAR_SERVICO
    )}
    leftIcon={<IoIosAddCircleOutline />}
    isDisabled={!tabelaPrecoWatch}
    onClick={() => handleAbrirModalServicoAssinatura()}
  >
    Adicionar
  </ButtonDefault>
</Flex>
                    </Box>

                  </Flex>
                  <Box>

                    {listServicosAdicionais.length > 0 || (listServicosContratados && listServicosContratados.length > 0) ? (
                      <Table
                        sx={{
                          '& tr > th': { borderBottom: 'none' },
                          '& td:only-child': {
                            bg: 'white',
                            h: '60px',
                            border: 'none',
                          },
                          '& tr': { boxShadow: 'none' },
                          '& th': {
                            fontSize: '2xs',
                            color: 'gray.300',
                            paddingBottom: '5px',
                          },
                        }}
                        variant="simple-card"
                        mt="20px"
                      >
                        <Thead>
                          <Tr>
                            <Th fontSize="xs" w="20%">
                              Nome
                            </Th>
                            <Th w="10%" isNumeric>
                              Quantidade
                            </Th>
                            <Th w="5%" isNumeric>
                              Valor Un.
                            </Th>
                            <Th w="5%" isNumeric>
                              Valor Total
                            </Th>
                            <Th w="1%" isNumeric />
                          </Tr>
                        </Thead>
                        <Tbody>
                          {/* Renderiza os serviços adicionais */}
                          {listServicosAdicionais.map((servicos, index) => (
                            <React.Fragment key={servicos.servicoId}>
                              {index > 0 && <Box h="5px"/>}
                              <Tr key={servicos.servicoId}>
                                <Td>{servicos.servicoNome}</Td>
                                <Td isNumeric>{servicos.quantidade}</Td>
                                <Td isNumeric whiteSpace="nowrap">
                                  {moneyMask(Number(servicos.valorUnitarioRepasse), true)}
                                </Td>
                                <Td isNumeric whiteSpace="nowrap">
                                  {moneyMask(Number(servicos.valorTotalRepasse), true)}
                                </Td>
                                {(auth.usuarioPossuiPermissao(
                                  ConstantFuncionalidades.AssinaturaAcao
                                    .ASSINATURA_ALTERAR_SERVICO
                                ) ||
                                  auth.usuarioPossuiPermissao(
                                    ConstantFuncionalidades.AssinaturaAcao
                                      .ASSINATURA_EXCLUIR_SERVICO
                                  )) && (
                                  <Td isNumeric>
                                    <ActionsMenu
                                      isFullScreen={false}
                                      items={[
                                        {
                                          content: 'Editar',
                                          onClick: () =>
                                            handleAbrirModalServicoAssinatura(
                                              index,
                                              true
                                            ),
                                          possuiFuncionalidade:
                                            auth.usuarioPossuiPermissao(
                                              ConstantFuncionalidades
                                                .AssinaturaAcao
                                                .ASSINATURA_ALTERAR_SERVICO
                                            ),
                                        },
                                        {
                                          content: 'Excluir',
                                          onClick: () =>
                                            handleExcluirServico(
                                              index,
                                              servicos.tipo
                                            ),
                                          possuiFuncionalidade:
                                            auth.usuarioPossuiPermissao(
                                              ConstantFuncionalidades
                                                .AssinaturaAcao
                                                .ASSINATURA_EXCLUIR_SERVICO
                                            ),
                                        },
                                      ]}
                                    />
                                  </Td>
                                )}
                              </Tr>
                            </React.Fragment>
                          ))}

                          {/* Separador entre as listas */}
                          {listServicosAdicionais.length > 0 && listServicosContratados && listServicosContratados.length > 0 && (
                            <Tr>
                              <Td colSpan={5} py={4}>
                                <Flex align="center">
                                  <Divider />
                                  <Text px={2} fontSize="sm" fontWeight="medium" color="gray.500">
                                     Demais Serviços Contratados até final do mês
                                  </Text>
                                  <Divider />
                                </Flex>
                              </Td>
                            </Tr>
                          )}

                          {/* Renderiza os serviços contratados */}
                          {listServicosContratados && listServicosContratados.map((servico, index) => (
                            <React.Fragment key={`contratado-${servico.servicoId}`}>
                              {index > 0 && <Box h="5px"/>}
                              <Tr key={`contratado-${servico.servicoId}`}>
                                <Td>{servico.servicoNome}</Td>
                                <Td isNumeric>{servico.quantidade}</Td>
                                <Td isNumeric whiteSpace="nowrap">
                                  {moneyMask(Number(servico.valorUnitarioRepasse), true)}
                                </Td>
                                <Td isNumeric whiteSpace="nowrap">
                                  {moneyMask(Number(servico.valorTotalRepasse), true)}
                                </Td>
                                <Td></Td> {/* Célula vazia para manter o alinhamento */}
                              </Tr>
                            </React.Fragment>
                          ))}
                        </Tbody>
                      </Table>
                    ) : (
                      <Text mt="20px" fontSize="xs">
                        Nenhum serviço foi adicionado
                      </Text>
                    )}
                  </Box>
                </Box>
              ),
            },
      ]}
    />
  );
};
