import { InputProps } from '@chakra-ui/react';
import { ChangeEvent, useCallback } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { IconType } from 'react-icons';

import { InputChakra } from '..';

export interface InputDefaultProps extends InputProps {
  name: string;
  label?: string;
  colorLabel?: string;
  fontLabel?: string;
  
  iconLeftElement?: IconType;
  onEnterKeyPress?: (value?: string) => void;
  blockSpecialCharacters?: boolean;
}

export const InputDefault = ({
  name,
  label,
  isDisabled,
  blockSpecialCharacters = false,
  colorLabel = 'black',
  isRequired,
  onEnterKeyPress,
  type,
  onKeyPress,
  fontLabel,
  placeholder,
  fontSize = 'sm',
  iconLeftElement,
  variant,
  ...rest
}: InputDefaultProps) => {
  const { setValue } = useFormContext();

  const typeIsNumber = type === 'number';

  const formatValueInput = useCallback(
    (value: any) => {
      if (blockSpecialCharacters) {
        return value?.replace(/[^a-z0-9]/gi, '');
      } else if (typeIsNumber) {
        return value && String(value)?.replace(/\D/g, '');
      }

      return value;
    },
    [typeIsNumber, blockSpecialCharacters]
  );

  const keyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (typeIsNumber) {
        const forbiddenKeys = ['.', 'e', 'E'];

        if (forbiddenKeys.includes(e.key)) {
          e.preventDefault();
        }
      }
    },
    [typeIsNumber]
  );

  return (
    <Controller
      name={name}
      render={({
        field: { onChange, onBlur, value, name },
        fieldState: { error },
      }) => {
        return (
          <InputChakra
            onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => {
              if (e.key === 'Enter' && onEnterKeyPress) {
                e.currentTarget.value = e.currentTarget.value.trim();

                onEnterKeyPress(value);
              }

              if (onKeyPress) onKeyPress(e);
            }}
            error={error}
            iconLeftElement={iconLeftElement}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              if (onChange) onChange(e);

              setValue(name, formatValueInput(e.target.value));
            }}
            name={name}
            isRequired={isRequired}
            fontLabel={fontLabel}
            value={formatValueInput(value)}
            onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) =>
              keyDown(event)
            }
            placeholder={placeholder}
            label={label}
            colorLabel={colorLabel}
            isDisabled={isDisabled}
            onBlur={onBlur}
            variant={variant}
            fontSize={fontSize}
            type={type}
            {...rest}
          />
        );
      }}
    />
  );
};
