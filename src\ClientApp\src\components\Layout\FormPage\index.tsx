import { Box, Flex, Icon } from '@chakra-ui/react';
import { IoIosAddCircleOutline } from 'react-icons/io';

import { ButtonDefault } from 'components/Button';
import { LoadingDefault } from 'components/Loading';

type LayoutFormPageProps = {
  children: React.ReactNode;
  onSubmit?: () => void;
  onResetSubmit?: () => void;
  isLoading: boolean;
  isDisabledSubmit?: boolean;
  isDisabledReset?: boolean;
  button?: React.ReactNode;
};

export const LayoutFormPage = ({
  children,
  onSubmit,
  onResetSubmit,
  isDisabledSubmit = false,
  isDisabledReset = false,
  isLoading,
  button,
}: LayoutFormPageProps) => {
  return (
    <>
      {isLoading && <LoadingDefault />}
      <Box
        borderRadius="5px"
        p={['20px', '20px', '35px', '35px']}
        boxShadow="primary"
      >
        {children}
      </Box>
      <Flex
        justifyContent={button ? 'space-between' : 'center'}
        alignItems="center"
        display={['column', 'column', 'flex', 'flex']}
        mt="40px"
        mb="40px"
      >
        {button && button}
        {onResetSubmit && (
          <ButtonDefault
            width={['full', 'full', 'full', '220px']}
            variant="outlinePill"
            mr={['0', '0', '30px']}
            mb={['30px', '30px', '0']}
            borderWidth="1px"
            colorScheme="primary"
            color="primary.400"
            isDisabled={isDisabledReset}
            onClick={() => onResetSubmit()}
            leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
            possuiFuncionalidade={true}
          >
            Salvar e inserir novo
          </ButtonDefault>
        )}
        {onSubmit && (
          <ButtonDefault
            width={['full', 'full', 'full', '220px']}
            color="white"
            isDisabled={isDisabledSubmit}
            colorScheme="secondary"
            onClick={() => onSubmit()}
            leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
            possuiFuncionalidade={true}
          >
            Salvar
          </ButtonDefault>
        )}
        {button && <Box width={['full', 'full', 'full', '220px']} />}
      </Flex>
    </>
  );
};
