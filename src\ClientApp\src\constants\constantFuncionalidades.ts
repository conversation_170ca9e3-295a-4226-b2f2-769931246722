import { AssinaturaAcao } from './Funcionalidades/assinatura';
import { FaturamentoAcao } from './Funcionalidades/faturamento';
import { AtualizacaoAcao } from './Funcionalidades/atualizacao';
import { ImportacaoNcmAcao } from './Funcionalidades/importacao_ncm';
import { FicalNFCeAcao } from './Funcionalidades/fiscal_nfce';
import { NotaFiscalUrlAcao } from './Funcionalidades/fiscal_notafiscalurl';
import { NotaFiscalRejeicaoAcao } from './Funcionalidades/fiscal_notafiscalrejeicao';
import { NotaFicalValidacaoAcao } from './Funcionalidades/fiscal_notafiscalvalidacao';
import { NotaFiscalRegraUrlAcao } from './Funcionalidades/fiscal_regrafiscalurl';
import { LogErroAcao } from './Funcionalidades/log_logerros';
import { AcessoMenu } from './Funcionalidades/acesso_menu';
import { CadastroGradeServicoAcao } from './Funcionalidades/cadastro_gradeservico';
import { CadastroProdutoAcao } from './Funcionalidades/cadastro_produto';
import { CadastroRevendaAcao } from './Funcionalidades/cadastro_revenda';
import { CadastroServicoAcao } from './Funcionalidades/cadastro_servico';
import { CadastroTabelaPrecoAcao } from './Funcionalidades/cadastro_tabelapreco';
import { CadastroUsuarioAcao } from './Funcionalidades/cadastro_usuario';
import { Zenflix } from './Funcionalidades/zenflix';

export const ConstantFuncionalidades = {
  AcessoMenu,

  AssinaturaAcao,
  FaturamentoAcao,
  AtualizacaoAcao,

  CadastroRevendaAcao,
  CadastroProdutoAcao,
  CadastroServicoAcao,
  CadastroGradeServicoAcao,
  CadastroTabelaPrecoAcao,
  CadastroUsuarioAcao,

  FicalNFCeAcao,
  NotaFiscalUrlAcao,
  NotaFiscalRejeicaoAcao,
  NotaFicalValidacaoAcao,
  NotaFiscalRegraUrlAcao,
  ImportacaoNcmAcao,
  LogErroAcao,
  Zenflix,
};
