import { Flex, Link } from '@chakra-ui/react';
import { useSortable } from '@dnd-kit/sortable';

import { ActionsMenu } from 'components/ActionsMenu';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';

export interface ItemDraggrableProp {
  nome: string;
}

export const ItemDraggrable = ({ nome }: ItemDraggrableProp) => {
  const { attributes, isDragging, listeners, setNodeRef } = useSortable({
    id: nome,
  });

  return (
    <Flex
      w="100%"
      opacity={isDragging ? 0.4 : undefined}
      bg={isDragging ? 'transparent' : 'gray.50'}
      p="12px"
      mt="8px"
      border={isDragging ? '1px dashed teal' : '1px solid lightgray'}
      borderRadius="4px"
      alignItems="center"
      justifyContent="space-between"
      ref={setNodeRef}
      {...attributes}
      {...listeners}
    >
      <Flex gap="16px" w="full">
        <StatusCircle isActive={true} />
        <Link
          lineHeight="12.5px"
          whiteSpace="pre-line"
          cursor="pointer"
          userSelect="none"
        >
          {nome}
        </Link>
      </Flex>

      <Flex alignSelf="flex-end">
        <ActionsMenu
          id="storybookAcoes"
          menuZIndex="base"
          items={[
            {
              content: 'Editar',
              possuiFuncionalidade: true,
            },
            {
              content: 'Excluir',
              possuiFuncionalidade: true,
            },
          ]}
        />
      </Flex>
    </Flex>
  );
};
