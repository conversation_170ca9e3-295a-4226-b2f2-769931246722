import {
  Box,
  Flex,
  ModalBody,
  Text,
  ModalProps as ModalChakraProps,
  ModalContent,
  useDisclosure,
  ModalFooter,
  Button,
  ModalHeader,
  Input,
} from '@chakra-ui/react';
import { create, InstanceProps } from 'react-modal-promise';
import { FormProvider, useForm } from 'react-hook-form';

import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { ModalDefaultChakra } from 'components/Modal/ModalDefaultChakra';

type ModalProps = Omit<ModalChakraProps, 'children' | 'isOpen' | 'onClose'> &
  InstanceProps<ModalChakraProps> & {
    titulo?: string;
    aoConfirmar: ({ onClose }: { onClose: () => void }) => Promise<void>;
    aoCancelar: ({ onClose }: { onClose: () => void }) => void;
  };

export const ModalRemoverDominio = create<ModalProps>(
  ({
    onReject,
    onResolve,
    aoConfirmar,
    aoCancelar,
    titulo = 'Você tem certeza?',
    closeOnOverlayClick = true,
    ...rest
  }) => {
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    return (
      <ModalDefaultChakra
        closeOnOverlayClick={closeOnOverlayClick}
        size={['full', '2xl']}
        {...rest}
        isCentered
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <ModalContent bg="gray.50">
          <ModalHeader pt="40px" pb={0} px={8}>
            <Text fontSize="20px" textColor="red.500">
              {titulo}
            </Text>
          </ModalHeader>
          <ModalBody py={0} px={8}>
            <Text my="22px" fontSize="16px" color="black" whiteSpace="pre-wrap">
              O campo de domínio do cardápio digital está vazio. Isso resultará
              na exclusão do link atual e do QR Code, caso ele tenha sido
              gerado, fazendo com que ambos deixem de funcionar. Confirme esta
              ação se deseja realmente remover o domínio.
            </Text>
          </ModalBody>
          <ModalFooter py="40px" px={8}>
            <Flex
              w="full"
              justifyContent="center"
              alignItems="center"
              mt="10px"
              pb="0"
              px="0"
            >
              <Button
                colorScheme="gray.300"
                mr="20px"
                w="120px"
                variant="outlinePill"
                onClick={() => aoCancelar({ onClose })}
              >
                Cancelar
              </Button>
              <Button
                w="240px"
                variant="solid"
                colorScheme="red"
                onClick={() => aoConfirmar({ onClose })}
              >
                Remover domínio
              </Button>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </ModalDefaultChakra>
    );
  }
);
