export interface FaturamentoAssinaturaProps {
  id: string;
  valorTotalRepasse: number;
  subTotal: number;
  valorAcrescimoDesconto: number;
  motivoAcrescimoDesconto: string;
  dataEmissao: string;
  dataVencimento: string;  
}

export interface FaturamentoInfoProps {
  nomeFantasia: string;
  idAssinatura: string;
}

export type FormData = {
  dataVencimentoInicio: Date | null;
  dataVencimentoFim: Date | null;
};

export const defaultValues = {
  dataVencimentoInicio: null,
  dataVencimentoFim: null,
};
