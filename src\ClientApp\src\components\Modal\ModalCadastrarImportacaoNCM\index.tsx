import {
  Box,
  Flex,
  Grid<PERSON>tem,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalProps,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { create, InstanceProps } from 'react-modal-promise';
import { toast } from 'react-toastify';
import { useDropzone } from 'react-dropzone';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';

import { ButtonDefault } from 'components/Button';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { LoadingDefault } from 'components/Loading';
import { ModalDefaultChakra } from 'components/Modal/ModalDefaultChakra';

type ModalCadastrarImportacaoNCMProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> & {
  atualizarListagem: () => void;
} & InstanceProps<ModalProps>;

export const ModalCadastrarImportacaoNCM =
  create<ModalCadastrarImportacaoNCMProps>(({ atualizarListagem, ...rest }) => {
    const [isLoading, setIsLoading] = useState(false);
    const { acceptedFiles, fileRejections, getRootProps, getInputProps } =
      useDropzone({
        accept: { file: ['.zip'] },
      });
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const files = acceptedFiles.map((file: File) => (
      <Text key={file.name}>
        {file.name} - {file.size} bytes
      </Text>
    ));

    const toBase64 = (file: File) =>
      new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
      });

    const onSubmit = useCallback(async () => {
      setIsLoading(true);
      if (!files[0]) {
        toast.warning('Favor selecionar um arquivo válido para enviar');
        setIsLoading(false);
        return;
      }

      const arquivo64 = await toBase64(acceptedFiles[0]);

      const response = await api.post<void, ResponseApi>(
        ConstantEnderecoWebservice.CADASTRAR_IMPORTACAO_NCM,
        { arquivo64: String(arquivo64).split(',')[1] }
      );
      if (response.sucesso) {
        toast.success('Cadastro realizado com sucesso.');
        atualizarListagem();
        onClose();
        setIsLoading(false);
      }
      setIsLoading(false);
    }, [files]);

    useEffect(() => {
      if (fileRejections.length > 0) {
        toast.error('O arquivo não é de um tipo válido');
      }
    }, [fileRejections]);

    return (
      <ModalDefaultChakra
        {...rest}
        isCentered
        size={['full', 'full', '4xl']}
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <ModalContent bg="white" w={['full', 'full', 'lg']} h={['full', 'lg']}>
          {isLoading && <LoadingDefault />}
          <ModalHeader borderTopRadius="base" bg="primary.600" pb="3.5">
            <Flex
              color="white"
              pt="1.5"
              justifyContent="space-between"
              alignItems="center"
            >
              <Text pl="0.5">Importação de NCM</Text>
              <ModalCloseButton mt="3" mr="2.5" />
            </Flex>
          </ModalHeader>
          <ModalBody pt="9">
            <Text>
              Importe os dados da tabela do Instituto Brasileiro de Planejamento
              e Tributação (IBPT) de um arquivo{' '}
              <Text as="span" fontWeight="bold">
                .ZIP
              </Text>{' '}
              arrastando e soltando o mesmo no retângulo cinza abaixo. Ou clique
              no botão para selecioná-lo em uma pasta de seu computador.
            </Text>
            <SimpleGridForm mt="6">
              <GridItem colSpan={[12]}>
                <Flex
                  bg="gray.50"
                  {...getRootProps()}
                  p="5"
                  height="28"
                  alignItems="center"
                  justifyContent="center"
                  textAlign="center"
                  borderWidth="medium"
                  borderStyle="dashed"
                >
                  <Box {...getInputProps()} />
                  <Text>
                    {files.length > 0
                      ? files
                      : 'Arraste um arquivo ou clique aqui para selecionar'}
                  </Text>
                </Flex>
              </GridItem>
            </SimpleGridForm>
          </ModalBody>
          <ModalFooter>
            <Flex
              w="full"
              justifyContent={['center', 'center', 'right']}
              flexDirection={['column', 'row', 'row']}
            >
              <ButtonDefault
                width={['full', '32', '32', '32']}
                colorScheme="gray"
                mr="5"
                mb={['5', 'undefined', 'undefined']}
                variant="outlinePill"
                onClick={() => onClose()}
                possuiFuncionalidade={true}
              >
                Fechar
              </ButtonDefault>
              <ButtonDefault
                width={['full', '36', '36', '36']}
                color="white"
                colorScheme="secondary"
                onClick={() => onSubmit()}
                possuiFuncionalidade={true}
              >
                Enviar Arquivo
              </ButtonDefault>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </ModalDefaultChakra>
    );
  });
