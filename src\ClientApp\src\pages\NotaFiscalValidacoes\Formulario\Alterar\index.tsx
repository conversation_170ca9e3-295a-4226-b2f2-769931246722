import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import api, { ResponseApi } from 'services/api';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormNotaFiscalValidacoes } from '..';
import { formDefaultValues, FormData, yupResolver } from '../validationForms';

type NotaFiscalValidacoesResponse = {
  codigo: string;
  descricao: string;
};

export const AlterarNotaFiscalValidacoes = () => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });

  const { handleSubmit, reset } = formMethods;

  const navigate = useNavigate();
  const idRouter = useParams();
  const { id: idRota } = idRouter;

  const getNotaFiscalValidacoes = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<
      void,
      ResponseApi<NotaFiscalValidacoesResponse>
    >(ConstantEnderecoWebservice.OBTER_NF_VALIDACAO, {
      params: { id: idRota },
    });

    if (response.sucesso) {
      reset(response.dados);
    } else {
      navigate(ConstanteRotas.NOTA_FISCAL_VALIDACOES);
    }

    setIsLoading(false);
  }, [reset, idRota]);

  const handleAlterarNotaFiscal = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await api.put<
      void,
      ResponseApi<NotaFiscalValidacoesResponse>
    >(ConstantEnderecoWebservice.ALTERAR_NF_VALIDACAO, {
      ...data,
      id: idRota,
    });

    if (response.sucesso) {
      toast.success('Cadastro alterado com sucesso.');
      navigate(ConstanteRotas.NOTA_FISCAL_VALIDACOES);
    }

    setIsLoading(false);
  });

  useEffect(() => {
    getNotaFiscalValidacoes();
  }, [getNotaFiscalValidacoes]);

  return (
    <LayoutFormPage
      onSubmit={() => handleAlterarNotaFiscal()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormNotaFiscalValidacoes isReadOnly />
      </FormProvider>
    </LayoutFormPage>
  );
};
