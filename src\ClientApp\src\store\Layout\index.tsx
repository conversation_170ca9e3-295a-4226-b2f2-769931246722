import { useMediaQuery } from '@chakra-ui/react';
import React, {
  createContext,
  useState,
  useContext,
  Dispatch,
  SetStateAction,
  useEffect,
} from 'react';

export type ContentItemProps = {
  title?: string;
  keyMenu: string;
  colorHover?: string;
  onClick?: () => void;
  possuiFuncionalidade?: boolean;
  isSeparator?: boolean;
};

export interface BreadcrumbInterface {
  title: string;
  path?: string;
}

interface LayoutContextProps {
  breadcrumb: BreadcrumbInterface[];
  setBreadcrumb: Dispatch<SetStateAction<BreadcrumbInterface[]>>;
  valueItemMenu: ContentItemProps[];
  setValueItemMenu: React.Dispatch<React.SetStateAction<ContentItemProps[]>>;
  setIsOpenMenu: React.Dispatch<React.SetStateAction<boolean>>;
  isOpenMenu: boolean;
  handleClickOutside: (openMenu: boolean) => void;
  nameItemMenu: string;
  handleGetKeyName: (name: string) => void;
  isMenuAberto: boolean;
}

const LayoutContext = createContext<LayoutContextProps>(
  {} as LayoutContextProps
);

interface LayoutProviderProps {
  children: React.ReactNode;
}

export default function LayoutProvider({
  children,
}: LayoutProviderProps): JSX.Element {
  const [breadcrumb, setBreadcrumb] = useState<BreadcrumbInterface[]>([]);
  const [valueItemMenu, setValueItemMenu] = useState<ContentItemProps[]>([]);
  const [isOpenMenu, setIsOpenMenu] = useState(false);
  const [nameItemMenu, setNameItemMenu] = useState('');

  const [isMenuAberto] = useMediaQuery('(min-width: 768px)');

  const handleClickOutside = (openMenu: boolean) => {
    setIsOpenMenu(openMenu);
  };

  const handleGetKeyName = (name: string) => {
    setNameItemMenu(name);
  };

  useEffect(() => {
    const clickOutside = () => {
      handleClickOutside(false);
    };
    document.addEventListener('click', clickOutside, true);
    return () => {
      document.removeEventListener('click', clickOutside, true);
    };
  }, [handleClickOutside]);

  return (
    <LayoutContext.Provider
      value={{
        breadcrumb,
        nameItemMenu,
        handleGetKeyName,
        setBreadcrumb,
        valueItemMenu,
        isMenuAberto,
        setValueItemMenu,
        setIsOpenMenu,
        handleClickOutside,
        isOpenMenu,
      }}
    >
      {children}
    </LayoutContext.Provider>
  );
}

export function useLayoutContext(): LayoutContextProps {
  const context = useContext(LayoutContext);

  if (!context)
    throw new Error('useLayoutContext must be used within a LayoutProvider.');

  return context;
}
