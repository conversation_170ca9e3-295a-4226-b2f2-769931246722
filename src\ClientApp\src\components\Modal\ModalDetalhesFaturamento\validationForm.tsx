export interface FaturamentoAssinaturaProps {
  id: string;
  valor: number;
  dataEmissao: string;
}

interface ItensFaturaProps {
  id: string;
  servicoNome: string;
  dataContratacao: string;
  quantidade: number;
  valorUnitarioRepasse: number;
  valorTotalRepasse: number;
}

export interface FaturamentoProps {
  fantasia: string;
  dataEmissao: string;
  subTotal: number;
  valorAcrescimoDesconto: number;
  motivoAcrescimoDesconto?: string;
  totalRepasse: number;
  itensFatura: ItensFaturaProps[];
}

export type FormData = {
  dataVencimentoInicio: Date;
  dataVencimentoFim: Date;
};

const dataAtual = new Date();
const dataAtualMenos30Dias = new Date(
  dataAtual.setDate(dataAtual.getDate() - 30)
);

export const defaultValues = {
  dataVencimentoInicio: new Date(dataAtualMenos30Dias),
  dataVencimentoFim: new Date(),
};
