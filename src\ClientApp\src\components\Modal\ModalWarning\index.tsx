import { useState } from 'react';
import {
  ModalContent,
  ModalBody,
  Flex,
  Button,
  Text,
  ScaleFade,
  ModalProps,
  useDisclosure,
  ModalFooter,
  Icon,
  Box,
} from '@chakra-ui/react';
import { create, InstanceProps } from 'react-modal-promise';

import { LoadingDefault } from 'components/Loading';
import { AtencaoAvisoIcon } from 'icons';

import { ModalDefaultChakra } from '../ModalDefaultChakra';

type ModalWarningProps = Omit<ModalProps, 'children' | 'isOpen' | 'onClose'> &
  InstanceProps<ModalProps> & {
    cancelButtonText?: string;
    confirmButtonText?: string;
    description?: string;
    title?: string;
    closeOnOverlayClick?: boolean;
    onConfirm: () => Promise<boolean>;
  };

export const ModalWarning = create<ModalWarningProps>(
  ({
    onReject,
    onResolve,
    onConfirm,
    cancelButtonText = 'Não, cancelar!',
    confirmButtonText = 'Sim, apagar!',
    description = 'Não será possível desfazer esta ação.',
    title = 'Você tem certeza?',
    closeOnOverlayClick = true,
    ...rest
  }) => {
    const [isLoading, setIsLoading] = useState(false);
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const handleOnConfirm = async () => {
      setIsLoading(true);
      const sucess = await onConfirm();

      if (sucess) {
        onClose();
        setIsLoading(false);
      }
      setIsLoading(false);
    };

    return (
      <ModalDefaultChakra
        closeOnOverlayClick={closeOnOverlayClick}
        size={['full', 'xl']}
        {...rest}
        isCentered
        isOpen={isOpen}
        onClose={onClose}
        autoFocus={false}
      >
        <ModalContent
          marginBottom={{ base: 0, sm: '3.75rem' }}
          marginTop={{ base: 0, sm: '3.75rem' }}
          h={['full', 'unset', 'unset']}
          bg="gray.50"
          maxW={['full', '600px', '600px']}
        >
          {isLoading && <LoadingDefault />}
          <ScaleFade initialScale={1.5} in={isOpen}>
            <ModalBody p={0} h="100%">
              <Box>
                <Flex
                  justifyContent="center"
                  h="120px"
                  bg="orange.400"
                  alignItems="center"
                  borderTopLeftRadius="5px"
                  borderTopRightRadius="5px"
                >
                  <Icon fontSize="75px" as={AtencaoAvisoIcon} />
                </Flex>
                <Box h={['undefined', '150px']} pt="20px" pl="30px">
                  <Text fontSize="2xl" color="orange.400">
                    {title}
                  </Text>
                  <Text mt="15px">{description}</Text>
                </Box>
              </Box>
            </ModalBody>
            <ModalFooter>
              <Flex
                w="full"
                justifyContent="center"
                alignItems="center"
                pb="20px"
                px="0"
              >
                <Button
                  id="cancelModal"
                  colorScheme="gray.300"
                  mr="20px"
                  variant="outlinePill"
                  w="100px"
                  onClick={() => {
                    onClose();
                  }}
                >
                  {cancelButtonText}
                </Button>
                <Button
                  id="confirmModal"
                  variant="solid"
                  colorScheme="orange"
                  onClick={() => handleOnConfirm()}
                >
                  {confirmButtonText}
                </Button>
              </Flex>
            </ModalFooter>
          </ScaleFade>
        </ModalContent>
      </ModalDefaultChakra>
    );
  }
);
