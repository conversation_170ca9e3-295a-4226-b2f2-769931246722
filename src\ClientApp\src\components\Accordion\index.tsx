import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  AccordionProps,
  Box,
} from '@chakra-ui/react';

export type ContentAccordionProps = {
  title: string;
  children: React.ReactNode;
};

interface AccordionDefaultProps extends AccordionProps {
  contentAccordion: ContentAccordionProps[];
  defaultIndex: number[];
}

export const AccordionDefault = ({
  contentAccordion,
  defaultIndex = [0],
  ...rest
}: AccordionDefaultProps) => {
  return (
    <Accordion defaultIndex={defaultIndex} allowMultiple {...rest}>
      {contentAccordion.map((accordion) => {
        if (!accordion.title) return null;

        return (
          <AccordionItem mb="20px" key={accordion.title}>
            <AccordionButton>
              <Box flex="1" textAlign="left">
                {accordion.title}
              </Box>
              <AccordionIcon />
            </AccordionButton>
            <AccordionPanel p={['15px', '20px', '30px']}>
              {accordion.children}
            </AccordionPanel>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
};
