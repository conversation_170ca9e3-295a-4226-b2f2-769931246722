import { FormProvider, useForm } from 'react-hook-form';

import { Calendario as CalendarExample, CalendarioProps } from '.';

import 'react-date-range/dist/styles.css'; // main css file
import 'react-date-range/dist/theme/default.css'; // theme css file

export const Calendario = ({ name, ...props }: CalendarioProps) => {
  return (
    <FormProvider {...useForm()}>
      <CalendarExample name="data" {...props} />
    </FormProvider>
  );
};
