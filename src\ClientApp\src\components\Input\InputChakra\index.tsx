import { Input, InputProps } from '@chakra-ui/react';
import { FieldError } from 'react-hook-form';
import { IconType } from 'react-icons';

import { CampoPrototipo } from 'components/Layout/CampoPrototipo';

interface FieldErrorProps extends FieldError {
  value?: {
    message?: string;
  };
}

export interface InputDefaultProps extends InputProps {
  name: string;
  label?: string;
  colorLabel?: string;
  fontLabel?: string;
  iconLeftElement?: IconType;
  onEnterKeyPress?: () => void;
  error: FieldErrorProps | undefined;
  onBlur?: any;
  value: string | number;
}

export const InputChakra = ({
  name,
  label,
  isDisabled,
  colorLabel = 'black',
  isRequired,
  onEnterKeyPress,
  onKeyPress,
  placeholder,
  iconLeftElement,
  error,
  onBlur,
  onChange,
  value = '', // Forneça um valor padrão vazio
  fontLabel = 'xs',
  ...rest
}: InputDefaultProps) => {
  return (
    <CampoPrototipo
      iconLeftElement={iconLeftElement}
      error={error}
      fontLabel={fontLabel}
      colorLabel={colorLabel}
      label={label}
      isRequired={isRequired}
    >
      <Input
        onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => {
          if (e.key === 'Enter' && onEnterKeyPress) {
            e.currentTarget.value = e.currentTarget.value.trim();

            onEnterKeyPress();
          }

          if (onKeyPress) onKeyPress(e);
        }}
        isDisabled={isDisabled}
        onBlur={onBlur}
        bg="white"
        _placeholder={{
          color: 'gray.400',
          fontSize: '15px',
        }}
        _autofill={{
          border: '1px solid secondary.600',
          textFillColor: 'black',
          boxShadow: '0 0 0px 1000px transparent inset',
          transition: 'background-color 5000s ease-in-out 0s',
        }}
        placeholder={placeholder}
        _focusVisible={{
          borderColor: 'secondary.600',
          borderWidth: '2px',
        }}
        color="black"
        h="35px"
        fontSize={fontLabel}
        onChange={onChange}
        value={value}
        name={name}
        id={name}
        {...rest}
      />
    </CampoPrototipo>
  );
};
