import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';
import { ResponsavelProps } from 'store/Assinaturas/AssinaturaContext';

export const getClienteObterResponsavel = async (idCliente: string) => {
  const response = await api.get<void, ResponseApi<ResponsavelProps>>(
    ConstantEnderecoWebservice.OBTER_RESPONSAVEL_CLIENTE,
    {
      params: {
        id: idCliente,
      },
    }
  );

  return response;
};
