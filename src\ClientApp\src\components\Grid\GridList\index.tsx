import { GridItem } from '@chakra-ui/react';
import { useCallback } from 'react';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';

type ContainerListagemProps = {
  inputPesquisa: React.ReactNode;
  buttonCadastrar: React.ReactNode;
  children: React.ReactNode;
  filtrosListagem?: React.ReactNode;
};

export const ContainerListagem = ({
  inputPesquisa,
  buttonCadastrar,
  children,
  filtrosListagem,
}: ContainerListagemProps) => {
  const sizeInputPesquisa = useCallback(() => {
    if (filtrosListagem) {
      return [12, 12, 12, 6, 6];
    }

    return [12, 12, 6, 6, 6];
  }, [])();

  const sizeButtonCadastrarNovo = useCallback(() => {
    if (filtrosListagem) {
      return [12, 12, 12, 3, 4];
    }

    return [12, 12, 6, 6, 6];
  }, [])();

  const sizeFiltrosListagem = useCallback(() => {
    if (filtrosListagem) {
      return [12, 12, 12, 3, 2];
    }

    return [12, 12, 2, 3, 2];
  }, [])();

  return (
    <SimpleGridForm>
      <GridItem colSpan={sizeInputPesquisa}>{inputPesquisa}</GridItem>

      {filtrosListagem && (
        <GridItem colSpan={sizeFiltrosListagem}>{filtrosListagem}</GridItem>
      )}

      <GridItem
        display="flex"
        justifyContent={['flex-start', 'flex-end']}
        colSpan={sizeButtonCadastrarNovo}
      >
        {buttonCadastrar}
      </GridItem>

      <GridItem mt="-15px" colSpan={12}>
        {children}
      </GridItem>
    </SimpleGridForm>
  );
};
