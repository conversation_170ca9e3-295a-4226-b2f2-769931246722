## Antes de começar seguir os passos

- ### [Instalador do Yarn para Windows](https://classic.yarnpkg.com/latest.msi)
- ### Instalar extensões no VSCode
  - ESLint
  - Prettier - Code formatter
  - EditorConfig for VS Code
- ### [Ctrl+Shift+P] > Open Settings JSON
  ```json
  "editor.codeActionsOnSave": {
  "source.fixAll.eslint": true
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
  ```
- ### Executar o comando `npm i`, reiniciar o VSCode e verificar se ele está aplicando a formatação de código automaticamente.

## Projeto criado com:

### Utilizado `npm create vite@latest [nome do app] --react-ts` para criar o projeto

## Scripts disponíveis

No diretório do projeto, você pode executar:

### `npm run dev`

Executa a aplicação em modo de desenvolvimento.<br />
Abra [http://localhost:3000](http://localhost:3000) para visualizar no browser.

### `npm run test`

Executa os testes da aplicação usando cypress.

### `npm run storybook`

Abre a documentação dos componentes usando storybook.

### `npm run build`

Executa o build, minifica e prepara os pacotes na pasta `build` em modo de produção.

### `npm run eject`

**Nota: esta é uma operação unilateral. Depois de 'ejetar', você não pode mais voltar!**

Se não estiver satisfeito com a ferramenta de construção e as opções de configuração, você pode `ejetar` a qualquer momento. Este comando removerá a dependência de compilação única de seu projeto.

Em vez disso, ele copiará todos os arquivos de configuração e as dependências transitivas (webpack, Babel, ESLint, etc) diretamente para o seu projeto para que você tenha controle total sobre eles. Todos os comandos, exceto `eject`, ainda funcionarão, mas irão apontar para os scripts copiados para que você possa ajustá-los. Neste ponto, você está sozinho.

Você não precisa usar `ejetar`. O conjunto de recursos selecionados é adequado para implantações pequenas e médias, e você não deve se sentir obrigado a usar esse recurso. No entanto, entendemos que esta ferramenta não seria útil se você não pudesse personalizá-la quando estiver pronto para ela.
