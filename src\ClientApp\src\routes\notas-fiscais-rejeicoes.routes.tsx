import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { NotaFiscalRejeicoes } from 'pages/NotaFiscalRejeicoes/Listar';
import { CadastrarNotaFiscalRejeicao } from 'pages/NotaFiscalRejeicoes/Formulário/Cadastrar';
import { AlterarNotaFiscalRejeicao } from 'pages/NotaFiscalRejeicoes/Formulário/Alterar';

import LayoutGuard from './LayoutGuard';

export const NotaFiscalRejeicoesRoutes = [
  <Route
    key={ConstanteRotas.NOTAFISCAL_REJEICOES_LISTAR}
    path={ConstanteRotas.NOTAFISCAL_REJEICOES_LISTAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.NOTAFISCAL_REJEICOES_LISTAR}
        breadcrumb={[
          { title: 'Fiscal' },
          {
            title: 'NF Rejeições',
            path: ConstanteRotas.NOTAFISCAL_REJEICOES_LISTAR,
          },
        ]}
        component={<NotaFiscalRejeicoes />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_FISCAL_NF_REJEICAO)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.NOTAFISCAL_REJEICOES_CADASTRAR}
    path={ConstanteRotas.NOTAFISCAL_REJEICOES_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.NOTAFISCAL_REJEICOES_CADASTRAR}
        breadcrumb={[
          { title: 'Fiscal' },
          {
            title: 'NF Rejeições',
            path: ConstanteRotas.NOTAFISCAL_REJEICOES_LISTAR,
          },
          {
            title: 'Cadastrar',
          },
        ]}
        component={<CadastrarNotaFiscalRejeicao />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.NotaFiscalRejeicaoAcao.CADASTRAR_NF_REJEICAO)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.NOTAFISCAL_REJEICOES_ALTERAR}
    path={ConstanteRotas.NOTAFISCAL_REJEICOES_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.NOTAFISCAL_REJEICOES_ALTERAR}
        breadcrumb={[
          { title: 'Fiscal' },
          {
            title: 'NF Rejeições',
            path: ConstanteRotas.NOTAFISCAL_REJEICOES_LISTAR,
          },
          {
            title: 'Alterar',
          },
        ]}
        component={<AlterarNotaFiscalRejeicao />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.NotaFiscalRejeicaoAcao.ALTERAR_NF_REJEICAO)}
      />
    }
  />,
];
