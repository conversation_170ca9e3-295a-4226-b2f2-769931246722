interface FontSizes {
  [key: string]: string;
}

export function getFontSize(key: keyof FontSizes) {
  const fontSizes: FontSizes = {
    '2xs': '10px',
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '30px',
    '4xl': '36px',
    '5xl': '48px',
    '6xl': '60px',
    '7xl': '72px',
    '8xl': '96px',
    '9xl': '128px',
  };
  return fontSizes[key];
}
