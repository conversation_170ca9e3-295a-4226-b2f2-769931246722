export interface AtualizacaoSistemaClienteProps {
  id: string;
  clienteBancoDados: string;
  clienteDominio: string;
  status: number;
  tempoExecucao: { [key: string]: number };
  erro?: string;
}

export type GerenciarItemProps = {
  listAtualizacao: AtualizacaoSistemaClienteProps;
  atualizarListagem: () => void;
};

export interface AcoesProps {
  status: number;
  id: string;
  atualizarListagem: () => void;
}
