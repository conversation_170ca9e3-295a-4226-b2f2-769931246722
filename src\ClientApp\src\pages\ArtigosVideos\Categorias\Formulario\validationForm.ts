import * as yup from 'yup';
import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

export type FormData = {
  titulo: string;
  sequenciaOrdenacao: number;
  ativo: boolean;
};

export const formDefaultValues: FormData = {
  titulo: '',
  sequenciaOrdenacao: 0,
  ativo: true,
};

const schema = yup.object().shape({
  titulo: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  sequenciaOrdenacao: yup
    .number()
    .integer()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

export const yupResolver = yupResolverInstance(schema);
