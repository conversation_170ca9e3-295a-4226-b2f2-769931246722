import { VStack } from '@chakra-ui/react';

import { InputDefault } from 'components/Input/InputChakra/InputDefault';

type FormNotaFiscalValidacoesProps = {
  isReadOnly?: boolean;
};

export const FormNotaFiscalValidacoes = ({
  isReadOnly,
}: FormNotaFiscalValidacoesProps) => {
  return (
    <VStack spacing="25px" w={['full', 'full', '50%']}>
      {isReadOnly && (
        <InputDefault
          isDisabled
          w={['full', 'full', '50%']}
          name="codigo"
          label="Código"
        />
      )}
      <InputDefault
        name="descricao"
        placeholder="Informe a descrição"
        label="Descrição"
        isRequired
      />
    </VStack>
  );
};
