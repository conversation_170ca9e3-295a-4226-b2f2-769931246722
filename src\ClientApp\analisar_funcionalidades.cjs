const fs = require('fs');
const path = require('path');

// Mapeamento dos valores do enum para os tipos
const enumMapping = {
    0: 'SISTEMA_ADMIN',
    1: 'SISTEMA_FINANCEIRO',
    2: 'DESENVOLVEDOR',
    3: 'REVENDA_ADMIN',
    5: 'REVENDA_ASSISTENTE',
    6: 'SUPORTE_STI3',
    7: 'CANAIS_GERENTE',
    8: 'ANALISTA_CONTEUDO'
};

// Mapeamento reverso para converter nomes em valores
const nomeParaEnum = {
    'SISTEMA_ADMIN': 0,
    'SISTEMA_FINANCEIRO': 1,
    'DESENVOLVEDOR': 2,
    'REVENDA_ADMIN': 3,
    'REVENDA_ASSISTENTE': 5,
    'SUPORTE_STI3': 6,
    'CANAIS_GERENTE': 7,
    'ANALISTA_CONTEUDO': 8
};

/**
 * Analisa um arquivo TypeScript e extrai as ações e permissões
 */
function analisarArquivo(caminhoArquivo) {
    try {
        const conteudo = fs.readFileSync(caminhoArquivo, 'utf8');
        const acoes = {};
        
        // Extrair o nome da constante exportada
        const matchExport = conteudo.match(/export\s+const\s+(\w+)\s*=/);
        if (!matchExport) {
            console.log(`⚠️  Não foi possível encontrar export const em ${caminhoArquivo}`);
            return {};
        }
        
        const nomeConstante = matchExport[1];
        
        // Encontrar todas as ações definidas
        // Padrão mais flexível para capturar ações com quebras de linha
        const padrao = /(\w+):\s*\[\s*((?:(?:\/\/.*\n\s*)?EnumTipoUsuario\.\w+(?:\s*,\s*)?(?:\/\/.*)?)+)\s*\]/g;
        let match;
        
        while ((match = padrao.exec(conteudo)) !== null) {
            const nomeAcao = match[1];
            const usuariosStr = match[2];
            
            // Extrair os tipos de usuário, ignorando comentários
            const usuariosEnum = [];
            const matches = usuariosStr.match(/EnumTipoUsuario\.(\w+)/g);
            
            if (matches) {
                matches.forEach(enumMatch => {
                    const nomeUsuario = enumMatch.replace('EnumTipoUsuario.', '');
                    if (nomeParaEnum.hasOwnProperty(nomeUsuario)) {
                        usuariosEnum.push(nomeParaEnum[nomeUsuario]);
                    }
                });
            }
            
            // Criar chave com nome da constante
            const chaveAcao = `${nomeConstante}.${nomeAcao}`;
            acoes[chaveAcao] = usuariosEnum;
        }
        
        return acoes;
    } catch (error) {
        console.error(`❌ Erro ao analisar arquivo ${caminhoArquivo}:`, error.message);
        return {};
    }
}

/**
 * Analisa todos os arquivos TypeScript na pasta Funcionalidades
 */
function analisarTodosArquivos() {
    const pastaFuncionalidades = path.join(__dirname, 'src', 'constants', 'Funcionalidades');
    
    console.log(`📁 Analisando arquivos em: ${pastaFuncionalidades}`);
    
    if (!fs.existsSync(pastaFuncionalidades)) {
        console.error(`❌ Pasta não encontrada: ${pastaFuncionalidades}`);
        return {};
    }
    
    const arquivos = fs.readdirSync(pastaFuncionalidades)
        .filter(arquivo => arquivo.endsWith('.ts'))
        .map(arquivo => path.join(pastaFuncionalidades, arquivo));
    
    console.log(`📊 Encontrados ${arquivos.length} arquivos TypeScript`);
    
    const todasAcoes = {};
    
    arquivos.forEach(arquivo => {
        const nomeArquivo = path.basename(arquivo);
        console.log(`🔍 Analisando: ${nomeArquivo}`);
        
        const acoesArquivo = analisarArquivo(arquivo);
        Object.assign(todasAcoes, acoesArquivo);
        
        console.log(`   ✅ Extraídas ${Object.keys(acoesArquivo).length} ações`);
    });
    
    console.log(`\n📈 Total de ações extraídas: ${Object.keys(todasAcoes).length}`);
    return todasAcoes;
}

/**
 * Gera estatísticas por tipo de usuário
 */
function gerarEstatisticas(acoesData) {
    const stats = {};
    Object.values(enumMapping).forEach(tipo => {
        stats[tipo] = 0;
    });
    
    Object.values(acoesData).forEach(usuarios => {
        usuarios.forEach(usuarioEnum => {
            if (enumMapping[usuarioEnum]) {
                stats[enumMapping[usuarioEnum]]++;
            }
        });
    });
    
    return stats;
}

/**
 * Imprime estatísticas das permissões
 */
function imprimirEstatisticas(stats) {
    console.log('\n📈 Estatísticas por tipo de usuário:');
    console.log('='.repeat(50));
    
    Object.entries(stats).forEach(([tipo, count]) => {
        console.log(`${tipo.padEnd(20)}: ${count.toString().padStart(3)} permissões`);
    });
    
    const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
    console.log('='.repeat(50));
    console.log(`${'TOTAL'.padEnd(20)}: ${total.toString().padStart(3)} permissões`);
}

/**
 * Salva os dados das ações em um arquivo JavaScript para uso pelos outros scripts
 */
function salvarDadosAcoes(acoesData) {
    const conteudo = `// Dados das ações extraídos automaticamente dos arquivos TypeScript
// Gerado em: ${new Date().toLocaleString('pt-BR')}

const acoesData = ${JSON.stringify(acoesData, null, 4)};

module.exports = { acoesData };
`;
    
    const nomeArquivo = 'dados_acoes_gerados.cjs';
    fs.writeFileSync(nomeArquivo, conteudo, 'utf8');
    console.log(`💾 Dados salvos em: ${nomeArquivo}`);
    return nomeArquivo;
}

/**
 * Função principal
 */
function main() {
    console.log('🔄 Iniciando análise automática de permissões...');
    console.log('='.repeat(60));
    
    // Analisar todos os arquivos
    const acoesData = analisarTodosArquivos();
    
    if (Object.keys(acoesData).length === 0) {
        console.error('❌ Nenhuma ação foi encontrada!');
        return false;
    }
    
    // Gerar e exibir estatísticas
    const stats = gerarEstatisticas(acoesData);
    imprimirEstatisticas(stats);
    
    // Salvar dados para uso pelos outros scripts
    salvarDadosAcoes(acoesData);
    
    console.log('\n✅ Análise concluída com sucesso!');
    return acoesData;
}

// Executar se chamado diretamente
if (require.main === module) {
    main();
}

module.exports = {
    analisarTodosArquivos,
    gerarEstatisticas,
    imprimirEstatisticas,
    salvarDadosAcoes,
    enumMapping,
    nomeParaEnum
};
