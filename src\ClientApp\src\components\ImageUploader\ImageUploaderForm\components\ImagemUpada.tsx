import { Flex, Box, Image, ImageProps } from '@chakra-ui/react';
import { ControllerRenderProps, FieldValues } from 'react-hook-form';

type ImagemUpadaProps = {
  inputFileRef: React.RefObject<HTMLInputElement>;
  field: ControllerRenderProps<FieldValues, string>;
  handleFile: (file: File, onChange: (value: string | null) => void) => void;
  handleDragOver: (e: React.DragEvent<HTMLDivElement>) => void;
  setOnMouseOver: (value: boolean) => void;
  onMouseOver: boolean;
} & Omit<ImageProps, 'onMouseOver' | 'onMouseLeave'>;

export const ImagemUpada = ({
  handleFile,
  inputFileRef,
  field,
  handleDragOver,
  setOnMouseOver,
  onMouseOver,
  maxH,
  width,
}: ImagemUpadaProps) => {
  return (
    <Flex
      position="relative"
      border="none"
      borderRadius={'md'}
      onMouseOverCapture={() => setOnMouseOver(true)}
      onMouseLeave={() => setOnMouseOver(false)}
      onDrop={(e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();

        const file = e.dataTransfer.files[0];

        handleFile(file, field.onChange);
      }}
      onDragOver={handleDragOver}
    >
      {!onMouseOver && (
        <Image
          src={field.value}
          alt="Imagem da thumbnail"
          maxH={maxH}
          width={width}
        />
      )}
      {onMouseOver && (
        <Flex
          justifyContent="center"
          h="full"
          w="full"
          alignItems="center"
          top="0"
          bg="gray.50"
          px="20px"
          position="absolute"
        >
          <Box fontSize={{ base: 'sm', sm: 'md' }} cursor="pointer">
            <Box
              w="full"
              p="7px"
              bg="white"
              borderTopRadius={'sm'}
              _hover={{
                bg: 'gray.100',
              }}
              onClick={() => {
                field.onChange(null);

                if (inputFileRef.current) {
                  inputFileRef.current.value = '';
                }
              }}
            >
              Excluir imagem
            </Box>
            <Box
              w="full"
              p="7px"
              bg="white"
              borderBottomRadius={'sm'}
              _hover={{
                bg: 'gray.100',
              }}
              onClick={() => {
                if (inputFileRef?.current?.click) {
                  inputFileRef.current.click();
                }
              }}
            >
              Adicionar nova imagem
            </Box>
          </Box>
        </Flex>
      )}
    </Flex>
  );
};
