import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import { LayoutFormPage } from 'components/Layout/FormPage';

import FormTema from '..';
import { formDefaultValues, yupResolver, FormData } from '../validationForm';

const AlterarTema = () => {
  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, reset } = formMethods;

  const { id: idRota } = useParams();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(false);

  const getTemas = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<FormData>>(
      `${ConstantEnderecoWebservice.OBTER_TEMA}/${idRota}`
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso && response.dados) {
        reset(response.dados);
      }
    }
    setIsLoading(false);
  }, [idRota]);

  const handleAlterarTema = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await api.put<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.ALTERAR_TEMA,
      {
        ...data,
        id: idRota,
      }
    );

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso) {
        toast.success('Cadastro alterado com sucesso');
        navigate(ConstanteRotas.TEMAS);
      }
    }
    setIsLoading(false);
  });

  useEffect(() => {
    getTemas();
  }, [getTemas]);

  return (
    <LayoutFormPage onSubmit={() => handleAlterarTema()} isLoading={isLoading}>
      <FormProvider {...formMethods}>
        <FormTema />
      </FormProvider>
    </LayoutFormPage>
  );
};

export default AlterarTema;
