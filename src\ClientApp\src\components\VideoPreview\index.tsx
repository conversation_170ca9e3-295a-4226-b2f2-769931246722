import { useState } from 'react';
import { Box, BoxProps, Flex, Text, VStack } from '@chakra-ui/react';

import { enumStylesThumb } from 'constants/Enum/enumStylesThumb';
import getPlanos from 'helpers/data/getPlanos';

interface VideoPreviewProps extends BoxProps {
  titulo: string;
  tituloThumbnail: string;
  styleThumbnail?: number;
  planos?: {
    label: string;
  }[];
}

const PreviewThumbnail = ({
  titulo = '',
  tituloThumbnail = '',
  planos,
  styleThumbnail,
  ...rest
}: VideoPreviewProps) => {
  const [onMouseOver, setOnMouseOver] = useState(false);

  const listPlanos = getPlanos(planos?.map((plano) => plano.label)?.toString());

  const hasThumbnail = styleThumbnail;

  const styleThumbnailDefault = {
    bg: 'black',
    color: 'white',
  };

  const thumbnail = hasThumbnail
    ? enumStylesThumb[styleThumbnail]
    : styleThumbnailDefault;

  const thumbnailBackground = thumbnail.bg;
  const thumbnailTextColor = thumbnail.color;

  return (
    <Box
      aspectRatio={16 / 9}
      borderRadius="8px"
      bg={thumbnailBackground}
      color={thumbnailTextColor}
      cursor="pointer"
      position="relative"
      display="flex"
      height="153px"
      width="272px"
      alignItems="center"
      title={titulo}
      transition={'all 0.2s ease-in-out'}
      _hover={{
        transform: 'scale(1.02)',
      }}
      onMouseOver={() => setOnMouseOver(true)}
      onMouseLeave={() => setOnMouseOver(false)}
      {...rest}
    >
      <Text
        position="absolute"
        fontSize="28px"
        paddingX="24px"
        wordBreak="break-word"
        letterSpacing="0"
        textTransform="uppercase"
        lineHeight="1.1"
        transform={'rotate(-6deg)'}
        fontFamily="Luckiest Guy, cursive;"
        color={thumbnailTextColor}
      >
        {tituloThumbnail}
      </Text>
      {onMouseOver && (
        <Flex
          py="12px"
          w="full"
          h="full"
          flexDirection="column"
          alignItems="flex-end"
          opacity="0.9"
          borderRadius="8px"
          bg="linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.5) 100%)"
          justifyContent="space-between"
        >
          <VStack gap="4px" align="flex-end">
            {listPlanos?.map((planoItem) => (
              <Box
                h="12px"
                minW="48px"
                px="6px"
                lineHeight="1.2"
                zIndex={5}
                bgColor={planoItem.color}
                borderTopLeftRadius="14px"
                borderBottomLeftRadius="14px"
                key={planoItem.value}
              >
                <Text fontSize="10px" fontWeight="700" color="black">
                  {planoItem.label.replace(/\s/g, '')}
                </Text>
              </Box>
            ))}
          </VStack>
          <Box w="full" px="12px" textAlign="left" noOfLines={1}>
            <Text fontSize="sm" fontWeight="normal" color="white">
              {titulo}
            </Text>
          </Box>
        </Flex>
      )}
    </Box>
  );
};

export default PreviewThumbnail;
