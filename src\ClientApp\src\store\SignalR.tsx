import React, {
  create<PERSON>ontex<PERSON>,
  Dispatch,
  SetStateAction,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react';

import {
  HubConnectionState,
  HubConnectionBuilder,
  LogLevel,
} from '@microsoft/signalr';

interface SignalRContextProps {
  securityStamp: string;
  setSecurityStamp: Dispatch<SetStateAction<string>>;
  hubConnection: signalR.HubConnection;
  joinGroup: (group: string) => Promise<unknown>;
  exitGroup: (group: string) => Promise<void>;
  sendMessageToGroup: (
    groupName: string,
    method: string,
    message?: string
  ) => Promise<unknown>;
}

const SignalRContext = createContext<SignalRContextProps>(
  {} as SignalRContextProps
);

interface SignalRProviderProps {
  children: React.ReactNode | undefined;
}

export default function SignalRProvider({
  children,
}: SignalRProviderProps): JSX.Element {
  const [securityStamp, setSecurityStamp] = useState<string>('');

  const hubConnection = useMemo(
    () =>
      new HubConnectionBuilder()
        .withUrl(
          import.meta.env.VITE_API_URL?.replace(
            'api/',
            'notificationHubMultiEmpresa'
          ) as string
        )
        .configureLogging(LogLevel.Information)
        .withAutomaticReconnect()
        .build(),
    []
  );

  const GetOpenConnection = useCallback(async () => {
    return new Promise((resolve, reject) => {
      const msMax = 5000;
      const msInc = 10;

      let ms = 0;

      const idInterval = setInterval(() => {
        if (hubConnection.state === HubConnectionState.Disconnected) {
          hubConnection.start();
        }

        if (hubConnection.state === HubConnectionState.Connected) {
          clearInterval(idInterval);
          resolve(hubConnection);
        }

        ms += msInc;

        if (ms >= msMax) {
          clearInterval(idInterval);
          reject(hubConnection);
        }
      }, msInc);
    });
  }, [hubConnection]);

  const joinGroup = useCallback(
    async (group: string) => {
      GetOpenConnection().then(() => {
        hubConnection.invoke('JoinGroup', group);
      });
    },
    [GetOpenConnection, hubConnection]
  );

  const exitGroup = useCallback(
    async (group: string) => {
      if (hubConnection.state === HubConnectionState.Connected) {
        await hubConnection.invoke('ExitGroup', group);
      }
    },
    [hubConnection]
  );

  const sendMessageToGroup = useCallback(
    async (groupName: string, method: string, message?: string) => {
      GetOpenConnection().then(() => {
        hubConnection.invoke(
          'SendMessageToGroup',
          groupName,
          method,
          message || ''
        );
      });
    },
    [GetOpenConnection, hubConnection]
  );

  return (
    <SignalRContext.Provider
      value={{
        securityStamp,
        setSecurityStamp,
        hubConnection,
        joinGroup,
        exitGroup,
        sendMessageToGroup,
      }}
    >
      {children}
    </SignalRContext.Provider>
  );
}

export function useSignalRContext(): SignalRContextProps {
  const context = useContext(SignalRContext);

  if (!context)
    throw new Error('useSignalRContext must be used within a SignalRProvider.');

  return context;
}
