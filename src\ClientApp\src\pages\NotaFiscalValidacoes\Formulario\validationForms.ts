import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

export type FormData = {
  descricao: string;
  codigo: string;
};

export const formDefaultValues = {
  descricao: '',
  codigo: '',
};

const schema = yup.object().shape({
  descricao: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
});

export const yupResolver = yupResolverInstance(schema);
