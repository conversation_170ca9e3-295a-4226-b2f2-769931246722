import { useState, useLayoutEffect, useRef, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { VStack, Box } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';

import api, { ResponseApi } from 'services/api';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import { submitInputOnEnter } from 'hook/submitInputOnEnter';

import { InputAutenticacao } from 'components/Input/InputAutenticacao';
import { ButtonDefault } from 'components/Button';
import { Title } from 'components/Layout/Title';
import { Header } from 'components/Layout/Header';

import { yupResolver } from './validationForm';

export const RedefinirSenha = () => {
  const [isLoading, setLoading] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();

  const formMethods = useForm({
    resolver: yupResolver,
    mode: 'onChange',
    defaultValues: {
      senha: '',
      confirmarSenha: '',
    },
  });

  const {
    formState: { isValid, errors },
    handleSubmit,
  } = formMethods;

  const searchParams = new URLSearchParams(location.search);

  const usuarioCodificado = searchParams.get('usuario');
  const tokenCodificado = searchParams.get('token');

  const usuario = usuarioCodificado
    ? decodeURIComponent(usuarioCodificado)
    : '';
  const token = decodeURIComponent(tokenCodificado as string);

  const latestProps = useRef({ token, usuario });

  const handleSubmitResetSenha = handleSubmit(async (data) => {
    setLoading(true);

    const response = await api.put<void, ResponseApi>(
      ConstantEnderecoWebservice.AUTENTICACAO_ALTERAR_SENHA,
      {
        usuario,
        novaSenha: data.senha,
        token,
      }
    );

    if (response.sucesso) {
      navigate(ConstanteRotas.SENHA_REDEFINIDA_COM_SUCESSO);
    }

    setLoading(false);
  });

  useEffect(() => {
    submitInputOnEnter(handleSubmitResetSenha);
  }, []);

  useEffect(() => {
    latestProps.current = { token, usuario };
  });

  useLayoutEffect(() => {
    if (!latestProps.current.token || !latestProps.current.usuario) {
      navigate(ConstanteRotas.LOGIN);
    }
  }, []);

  return (
    <VStack spacing={9}>
      <Header />
      <VStack w="full" spacing={12}>
        <Box
          w="full"
          bg={{ base: 'transparent', md: 'blackAlpha.100' }}
          py={3.5}
          px={6}
          alignItems="flex-start"
        >
          <Title>{usuario || 'Usuário não identificado.'}</Title>
        </Box>
        <FormProvider {...formMethods}>
          <VStack spacing={12} w="full">
            <InputAutenticacao
              id="senha"
              name="senha"
              type="password"
              label="Nova senha"
              bg="none"
              autoComplete="senha"
              variant="floating"
              colorLabel="white"
              isPassword
              isDisabled={isLoading}
              maxLength={50}
              isInvalid={!!errors?.senha?.message}
            />

            <InputAutenticacao
              id="confirmarSenha"
              name="confirmarSenha"
              type="password"
              variant="floating"
              colorLabel="white"
              bg="none"
              label="Confirme a nova senha"
              defaultValue=""
              autoComplete="senha"
              isPassword
              isInvalid={!!errors?.confirmarSenha?.message}
              isDisabled={isLoading}
              maxLength={50}
            />
          </VStack>
          <ButtonDefault
            onClick={() => handleSubmitResetSenha()}
            color={!isValid ? 'primary' : 'white'}
            colorScheme="secondary"
            w="full"
            type="button"
            id="button"
            isDisabled={isLoading || !isValid}
            isLoading={isLoading}
            possuiFuncionalidade={true}
          >
            Confirmar
          </ButtonDefault>
        </FormProvider>
      </VStack>
    </VStack>
  );
};
