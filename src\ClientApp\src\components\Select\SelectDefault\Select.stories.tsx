import React, { useState } from 'react';
import { Meta, StoryFn } from '@storybook/react';
import { Text } from '@chakra-ui/react';

import { SelectDefault as Select } from './exampleSelect';
import { SelectDefaultProps } from '.';

export default {
  title: 'Components/Select',
  argTypes: {
    onSelect: {
      action: 'selected',
      description: 'Evento chamado caso uma opção seja selecionada',
      name: 'onSelect',
    },
    colorLabel: {
      control: { type: 'color' },
    },
    size: {
      control: {
        type: 'select',
      },
      options: ['sm', 'md', 'lg', 'full'],
    },
    isRequired: {
      control: {
        type: 'boolean',
      },
    },
    filtrosAtivos: {
      control: {
        type: 'boolean',
      },
    },
  },
  args: {
    name: 'name',
    placeholder: 'Selecione uma opção',
    label: 'Label',
    id: '',
    options: [
      {
        label: 'Opcao 1',
        value: 'Opcao 1',
      },
      {
        label: 'Opcao 2',
        value: 'Opcao 2',
      },
      {
        label: 'Opcao 3',
        value: 'Opcao 3',
      },
    ],
  },
} as Meta<SelectDefaultProps>;

const Default: StoryFn<SelectDefaultProps> = (args) => {
  const [filters, setCurrentFilters] = useState<{ status: string }>({
    status: '',
  });

  return (
    <>
      <Select
        {...args}
        onSelect={(optionSelecionada) =>
          setCurrentFilters((filtrosJaAdicionados) => ({
            ...filtrosJaAdicionados,
            status: optionSelecionada?.value,
          }))
        }
      />
      <Text mt="4">
        {filters.status
          ? `A opção selecionada foi: ${filters.status}`
          : 'Ao selecionar uma opção ela aparecerá aqui'}
      </Text>
    </>
  );
};
export const SelectDefault = Default.bind({});
SelectDefault.args = {};
SelectDefault.parameters = {
  docs: {
    source: {
      format: true,
      code: `
      <>
    <SelectDefault
      id=""
      label="Label"
      name="name"
      onSelect={() => {}} // Função chamada ao selecionar uma opção
      options={[]} // Aceita um array de objetos com label e value
      placeholder="Selecione uma opção" // Texto que irá aparecer caso não tenha opção selecionada
    />
</>`,
      language: 'tsx',
      type: 'auto',
    },
  },
};

export const SelectSemOpcoes = Default.bind({});
SelectSemOpcoes.parameters = { ...SelectDefault.parameters };

SelectSemOpcoes.args = {
  options: [],
};

export const SelectComFiltrosAtivos = Default.bind({});
SelectComFiltrosAtivos.parameters = {
  docs: {
    source: {
      format: true,
      code: `
      <>
    <SelectDefault
      id=""
      label="Label"
      name="name"
      filtrosAtivos // Caso true aparece o icone de Filtros
      onSelect={() => {}} // Função chamada ao selecionar uma opção
      options={[]} // Aceita um array de objetos com label e value
      placeholder="Selecione uma opção" // Texto que irá aparecer caso não tenha opção selecionada
    />
</>`,
      language: 'tsx',
      type: 'auto',
    },
  },
};

SelectComFiltrosAtivos.args = {
  filtrosAtivos: true,
};
