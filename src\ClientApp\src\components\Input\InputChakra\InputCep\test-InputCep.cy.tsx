import cypress from 'cypress';

import { hexToRgbA } from 'store/getHexDecimalColor';

import { InputCep as Input } from './exampleInput';

const valueLabel = 'Cep';
const valueColorLabel = '#6502b2';

describe('Testing cep input', () => {
  beforeEach(() => {
    cy.mount(<Input label={valueLabel} colorLabel={valueColorLabel} />);
  });

  const valueInput = '31231233';

  it('Field formatting is being rendered', () => {
    cy.get('input[name=cep]')
      .type(valueInput, {
        delay: 0,
      })
      .invoke('val')
      .should((value) => {
        expect(String(value).includes('-')).to.eq(true);
      });
  });

  it('Input label is showing', () => {
    cy.testLabelInput(valueLabel);
  });

  it('Validating the color of the label', () => {
    cy.testColorLabel(valueLabel, hexToRgbA(valueColorLabel));
  });

  it('Render only numbers', () => {
    cy.get('input[name=cep]')
      .type(valueInput, {
        delay: 0,
      })
      .invoke('val')
      .should((value) => {
        expect(value === '').to.eq(false);
      });
  });

  it('Number of correct characters in the input', () => {
    cy.get('input[name=cep]')
      .type(valueInput, {
        delay: 0,
      })
      .invoke('val')
      .should((value) => {
        expect(value ? String(value).length : 0).to.eq(9);
      });
  });
});
