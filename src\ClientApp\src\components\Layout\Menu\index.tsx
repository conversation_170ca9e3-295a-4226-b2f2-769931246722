import { Box, Flex } from '@chakra-ui/react';
import { useState } from 'react';

import { LayouSubMenu } from './SubMenu';
import { Breadcrumbs } from '../Breadcrumb';
import { MenuLogout } from './MenuLogout';

export interface BreadcrumbInterface {
  title: string;
  path?: string;
}

type LayoutMenu = {
  children: React.ReactNode;
};

export const LayoutMenu = ({ children }: LayoutMenu) => {
  const [sizeSubMenu, setSizeSubMenu] = useState(false);

  return (
    <Flex h="100vh">
      <Box
        transition="all ease 1.2s"
        maxWidth={!sizeSubMenu ? ['50', '50px', '200px'] : '50px'}
        w="full"
      >
        <LayouSubMenu setSizeSubMenu={setSizeSubMenu} />
      </Box>
      <Box
        mt="10px"
        overflow="auto"
        pl="20px"
        pr="20px"
        w={`calc(100% - 50px)`}
      >
        <Flex
          alignItems="center"
          justifyContent="space-between"
          h="40px"
          borderBottom="1px"
          borderColor="primary.600"
          mb="20px"
          whiteSpace="nowrap"
          overflowX="auto"
        >
          <Breadcrumbs />
          <Flex>
            <MenuLogout />
          </Flex>
        </Flex>
        {children}
      </Box>
    </Flex>
  );
};
