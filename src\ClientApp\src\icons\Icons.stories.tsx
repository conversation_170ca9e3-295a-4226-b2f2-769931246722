import { As, Text, Flex, Icon, SimpleGrid } from '@chakra-ui/react';
import { Meta } from '@storybook/react';
import * as identidadeIcons from 'react-icons/fi';

import * as icons from 'icons';

export default {
  title: 'Components/Icons',
} as Meta;

export const Icons = () => {
  const iconsList = Object.entries(icons);

  const identidadeIconsList = Object.entries(identidadeIcons);
  return (
    <SimpleGrid
      overflowY="auto"
      maxH="90vh"
      templateColumns="1fr 1fr 1fr 1fr"
      rowGap="8"
      columnGap="4"
    >
      {identidadeIconsList.map((item) => {
        const [iconName, icon] = item;

        return (
          <Flex
            flexDirection="column"
            border="2px"
            pt="10px"
            borderRadius="10px"
            borderColor="primary.500"
            alignItems="center"
          >
            <Icon as={icon as As<any>} boxSize="6" />

            <Text mt="2">{iconName}</Text>
          </Flex>
        );
      })}

      {iconsList.map((item) => {
        const [iconName, icon] = item;

        return (
          <Flex
            border="2px"
            pt="10px"
            borderRadius="10px"
            borderColor="primary.500"
            flexDirection="column"
            alignItems="center"
          >
            <Icon as={icon as As<any>} boxSize="6" />

            <Text mt="2">{iconName}</Text>
          </Flex>
        );
      })}
    </SimpleGrid>
  );
};
