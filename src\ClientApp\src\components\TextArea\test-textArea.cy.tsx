import { hexToRgbA } from 'store/getHexDecimalColor';
import { getFontSize } from 'store/getFontSize';

import { TextAreaDefault as TextArea } from './exampleTextArea';

const valueLabel = 'Text Area Label';
const valueColorLabel = '#6502b2';
const fontSizeTextArea = getFontSize('sm');

describe('Testing default textArea', () => {
  beforeEach(() => {
    cy.mount(
      <TextArea
        label={valueLabel}
        colorLabel={valueColorLabel}
        id="testTextArea"
        name="testTextArea"
        fontLabel={fontSizeTextArea}
      />
    );
  });

  it('renders a label with correct color and text', () => {
    cy.testColorLabel(valueLabel, hexToRgbA(valueColorLabel));
  });

  it('displays the entered text', () => {
    cy.get('textarea')
      .type('Testando componente de TextArea')
      .should((value) => {
        expect(value).to.equal(value);
      });
  });

  it('has a font size from design system', () => {
    cy.testFontSizeLabel(valueLabel, fontSizeTextArea);
  });
});
