import { Meta, StoryFn } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';

import { theme } from 'theme';

import { InputCpfCnpj as Input } from './exampleInput';
import { InputCpfCnpjProps } from '.';

export default {
  title: 'Components/Input',
  argTypes: getThemingArgTypes(theme as any, 'Input'),
  args: {
    name: 'storybookInput',
    placeholder: 'Insira o valor aqui',
    label: 'Label',
    background: 'pink.500',
    asCpf: true,
  },
} as Meta;

export const InputCnpjCpf: StoryFn<InputCpfCnpjProps> = (props) => {
  return (
    <Input
      colorLabel={props.background}
      label={props.label}
      asCpf={props.asCnpj}
      w={props.size}
      variant={props.variant}
      placeholder={props.placeholder}
    />
  );
};
