import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

const schema = yup.object().shape({
  versao: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  exibirNotasVersao: yup.boolean(),
  possuiAtualizacaoBancoDados: yup.boolean(),
  notasVersao: yup
    .string()
    .nullable()
    .when('exibirNotasVersao', {
      is: true,
      then: yup.string().required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
    }),
});

export const formDefaultValues = {
  versao: '',
  exibirNotasVersao: false,
  notasVersao: '',
  possuiAtualizacaoBancoDados: false,
};

export type FormData = {
  versao: string;
  exibirNotasVersao: boolean;
  possuiAtualizacaoBancoDados: boolean;
  notasVersao?: string;
};

export const yupResolver = yupResolverInstance(schema);
