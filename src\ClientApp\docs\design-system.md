# Foundations

## Para estizar components ou foundations, use como base o tema padrão do Chakra

[chakra-ui/theme](https://github.com/chakra-ui/chakra-ui/tree/main/packages/theme/src)

---

## Conversões

> rem -> px (valorRem \* 16 = resultado)

> px -> rem (valorPx / 16 = resultado)

---

## Borders (border)

> Tamanho + estilo de bordas que o sistema usará

| Identificador | Conteúdo    |
| ------------- | ----------- |
| **none**      | `0`         |
| **1px**       | `1px solid` |
| **2px**       | `2px solid` |
| **4px**       | `4px solid` |
| **8px**       | `8px solid` |

---

## Breakpoints

> Breakpoints onde o sistema terá alterações de responsividade

| Identificador | Conteúdo                                |
| ------------- | --------------------------------------- |
| **sm**        | `700px`                                 |
| **md**        | `900px`                                 |
| **lg**        | `1200px`                                |
| **xl**        | `1800px`                                |
| **2xl**       | `2500px` _(Não utilizado por enquanto)_ |

---

## Font sizes (fontSize)

> Tamanhos de fontes que todo o sistema usará

| Identificador | Conteúdo             |
| ------------- | -------------------- |
| **2xs**       | `0.625rem` _(10px)_  |
| **xs**        | `0.75rem` _(12px)_   |
| **sm**        | `0.875rem` _(14px)_  |
| **md**        | `1rem` _(16px)_      |
| **lg**        | `1.125rem` _(18px)_  |
| **xl**        | `1.25rem` _(20px)_   |
| **2xl**       | `1.5rem` _(24px)_    |
| **3xl**       | `1.875rem"` _(30px)_ |
| **4xl**       | `2.25rem` _(36px)_   |
| **5xl**       | `3rem` _(48px)_      |
| **6xl**       | `3.75rem` _(60px)_   |
| **7xl**       | `4.5rem"` _(72px)_   |
| **8xl**       | `6rem` _(96px)_      |
| **9xl**       | `8rem` _(128px)_     |

---

## Font weights (fontWeight)

> Peso de fontes que todo o sistema usará

| Identificador | Conteúdo |
| ------------- | -------- |
| **hairline**  | `100`    |
| **thin**      | `200`    |
| **light**     | `300`    |
| **normal**    | `400`    |
| **medium**    | `500`    |
| **semibold**  | `600`    |
| **bold**      | `700`    |
| **extrabold** | `800`    |
| **black**     | `900`    |

---

## Line heights (lineHeight)

> Altura das linhas onde as fontes serão envolvidas, importante ressaltar que os valores são a partir do valor `em` das fontes, ou seja, a partir do tamanho da fonte o valor da lineHeight será calculado

| Identificador | Conteúdo                                                              |
| ------------- | --------------------------------------------------------------------- |
| **normal**    | `normal` _(é o valor default dos navegadores, geralmente usam 1.2em)_ |
| **none**      | `1` _em_                                                              |
| **shorter**   | `1.25` _em_                                                           |
| **short**     | `1.375` _em_                                                          |
| **base**      | `1.5` _em_                                                            |
| **tall**      | `1.625` _em_                                                          |
| **taller**    | `2` _em_                                                              |
| **3**         | `0.75rem`                                                             |
| **4**         | `1rem`                                                                |
| **5**         | `1.25rem`                                                             |
| **6**         | `1.5rem`                                                              |
| **7**         | `1.75rem`                                                             |
| **8**         | `2rem`                                                                |
| **9**         | `2.25rem`                                                             |
| **10**        | `2.5rem`                                                              |

---

## Letter spacings (letterSpacing)

> Espaçamento entre letras que todo o sistema usará

| Identificador | Conteúdo   |
| ------------- | ---------- |
| **tighter**   | `-0.05em`  |
| **tight**     | `-0.025em` |
| **normal**    | `0em`      |
| **wide**      | `0.025em`  |
| **wider**     | `0.05em`   |
| **widest**    | `0.1em`    |

---

## Radius

> Todas as curvaturas disponíveis no sistema

| Identificador | Conteúdo                                                        |
| ------------- | --------------------------------------------------------------- |
| **none**      | `0`                                                             |
| **sm**        | `0.125rem` _(2px)_                                              |
| **base**      | `0.25rem` _(4px)_                                               |
| **md**        | `0.375rem`_(6px)_                                               |
| **lg**        | `0.5rem` _(8px)_                                                |
| **xl**        | `0.75rem` _(12px)_                                              |
| **2xl**       | `1rem` _(16px)_                                                 |
| **3xl**       | `1.5rem` _(24px)_                                               |
| **full**      | `9999px` _(usado quando o componente tem as bordar "redondas")_ |

---

## Shadows

> Todas as sombras do sistema

| Identificador | Conteúdo                                                                                                |
| ------------- | ------------------------------------------------------------------------------------------------------- |
| **xs**        | `0 0 0 1px rgba(0, 0, 0, 0.05)`                                                                         |
| **sm**        | `0 1px 2px 0 rgba(0, 0, 0, 0.05)`                                                                       |
| **base**      | `0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)`                                       |
| **md**        | `0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)`                                 |
| **lg**        | `0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)`                               |
| **xl**        | `0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)`                             |
| **2xl**       | `0 25px 50px -12px rgba(0, 0, 0, 0.25)`                                                                 |
| **outline**   | `0 0 0 3px rgba(66, 153, 225, 0.6)`                                                                     |
| **inner**     | `inset 0 2px 4px 0 rgba(0,0,0,0.06)`                                                                    |
| **none**      | `none`                                                                                                  |
| **dark-lg**   | `rgba(0, 0, 0, 0.1) 0px 0px 0px 1px, rgba(0, 0, 0, 0.2) 0px 5px 10px, rgba(0, 0, 0, 0.4) 0px 15px 40px` |

---

## Sizes

> Todos os tamanhos disponíveis no sistema

| Identificador | Conteúdo            |
| ------------- | ------------------- |
| **px**        | `1px`               |
| **0.5**       | `0.125rem` _(2px)_  |
| **1**         | `0.25rem` _(4px)_   |
| **1.5**       | `0.375rem` _(6px)_  |
| **2**         | `0.5rem` _(8px)_    |
| **2.5**       | `0.625rem` _(10px)_ |
| **3**         | `0.75rem` _(12px)_  |
| **3.5**       | `0.875rem` _(14px)_ |
| **4**         | `1rem` _(16px)_     |
| **5**         | `1.25rem` _(20px)_  |
| **6**         | `1.5rem` _(24px)_   |
| **7**         | `1.75rem` _(28px)_  |
| **8**         | `2rem` _(32px)_     |
| **9**         | `2.25rem` _(36px)_  |
| **10**        | `2.5rem` _(40px)_   |
| **12**        | `3rem` _(48px)_     |
| **14**        | `3.5rem` _(56px)_   |
| **16**        | `4rem` _(64px)_     |
| **20**        | `5rem` _(80px)_     |
| **24**        | `6rem` _(96px)_     |
| **28**        | `7rem` _(112px)_    |
| **32**        | `8rem` _(128px)_    |
| **36**        | `9rem` _(144px)_    |
| **40**        | `10rem` _(160px)_   |
| **44**        | `11rem` _(176px)_   |
| **48**        | `12rem` _(192px)_   |
| **52**        | `13rem` _(208)_     |
| **56**        | `14rem` _(224px)_   |
| **60**        | `15rem` _(240px)_   |
| **64**        | `16rem` _(256px)_   |
| **72**        | `18rem` _(288px)_   |
| **80**        | `20rem` _(320px)_   |
| **96**        | `24rem` _(384px)_   |
| **max**       | `max-content`       |
| **min**       | `min-content`       |
| **full**      | `100%`              |
| **3xs**       | `14rem` _(224px)_   |
| **2xs**       | `16rem` _(256px)_   |
| **xs**        | `20rem` _(320px)_   |
| **sm**        | `24rem` _(384px)_   |
| **md**        | `28rem` _(448px)_   |
| **lg**        | `32rem` _(512px)_   |
| **xl**        | `36rem` _(576px)_   |
| **2xl**       | `42rem` _(672px)_   |
| **3xl**       | `48rem` _(768px)_   |
| **4xl**       | `56rem` _(896px)_   |
| **5xl**       | `64rem` _(1024px)_  |
| **6xl**       | `72rem` _(1152px)_  |
| **7xl**       | `80rem` _(1280px)_  |
| **8xl**       | `90rem` _(1440px)_  |

---

## zIndices

> Todos os z-index do sistema

| Identificador | Conteúdo |
| ------------- | -------- |
| **hide**      | `-1`     |
| **auto**      | `auto`   |
| **base**      | `0`      |
| **docked**    | `10`     |
| **dropdown**  | `1000`   |
| **sticky**    | `1100`   |
| **banner**    | `1200`   |
| **overlay**   | `1300`   |
| **modal**     | `1400`   |
| **popover**   | `1500`   |
| **skipLink**  | `1600`   |
| **toast**     | `1700`   |
| **tooltip**   | `1800`   |

