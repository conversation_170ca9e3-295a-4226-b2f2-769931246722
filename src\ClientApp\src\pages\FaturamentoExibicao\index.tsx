import { useCallback, useEffect, useRef, useState } from 'react';
import { Flex, GridItem, Td, Tr, Text, Tag } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';

import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { ConstanteRotas, SubstituirParametroRota } from 'constants/constantRotas';
import { moneyMask } from 'helpers/format/fieldsMasks';
import { formatDateMesAno } from 'helpers/format/formatStringDate';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';

import { ActionsMenu } from 'components/ActionsMenu';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { LoadingDefault } from 'components/Loading';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { Pagination } from 'components/Grid/Pagination';

import { FaturamentosAgrupadosProps, FaturamentoFiltroProps } from './validationForm';
import { toast } from 'react-toastify';

export const FaturamentoExibicao = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [listaFaturamentos, setListaFaturamentos] = useState<FaturamentosAgrupadosProps[]>([]);
  const [page, setPage] = useState(1);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [agrupar, setAgrupar] = useState(true);
  
  const navigate = useNavigate();

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<FaturamentosAgrupadosProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_FATURAMENTOS_EXIBICAO,
          gridPaginadaConsulta
        ),
        {
          params: {
            agrupar,
          },
        }
      );

      if (response.sucesso) {
        setTotalRegistros(response.dados.total);
        setListaFaturamentos(response.dados.registros);
      }

      setIsLoading(false);
    },
    [recarregarListagem, agrupar]
  );

  useEffect(() => {
    if (listaFaturamentos.length > 0 && agrupar) {
      setAgrupar(false);
    }
  }, [listaFaturamentos, agrupar]);


  
const handleGerarPDF = useCallback(async (mesAno: string) => {
  setIsLoading(true);
  try {
    // Make API call to generate PDF - keep the original format with slash
    const response = await api.get(
      `${ConstantEnderecoWebservice.GERAR_PDF_FATURAMENTO}?mesAno=${mesAno}`,
      { responseType: 'blob' }
    );
    

      //     const contentType = response.headers['content-type'];
      // if (contentType !== 'application/pdf') {
      //   throw new Error(`Resposta inválida: Content-Type recebido é ${contentType}`);
      // }

    // Create a blob from the PDF Stream
    const file = new Blob([response.data], { type: 'application/pdf' });
    
    // Create a link element to trigger download
    const fileURL = URL.createObjectURL(file);
    const link = document.createElement('a');
    link.href = fileURL;
    link.download = `Faturamento_${mesAno.replace('/', '_')}.pdf`;
    link.click();
    
    // Clean up
    URL.revokeObjectURL(fileURL);
  } catch (error) {
    toast.error('Erro ao gerar PDF do faturamento');
    console.error('Erro ao gerar PDF:', error);
  } finally {
    setIsLoading(false);
  }
}, []);

  return (
    <>
      {isLoading && <LoadingDefault />}
      <SimpleGridForm borderRadius="md" py="12px" />
      <Pagination
        loadColumnsData={paginationHandle}
        nPages={totalRegistros}
        currentPage={page}
        setCurrentPage={setPage}
        isLoading={isLoading}
        defaultOrderDirection="desc"
        defaultKeyOrdered="faturamentoDataEmissao"
        tableHeaders={[
          {
            content: 'Ações',
            key: 'Acoes',
            isOrderable: false,
            isNumeric: true,
            width: '0.5px',
          },
          {
            content: 'Competência',
            key: 'faturamentoDataEmissao',
            isOrderable: true,
            width: '15%',
          },
          {
            content: 'Pagamento',
            key: 'faturamentoDataVencimento',
            isOrderable: true,
            width: '15%',
          },
          {
            content: 'Assinaturas',
            key: 'assinaturaQtde',
            isOrderable: true,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '10%',
          },
          {
            content: 'SubTotal',
            key: 'faturamentoSubTotal',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '15%',
          },
          {
            content: 'Acrésc/Desc',
            key: 'faturamentoAcrescimoDesconto',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '15%',
          },
          {
            content: 'Valor',
            key: 'valor',
            isOrderable: false,
            isNumeric: true,
            sx: {
              '& div': { justifyContent: 'flex-end' },
            },
            width: '15%',
          },
          {
            content: '',
            key: 'espaco',
            isOrderable: false,
            isNumeric: true,
            width: '100%',
          },
        ]}
        renderTableRows={listaFaturamentos.map((faturamento) => (
          <Tr key={faturamento.faturamentoMesAno}>
            <Td isNumeric>
              <Flex justifyContent="flex-end">
                <ActionsMenu
                  id={`mostrarMais-${faturamento.faturamentoMesAno}`}
                  menuZIndex="modal"
                  items={[
                    {
                      content: 'Ver detalhes',
                      onClick: () => {
                        // Keep the slash format for consistency
                        const formattedMesAno = faturamento.faturamentoMesAno;
                        
                        navigate(
                          SubstituirParametroRota(
                            ConstanteRotas.FATURAMENTO_EXIBICAO_DETALHADO,
                            'mesAno',
                            formattedMesAno.replace('/', '')  // Remove slash only for URL
                          ),
                          {
                            state: {
                              revendaId: faturamento.revendaId
                            }
                          }
                        );
                      },
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_VER_DETALHES
                      ),
                    },
                    // {
                    //   content: 'Gerar PDF',
                    //   onClick: () => {
                    //     handleGerarPDF(faturamento.faturamentoMesAno);
                    //   },
                    //   possuiFuncionalidade: auth.usuarioPossuiPermissao(
                    //     ConstantFuncionalidades.FaturamentoAcao.FATURAMENTO_VER_DETALHES
                    //   ),
                    // },
                  ]}
                />
              </Flex>
            </Td>
            <Td>{formatDateMesAno(faturamento.faturamentoDataEmissao)}</Td>
            <Td>
              {faturamento.faturamentoDataVencimento ? formatDateMesAno(faturamento.faturamentoDataVencimento) : '-'}
            </Td>
            <Td isNumeric>
              {faturamento.assinaturaQtde}
            </Td>
            <Td isNumeric>
              {moneyMask(faturamento.faturamentoSubTotal || 0, true)}
            </Td>
            <Td isNumeric>
              {moneyMask(faturamento.faturamentoValorAcrescimoDesconto || 0, true)}
            </Td>
            <Td w="15%" isNumeric>
              {moneyMask(faturamento.faturamentoTotalRepasse, true)}
            </Td>
            <Td w="100%" />
          </Tr>
        ))}
      />
    </>
  );
};
