import { ChakraProvider } from '@chakra-ui/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import { RoutesContent } from 'routes/index.routes';
import 'react-toastify/dist/ReactToastify.css';
import 'react-date-range/dist/styles.css'; // main css file
import 'react-date-range/dist/theme/default.css';

import { theme } from 'theme';
import FullScreenProvider from 'store/FullScreen';
import LayoutContextProvider from 'store/Layout';

function App() {
  return (
    <ChakraProvider theme={theme}>
      <FullScreenProvider>
        <LayoutContextProvider>
          <BrowserRouter>
            <RoutesContent />
          </BrowserRouter>
        </LayoutContextProvider>
      </FullScreenProvider>
      <ToastContainer />
    </ChakraProvider>    
  );
  
}

export default App;
