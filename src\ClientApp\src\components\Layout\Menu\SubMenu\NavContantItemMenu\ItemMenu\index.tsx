import { Box, Flex } from '@chakra-ui/react';

import { useLayoutContext } from 'store/Layout';

import { ContentItemMenu } from '../../ContentItemMenu';

type ItemMenuProps = {
  animation: boolean;
};

export const ItemMenu = ({ animation }: ItemMenuProps) => {
  const { valueItemMenu, isOpenMenu } = useLayoutContext();
  return (
    <Box
      left={animation ? '200px' : '50px'}
      bg="gray.200"
      display={isOpenMenu ? 'initial' : 'none'}
      h="full"
      transition="all ease 1.5s"
      w="200px"
      pt="20px"
      zIndex="9999"
      maxWidth="200px"
      boxShadow="7px 0px 6px 0px rgba(0, 0, 0, 0.2) !important"
      position="absolute"
    >
      {valueItemMenu.map(
        (
          {
            possuiFuncionalidade,
            title,
            colorHover,
            onClick,
            keyMenu,
            isSeparator,
          },
          index
        ) =>
          isSeparator ? (
            <Flex
              alignItems="center"
              justifyContent="flex-start"
              padding="0 25px 0 15px"
              margin="7px 0"
              userSelect="none"
              cursor="default"
              _first={{ marginTop: '15px' }}
              _last={{ marginBottom: '15px' }}
            >
              <Box bg="gray.400" height="1px" width="100%" />
            </Flex>
          ) : (
            possuiFuncionalidade && (
              <ContentItemMenu
                key={index}
                keyMenu={keyMenu}
                background="none"
                colorHover={colorHover}
                handlePushNavigation={onClick}
                possuiFuncionalidade={true}
              >
                {title}
              </ContentItemMenu>
            )
          )
      )}
    </Box>
  );
};
