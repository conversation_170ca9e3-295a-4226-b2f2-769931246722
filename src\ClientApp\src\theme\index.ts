import { extendTheme } from '@chakra-ui/react';

import colors from './foundations/colors';
import fonts from './foundations/fonts';
import fontSizes from './foundations/fontSizes';
import Button from './components/Button';
import Input from './components/Input';
import { Form } from './components/Form';
import Table from './components/Table';
import Menu from './components/Menu';
import Textarea from './components/TextArea';
import boxShadow from './foundations/boxShadow';
import Accordion from './components/Accordion';

export const theme = extendTheme({
  config: {
    cssVarPrefix: 'zendar-ck',
    initialColorMode: 'light',
    useSystemColorMode: false,
  },
  variant: {
    options: ['solid', 'outlinePill', 'outline', 'link'],
    control: { type: 'radio' },
  },
  size: {
    options: ['xs', 'xl', 'sm', 'md', 'lg'],
    control: { type: 'radio' },
  },
  shadows: boxShadow,
  colorScheme: {
    options: [
      'whiteAlpha',
      'primary',
      'secondary',
      'blackAlpha',
      'aquamarine',
      'gray',
      'red',
      'orange',
      'yellow',
      'green',
      'blue',
      'purple',
      'pink',
    ],
    control: { type: 'select' },
  },
  colors,
  fonts,
  fontSizes,
  components: {
    Button,
    Accordion,
    Input,
    Form,
    Table,
    Menu,
    Textarea,
  },
});
