import { FormData } from './validationForm';

export const videoAdapter = (data: FormData) => {
  return {
    ...data,
    planos: [data.planos?.map((planoItem) => planoItem.label).join(',')],
    sistemas: [
      data.sistemas?.map((sistemaItem) => sistemaItem.label).join(','),
    ],
    telas: [data.telas?.map((telaItem) => telaItem.value).join(',')],
    temaId: data.tema,
    sequenciaOrdenacao: data.sequenciaOrdenacao,
    ...(data.categoriaTreinamento && {
      categoriaTreinamentoId: data.categoriaTreinamento,
    }),
  };
};
