# Informações coletadas sobre errors e warnings comuns.

## Warning: Can't perform a React state update on an unmounted component. This is a no-op, but it indicates a memory leak in your application. To fix, cancel all subscriptions and asynchronous tasks in a useEffect cleanup function.

### Qual a causa do warning?

Este warning alerta um "esforço desnecessário" da aplicação, pois ela está tentando alterar um state que pertence a um componentes desmontado.

### Como resolver?

Para resolver este warning, temos que testar se o componente está montado ou não sempre que ele tiver possibilidade de alterar um state após ser desmontado.
Assim como o próprio warning sugere, resolveremos ele utilizando um useEffect que ouvirá os eventos de montagem e desmontagem do componente para retornar essa informação a partir de uma ref. Teremos acesso a essa ref a partir do hook [useIsMountedRef](../src/helpers/layout/useIsMountedRef).

### Exemplo

```
if (response.sucesso) {
  setExemplo(response.dados);

  navigate('/');
}

setIsLoading(false);
```

Neste exemplo, o componente é desmontado caso a requisição retornar sucesso, mas mesmo se desmontado altera o state "isLoading" para false, e isso gera o warning.
Para resolver esse exemplo iremos uma criar uma instância do hook [useIsMountedRef](../src/helpers/layout/useIsMountedRef) (dentro do componente e não da função).

```
const isMountedRef = useIsMountedRef();
```

E testaremos se o componente está montado antes de setar o state.

```
if (response.sucesso) {
  setExemplo(response.dados);

  navigate(
      SubstituirParametroRota(ConstanteRotas.ROTA_EXEMPLO, 'id?', exemploId)
    );
}

if (isMountedRef.current) setIsLoading(false);
```

Ou

```
if (response.sucesso) {
  setExemplo(response.dados);

  navigate(ConstanteRotas.ROTA_EXEMPLO);
}

if (isMountedRef.current) setIsLoading(false);

```

Isso fará com que o componente não faça a alteração do state se o componente estiver desmontado, e resolverá o warning.
