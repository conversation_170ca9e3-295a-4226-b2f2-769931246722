import { useCallback, useState, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';
import api, { ResponseApi } from 'services/api';
import EnumServicosNfceQrCode from 'constants/Enum/enumServicoNfceQrCode';
import { EnumEstados } from 'constants/Enum/enumEstados';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormNfceQrCode } from '..';
import { FormData, formDefaultValues, yupResolver } from '../validationForm';
import { useEffectDefault } from 'hook/useEffectDefault';

export type NfceQrCodeResponse = {
  servico: number;
  urlProducao: string;
  urlHomologacao: string;
  estadoCodigo: number;
  versao: string;
};

export const AlterarNfceQrCode = () => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, reset } = formMethods;

  const navigate = useNavigate();
  const idRouter = useParams();
  const { id: idRota } = idRouter;

  const getNfce = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<NfceQrCodeResponse>>(
      ConstantEnderecoWebservice.OBTER_NFCE_QRCODE,
      {
        params: { id: idRota },
      }
    );

    if (response.sucesso) {
      const { dados } = response;

      reset({
        ...dados,
        servico: EnumServicosNfceQrCode.properties.find(
          (servico) => servico.value === dados.servico
        ),
        estadoCodigo: EnumEstados.properties.find(
          (estado) => estado.value === dados.estadoCodigo
        ),
      });
    } else {
      navigate(ConstanteRotas.NFCE_QRCODE);
    }

    setIsLoading(false);
  }, [reset, idRota]);

  const handleAlterarNfce = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await api.put<void, ResponseApi>(
      ConstantEnderecoWebservice.ALTERAR_NFCE_QRCODE,
      {
        ...data,
        id: idRota,
        servico: data.servico?.value,
        estadoCodigo: data?.estadoCodigo?.value,
      }
    );

    if (response.sucesso) {
      toast.success('Cadastro alterado com sucesso.');
      navigate(ConstanteRotas.NFCE_QRCODE);
    }

    setIsLoading(false);
  });

  useEffectDefault(() => {
    getNfce();
  }, [getNfce]);

  return (
    <LayoutFormPage onSubmit={() => handleAlterarNfce()} isLoading={isLoading}>
      <FormProvider {...formMethods}>
        <FormNfceQrCode />
      </FormProvider>
    </LayoutFormPage>
  );
};
