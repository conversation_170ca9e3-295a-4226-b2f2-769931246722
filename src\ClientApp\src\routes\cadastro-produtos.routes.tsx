import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { Produtos } from 'pages/Produtos/Listar';
import { CadastrarProdutos } from 'pages/Produtos/Formulario/Cadastrar';
import { AlterarProduto } from 'pages/Produtos/Formulario/Alterar';

import LayoutGuard from './LayoutGuard';

export const ProdutosRoutes = [
  <Route
    key={ConstanteRotas.PRODUTOS}
    path={ConstanteRotas.PRODUTOS}
    element={
      <LayoutGuard
        key={ConstanteRotas.PRODUTOS}
        breadcrumb={[
          { title: 'Cadastro' },
          {
            title: 'Produtos',
          },
        ]}
        component={<Produtos />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_CADASTRO_PRODUTO)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.PRODUTOS_CADASTRAR}
    path={ConstanteRotas.PRODUTOS_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.PRODUTOS_CADASTRAR}
        breadcrumb={[
          { title: 'Cadastro' },
          {
            title: 'Produtos',
            path: ConstanteRotas.PRODUTOS,
          },
          {
            title: 'Cadastrar',
          },
        ]}
        component={<CadastrarProdutos />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroProdutoAcao.CADASTRAR_PRODUTOS)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.PRODUTOS_ALTERAR}
    path={ConstanteRotas.PRODUTOS_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.PRODUTOS_ALTERAR}
        breadcrumb={[
          { title: 'Cadastro' },
          {
            title: 'Produtos',
            path: ConstanteRotas.PRODUTOS,
          },
          {
            title: 'Alterar',
          },
        ]}
        component={<AlterarProduto />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.CadastroProdutoAcao.ALTERAR_PRODUTOS)}
      />
    }
  />,
];
