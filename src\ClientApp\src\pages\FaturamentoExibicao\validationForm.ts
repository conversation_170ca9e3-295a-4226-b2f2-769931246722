export interface FaturamentoExibicaoProps {
  revendaId: string;
  revendaFantasia: string;
  contaClienteId: string;
  contaClienteDominio: string;
  assinaturaId: string;
  assinaturaFantasia: string;
  assinaturaRazaoSocial: string;
  assinaturaCodigoExterno: string;
  assinaturaCodigoERP: number;
  faturamentoId: string;
  faturamentoDataEmissao: string;
  faturamentoDataVencimento: string;
  faturamentoTotalRepasse: number;
  faturamentoSubTotal: number;
  faturamentoValorAcrescimoDesconto: number;
  faturamentoMotivoAcrescimoDesconto?: string;
  faturamentoMesAno: string;
  itens: FaturamentoItemExibicaoProps[];
}

export interface FaturamentoItemExibicaoProps {
  faturamentoItemId: string;
  servicoId: string;
  servicoNome: string;
  quantidade: number;
  valorRepasse: number;
  valorUnitarioRepasse: number;
}

export interface FaturamentosAgrupadosProps {
  revendaId: string;
  revendaFantasia: string;
  assinaturaQtde: number;
  faturamentoTotalRepasse: number;
  faturamentoSubTotal: number;
  faturamentoValorAcrescimoDesconto: number;
  faturamentoDataEmissao: string; 
  faturamentoDataVencimento: string;
  faturamentoMesAno: string;
}

export interface FaturamentoFiltroProps {
  revendaFantasia: string;
  revendaId: string;
}
