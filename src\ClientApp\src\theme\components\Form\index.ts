const activeLabelStyles = {
  transform: 'scale(0.85) translateY(-24px)',
};

export const Form = {
  baseStyle: {
    requiredIndicator: {
      color: 'black',
    },
  },
  variants: {
    floating: {
      container: {
        _focusWithin: {
          label: {
            ...activeLabelStyles,
            color: 'defaultColor',
            fontSize: 'sm',
          },
        },
        'input:not(:placeholder-shown) + label, .chakra-select__wrapper + label, textarea:not(:placeholder-shown) ~ label':
          {
            ...activeLabelStyles,
          },

        label: {
          top: 0,
          left: 0,
          zIndex: 2,
          position: 'absolute',
          backgroundColor: 'none',
          pointerEvents: 'none',
          my: 0,
          transformOrigin: 'left top',
          fontSize: 'sm',
        },
      },
    },
  },
};
