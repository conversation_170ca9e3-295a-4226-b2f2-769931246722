import { Meta, StoryFn } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';

import { theme } from 'theme';

import { InputDefault as Input } from './exampleInput';
import { InputDefaultProps } from '.';

export default {
  title: 'Components/Input',
  component: Input,
  argTypes: getThemingArgTypes(theme as any, 'Input'),
  args: {
    name: 'storybookInput',
    placeholder: 'Insira o valor aqui',
    label: 'Label',
    background: 'pink.500',
    blockSpecialCharacters: false,
  },
} as Meta;

export const InputDefault: StoryFn<InputDefaultProps> = (props) => {
  return (
    <Input
      colorLabel={props.background}
      label={props.label}
      blockSpecialCharacters={props.blockSpecialCharacters}
      w={props.size}
      variant={props.variant}
      placeholder={props.placeholder}
    />
  );
};
