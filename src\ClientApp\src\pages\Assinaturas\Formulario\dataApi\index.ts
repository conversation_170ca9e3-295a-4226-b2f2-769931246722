import { CepValue } from 'components/Input/InputChakra/InputCep';
import { SelectOptions } from 'components/Select/SelectDefault';

import { FormData, ServicosAdicionais } from '../validationForms';

export const dadosApiAssinatura = (params: {
  data: FormData;
  servicos: ServicosAdicionais[];
  naoExibirResponsavel: boolean;
  id?: string;
  servicosExcluidos?: string[];
}) => {
  const { data, servicos, naoExibirResponsavel, id, servicosExcluidos } =
    params;

  let dadosAtuais;

  const {
    codigoExterno,
    observacao,
    cnpj,
    razaoSocial,
    fantasia,
    inscricaoEstadual,
    inscricaoMunicipal,
    cep,
    logradouro,
    numero,
    complemento,
    bairro,
    telefone,
    celular,
    emailContato,
    dominio,
    urlDominio,
    telefoneResponsavel,
    email,
    nome,
    paisNome,
    usuario,
    tabelaPrecoId,
    cidadeNome,
    contato,
    bancoDados,
    codigoERP,
    diaVencimento,
    dataExpiracao,
  } = data;

  const telefoneResponsavelValue = telefoneResponsavel.replace(/\s/g, '');
  const celularValue = celular.replace(/\s/g, '');
  const telefoneValue = telefone.replace(/\s/g, '');
  const paisNomeValue = paisNome as SelectOptions;
  const cidadeNomeValue = cidadeNome as SelectOptions;

  const cepValue = cep as CepValue;
  const cepIsString = cep as string;

  // Ensure dataExpiracao is properly formatted for the API
  const formatDataExpiracao = () => {
    if (!dataExpiracao) return null;
    if (dataExpiracao instanceof Date) {
      if (isNaN(dataExpiracao.getTime())) return null;
      return dataExpiracao.toISOString();
    }
    return dataExpiracao;
  };

  const valueForm = {
    codigoExterno: codigoExterno,
    observacao: observacao,
    diaVencimento: diaVencimento,
    dataExpiracao: formatDataExpiracao(),
    loja: {
      cnpj,
      razaoSocial,
      contato,
      fantasia,
      inscricaoEstadual,
      inscricaoMunicipal,
      cep: cepValue?.value
        ? cepValue?.value.replace('.', '')
        : cepIsString?.replace('.', ''),
      logradouro,
      numero,
      complemento,
      bairro,
      cidadeId: cidadeNomeValue?.value,
      cidadeNome: cidadeNomeValue?.label,
      paisId: paisNomeValue?.value,
      paisNome: paisNomeValue?.label,
      telefone: telefoneValue,
      celular: celularValue,
      emailContato,
      codigoERP: codigoERP || 0,
    },
    tabelaPrecoId,
    dadosFaturamento: null,
    servicos: servicos.map(
      ({
        servicoId,
        tipo,
        quantidade,
        valorTotalRepasse,
        valorUnitarioRepasse,
        descricaoAdicional, 
      }) => ({
        servicoId: servicoId,
        tipo: tipo,
        quantidade: quantidade,
        valorTotalRepasse: valorTotalRepasse,
        valorUnitarioRepasse: valorUnitarioRepasse,
        descricaoAdicional: descricaoAdicional,
      })
    ),
  };

  if (id && servicosExcluidos) {
    dadosAtuais = {
      ...valueForm,
      id,
      servicosExcluidos: servicosExcluidos,
    };
  } else if (naoExibirResponsavel && !id) {
    dadosAtuais = {
      ...valueForm,
    };
  } else {
    dadosAtuais = {
      ...valueForm,
      usuarioResponsavel: usuario,
      responsavel: {
        nome: nome,
        bancoDados: bancoDados,
        email: email,
        celular: telefoneResponsavelValue,
        dominio: `${dominio}.${urlDominio}`,
      },
    };
  }
  return dadosAtuais;
};
