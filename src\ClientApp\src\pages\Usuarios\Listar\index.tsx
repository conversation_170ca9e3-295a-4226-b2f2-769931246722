import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FiSearch } from 'react-icons/fi';
import { Box, Flex, Td, Tr, Icon, Link } from '@chakra-ui/react';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { EnumStatusCadastros } from 'constants/Enum/enumStatusCadastros';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import api, { ResponseApi } from 'services/api';
import { EnumTipoUsuario } from 'constants/Enum/enumTipoUsuario';
import auth from 'modules/auth';

import { ActionsMenu } from 'components/ActionsMenu';
import { ButtonDefault } from 'components/Button';
import { ContainerListagem } from 'components/Grid/GridList';
import { Pagination } from 'components/Grid/Pagination';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SelectDefault } from 'components/Select/SelectDefault';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';

type UsuarioProps = {
  id: string;
  ativo: boolean;
  perfil: number;
  nome: string;
  revendaFantasia: string;
};

type FormData = {
  nome: string;
  ativo: boolean | null;
};

export const ListarUsuarios = () => {
  const [listaUsuarios, setListaUsuarios] = useState<UsuarioProps[]>([]);
  const [currentFilters, setCurrentFilters] = useState<FormData>(
    {} as FormData
  );
  const [page, setPage] = useState(1);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  const formMethods = useForm<FormData>({
    defaultValues: {
      ativo: EnumStatusCadastros.TODOS.value,
      nome: '',
    },
  });

  const { watch } = formMethods;

  const nomeWatch = watch('nome');

  const acessoAcoes = auth.usuarioPossuiPermissao(
    Array.from(
      new Set(
        [
          ConstantFuncionalidades.CadastroUsuarioAcao.USUARIO_ALTERAR,
          ConstantFuncionalidades.CadastroUsuarioAcao.USUARIO_ALTERAR,
        ].flatMap((item) => item)
      )
    )
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<UsuarioProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_USUARIOS,
          gridPaginadaConsulta
        ),
        {
          params: currentFilters,
        }
      );

      if (response.sucesso) {
        setTotalRegistros(response.dados.total);
        setListaUsuarios(response.dados.registros);
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const handleExcluirUsuario = useCallback(
    async (produtoId: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'A ação vai excluir o usuário!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            ConstantEnderecoWebservice.EXCLUIR_USUARIO,
            { params: { produtoId } }
          );

          if (response.sucesso) {
            setRecarregarListagem(!recarregarListagem);
            toast.success('Cadastro excluído com sucesso.');
            return true;
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );
  const handleAlterarUsuario = useCallback(
    (usuarioId: string) => {
      if (
        !auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.CadastroUsuarioAcao.USUARIO_ALTERAR
        )
      )
        return;
      navigate(
        SubstituirParametroRota(
          ConstanteRotas.USUARIOS_ALTERAR,
          'id',
          usuarioId
        )
      );
    },
    [
      auth.usuarioPossuiPermissao(
        ConstantFuncionalidades.CadastroUsuarioAcao.USUARIO_ALTERAR
      ),
    ]
  );

  const getLinkAlterar = useCallback(
    (usuarioId: string) => {
      let href = '';
      if (
        !auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.CadastroUsuarioAcao.USUARIO_ALTERAR
        )
      ) {
        href = '';
      } else {
        href = SubstituirParametroRota(
          ConstanteRotas.USUARIOS_ALTERAR,
          'id',
          usuarioId
        );
      }

      return href;
    },
    [
      auth.usuarioPossuiPermissao(
        ConstantFuncionalidades.CadastroUsuarioAcao.USUARIO_ALTERAR
      ),
    ]
  );

  return (
    <Box>
      <FormProvider {...formMethods}>
        <ContainerListagem
          inputPesquisa={
            <InputDefault
              maxLength={100}
              placeholder="Buscar por nome"
              iconLeftElement={FiSearch}
              name="nome"
              onEnterKeyPress={() => {
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  nome: nomeWatch,
                }));
              }}
            />
          }
          filtrosListagem={
            <SelectDefault
              name="ativo"
              placeholder="Selecione um filtro"
              filtrosAtivos
              asControlledByObject={false}
              onSelect={(optionSelecionada) =>
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  ativo: optionSelecionada?.value,
                }))
              }
              options={EnumStatusCadastros.properties.map((status) => status)}
            />
          }
          buttonCadastrar={
            <ButtonDefault
              onClick={() => navigate(ConstanteRotas.USUARIOS_CADASTRAR)}
              width={['full', 'full', 'full', '220px']}
              color="white"
              colorScheme="secondary"
              leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
              possuiFuncionalidade={auth.usuarioPossuiPermissao(
                ConstantFuncionalidades.CadastroUsuarioAcao.USUARIO_CADASTRAR
              )}
            >
              Cadastrar novo
            </ButtonDefault>
          }
        >
          <Pagination
            loadColumnsData={paginationHandle}
            nPages={totalRegistros}
            currentPage={page}
            setCurrentPage={setPage}
            isLoading={isLoading}
            defaultOrderDirection="asc"
            defaultKeyOrdered="nome"
            tableHeaders={[
              {
                content: <StatusCircle hasValue={false} />,
                key: 'ativo',
                isOrderable: false,
                width: '1px',
              },
              {
                content: 'Nome',
                key: 'nome',
                isOrderable: true,
                width: 'auto',
              },
              {
                content: 'Perfil',
                key: 'perfil',
                width: 'auto',
              },
              {
                content: 'Revenda',
                key: 'revendaFantasia',
                width: 'auto',
              },
              {
                content: 'Ações',
                key: 'Acoes',
                isOrderable: false,
                isNumeric: true,
                width: '10px',
              },
            ]}
            renderTableRows={listaUsuarios.map((usuario) => (
              <Tr key={usuario.id}>
                <Td>
                  <StatusCircle isActive={usuario.ativo} />
                </Td>
                <Td pb="3px" pt="3px">
                  <Flex w="full">
                    <Link
                      lineHeight="12.5px"
                      maxW="full"
                      whiteSpace="pre-line"
                      cursor="pointer"
                      href={getLinkAlterar(usuario.id)}
                    >
                      {usuario.nome}
                    </Link>
                  </Flex>
                </Td>
                <Td>
                  {EnumTipoUsuario.cadastroUsuario.find(
                    (enumUsuario) => enumUsuario.value === usuario.perfil
                  )?.label || '---'}
                </Td>
                <Td width={60}>
                  <Flex maxW="80px">{usuario.revendaFantasia}</Flex>
                </Td>

                {acessoAcoes && (
                  <Td>
                    <Flex justifyContent="right">
                      <ActionsMenu
                        id="usuarioAcoes"
                        items={[
                          {
                            content: 'Editar',
                            onClick: () => handleAlterarUsuario(usuario.id),
                            possuiFuncionalidade: auth.usuarioPossuiPermissao(
                              ConstantFuncionalidades.CadastroUsuarioAcao
                                .USUARIO_ALTERAR
                            ),
                          },
                          {
                            content: 'Excluir',
                            onClick: () => handleExcluirUsuario(usuario.id),
                            possuiFuncionalidade: auth.usuarioPossuiPermissao(
                              ConstantFuncionalidades.CadastroUsuarioAcao
                                .USUARIO_EXCLUIR
                            ),
                          },
                        ]}
                      />
                    </Flex>
                  </Td>
                )}
              </Tr>
            ))}
          />
        </ContainerListagem>
      </FormProvider>
    </Box>
  );
};
