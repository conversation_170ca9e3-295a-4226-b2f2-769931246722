import { Meta, StoryFn } from '@storybook/react';
import { Box, Icon } from '@chakra-ui/react';
import { IoIosAddCircleOutline } from 'react-icons/io';

import { ButtonDefault as Button, ButtonDefaultProps } from '.';
import { theme } from '../../theme/index';

export default {
  title: 'Components/Button',
  argTypes: {
    ...theme,
  },
  args: {
    colorScheme: 'green',
    children: 'Cadastrar novo',
    variant: 'solid',
    size: 'lg',
    leftIcon: true,
    isLoading: false,
    isDisabled: false,
  },
} as Meta;

export const ButtonDefault: StoryFn<ButtonDefaultProps> = (props) => {
  return (
    <Box w="180px">
      <Button
        size={props.size}
        rightIcon={props.leftIcon ? <Icon as={IoIosAddCircleOutline} /> : <></>}
        colorScheme={props.colorScheme}
        {...props}
      >
        {props.children || 'Cadastrar novo'}
      </Button>
    </Box>
  );
};
