import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';
import jsonp from 'jsonp';

export interface CepResponse {
  bairro: string;
  complemento: string;
  logradouro: string;
  uf: string;
  localidade: string;
  estado: string;
  ibge: string | number;
  erro: string;
}

const consultarViaCep = (
  cep: string,
  getData: (sucesso: boolean, dados: CepResponse | string) => void
) => {
  jsonp(
    `https://viacep.com.br/ws/${cep.replace(/\D/g, '')}/json/`,
    { timeout: 5000 },
    (err: Error | null, data: CepResponse) => {
      if (err || !data) getData(false, EnumValidacoesSistema.CEP_ERRO_CONSULTA);
      else if (data && data.erro)
        getData(false, EnumValidacoesSistema.CEP_INVALIDO);
      else if (data) getData(true, data);
    }
  );
};

export default consultarViaCep;
