import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { PlanoEnum } from 'constants/Enum/enumPlano';
import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { enumTelasExibicao } from 'constants/Enum/enumTelasExibicao';
import { enumSistemas } from 'constants/Enum/enumSistemas';
import { formatEnumTelasExibicao } from 'helpers/format/formatEnum';

import { LayoutFormPage } from 'components/Layout/FormPage';

import FormArtigo from '..';
import { formDefaultValues, yupResolver, FormData } from '../validationForm';

type RegrasExibicao = {
  planos: string;
  sistemas: string;
  telas: string;
};

const AlterarArtigo = () => {
  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, reset } = formMethods;

  const { id: idRota } = useParams();
  const navigate = useNavigate();

  const planos = PlanoEnum.options;
  const telas = formatEnumTelasExibicao(enumTelasExibicao);
  const sistemas = enumSistemas.options;

  const [isLoading, setIsLoading] = useState(false);

  const buscarArtigo = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<
      void,
      ResponseApi<FormData & RegrasExibicao>
    >(`${ConstantEnderecoWebservice.OBTER_ARTIGO}/${idRota}`);

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso && response.dados) {
        const { dados } = response;

        const telasExibidas = telas.filter((telaItem) =>
          dados.telas?.includes(telaItem.value.toString())
        );

        const sistemasExibidos = sistemas.filter((sistemaItem) =>
          dados.sistemas?.includes(sistemaItem.label)
        );

        reset({
          ...dados,
          planos: planos.filter((planoItem) =>
            dados.planos?.includes(planoItem.label)
          ),
          sistemas: sistemasExibidos,
          telas: telasExibidas,
        });
      }
    }
    setIsLoading(false);
  }, [idRota]);

  const submitAlterar = useCallback(async (data: FormData) => {
    const response = await api.put<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.ALTERAR_VIDEO,
      {
        ...data,
        id: idRota,
        planos: data.planos?.map((planoItem) => planoItem.label).join(','),
        sistemas: data.sistemas
          ?.map((sistemaItem) => sistemaItem.label)
          .join(','),
        telas: data.telas?.map((telaItem) => telaItem.value).join(','),
      }
    );

    return response;
  }, []);

  const handleAlterar = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await submitAlterar(data);

    if (response) {
      if (response.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }
      if (response.sucesso) {
        toast.success('Cadastro alterado com sucesso');
        navigate(ConstanteRotas.ARTIGOS);
      }
    }
    setIsLoading(false);
  });

  useEffect(() => {
    buscarArtigo();
  }, [buscarArtigo]);

  return (
    <LayoutFormPage onSubmit={() => handleAlterar()} isLoading={isLoading}>
      <FormProvider {...formMethods}>
        <FormArtigo />
      </FormProvider>
    </LayoutFormPage>
  );
};

export default AlterarArtigo;
