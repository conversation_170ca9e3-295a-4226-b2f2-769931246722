import { useCallback, useState } from 'react';
import { Flex, Td, Tr } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { IoIosAddCircleOutline } from 'react-icons/io';

import api, { ResponseApi } from 'services/api';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import { formatUTCToLocateDateTime } from 'helpers/format/formatUTCToLocateDateTime';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import auth from 'modules/auth';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';

import { ButtonDefault } from 'components/Button';
import { Pagination } from 'components/Grid/Pagination';
import { ActionsMenu } from 'components/ActionsMenu';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { ModalCadastrarAtualizacao } from 'components/Modal/ModalCadastrarAtualizacao';
import { ModalAlterarAtualizacao } from 'components/Modal/ModalAlterarAtualizacao';

interface AtualizacaoSistemaProps {
  id: string;
  versao: string;
  exibirNotasVersao: boolean;
  possuiAtualizacaoBancoDados: boolean;
  dataHoraCadastro: string;
}

export const Atualizacao = () => {
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [atualizacaoSistema, setAtualizacaoSistema] = useState<
    AtualizacaoSistemaProps[]
  >([]);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);

  const navigate = useNavigate();

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<AtualizacaoSistemaProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_ATUALIZACOES,
          gridPaginadaConsulta
        )
      );

      if (response.sucesso) {
        setTotalRegistros(response.dados.total);
        setAtualizacaoSistema(response.dados.registros);
      }

      setIsLoading(false);
    },
    [recarregarListagem]
  );

  const handleExcluirAtualizacao = useCallback(
    async (id: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'A ação irá excluir a atualização!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            ConstantEnderecoWebservice.EXCLUIR_ATUALIZACAO,
            { params: { id } }
          );

          if (response.sucesso) {
            setRecarregarListagem(!recarregarListagem);
            toast.success('Cadastro excluído com sucesso.');
            return true;
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );

  const atualizarListagem = useCallback(() => {
    setRecarregarListagem(!recarregarListagem);
  }, [recarregarListagem]);

  return (
    <Flex direction="column">
      <ButtonDefault
        onClick={() =>
          ModalCadastrarAtualizacao({
            atualizarListagem: atualizarListagem,
          })
        }
        width={['full', 'full', 'full', '56']}
        isLoading={isLoading}
        color="white"
        colorScheme="secondary"
        leftIcon={<IoIosAddCircleOutline />}
        alignSelf="flex-end"
        mb={['0', '17px']}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AtualizacaoAcao.CADASTRAR_ATUALIZACOES
        )}
      >
        Cadastrar novo
      </ButtonDefault>
      <Pagination
        loadColumnsData={paginationHandle}
        nPages={totalRegistros}
        currentPage={page}
        setCurrentPage={setPage}
        isLoading={isLoading}
        defaultKeyOrdered="DataHoraCadastro"
        defaultOrderDirection="desc"
        tableHeaders={[
          {
            content: 'Data',
            key: 'DataHoraCadastro',
            isOrderable: true,
            width: 'auto',
          },
          {
            content: 'Versão',
            key: 'Versao',
            isOrderable: false,
            width: 'auto',
          },
          {
            content: 'Possui atualização de banco?',
            key: 'PossuiAtualizacaoBancoDados',
            isOrderable: false,
            width: 'auto',
          },
          {
            content: 'Exibir notas da versão?',
            key: 'ExibirNotasVersao',
            isOrderable: false,
            width: 'auto',
          },
          {
            content: 'Ações',
            key: 'Acoes',
            isOrderable: false,
            isNumeric: true,
            width: '38px',
          },
        ]}
        renderTableRows={atualizacaoSistema.map((atualizacao) => (
          <Tr key={atualizacao.id}>
            <Td>{formatUTCToLocateDateTime(atualizacao.dataHoraCadastro)}</Td>
            <Td>{atualizacao.versao}</Td>
            <Td>{atualizacao.possuiAtualizacaoBancoDados ? 'Sim' : 'Não'}</Td>
            <Td>{atualizacao.exibirNotasVersao ? 'Sim' : 'Não'}</Td>
            <Td isNumeric>
              <Flex justifyContent="right">
                <ActionsMenu
                  id="mostrarMais"
                  items={[
                    {
                      content: 'Alterar',
                      onClick: () => {
                        ModalAlterarAtualizacao({
                          atualizarListagem: atualizarListagem,
                          id: atualizacao.id,
                        });
                      },
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.AtualizacaoAcao
                          .ALTERAR_ATUALIZACOES
                      ),
                    },

                    {
                      content: 'Gerenciar',
                      onClick: () => {
                        navigate(
                          SubstituirParametroRota(
                            ConstanteRotas.ATUALIZACAO_GERENCIAR,
                            'id',
                            atualizacao.id
                          )
                        );
                      },
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.AtualizacaoAcao
                          .VISUALIZAR_GERENCIAR_ATUALIZACAO
                      ),
                    },
                    {
                      content: 'Remover',
                      onClick: () => {
                        handleExcluirAtualizacao(atualizacao.id);
                      },
                      possuiFuncionalidade: auth.usuarioPossuiPermissao(
                        ConstantFuncionalidades.AtualizacaoAcao
                          .EXCLUIR_ATUALIZACOES
                      ),
                    },
                  ]}
                />
              </Flex>
            </Td>
          </Tr>
        ))}
      />
    </Flex>
  );
};
