import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import auth from 'modules/auth';

import api, { ResponseApi } from 'services/api';
import { useAssinaturasContext } from 'store/Assinaturas/AssinaturaContext';
import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { getClienteObterResponsavel } from 'helpers/functions/getClienteObterResponsavel';
import { getDominioFromURL } from 'helpers/format/getDominioFromURL';

import { LayoutFormPage } from 'components/Layout/FormPage';
import { ButtonDefault } from 'components/Button';
import { ModalCancelar } from 'components/Modal/ModalCancelar';
import { DominioProps } from 'pages/Assinaturas/types';
import { ModalRemoverDominio } from 'pages/Assinaturas/components/ModalRemoverDominio';

import { FormAssinaturas } from '..';
import { dadosApiAssinatura } from '../dataApi';
import {
  AssinaturaProps,
  FormData,
  yupResolverAlterar,
  yupResolverLojaAlterar,
} from '../validationForms';
import { useAssinaturaFormulario } from '../hook';

export const AlterarAssinaturas = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [listServicoRemovidos, setListServicosRemovidos] = useState<string[]>(
    []
  );

  const { opcaoPadraoDominioDelivery } = useAssinaturaFormulario();

  const {
    isUpdateFormDominio,
    setListServicosAdicionais,
    listServicosAdicionais,
    isPlanoAdicionado,
    dadosCancelamento,
    setDadosCancelamento,
    existeDominioDelivery,
    setExisteDominioDelivery,
    setListServicosContratados,
  } = useAssinaturasContext();

  const idRouter = useParams();
  const navigate = useNavigate();

  const idCliente = idRouter.id;
  const podeCancelarAssinatura = dadosCancelamento?.dataCancelamento === null;

  const formMethods = useForm<FormData>({
    resolver: isUpdateFormDominio ? yupResolverAlterar : yupResolverLojaAlterar,
  });

  const { reset, handleSubmit: onSubmit } = formMethods;

  const extrairDominios = (dominio: string, dominios: DominioProps[]) => {
    const dominioPrincipal =
      dominios?.find(({ principal }) => principal)?.dominio || dominio;
    const dominioDelivery = dominios?.find(
      ({ principal }) => !principal
    )?.dominio;

    const urlDominio = getDominioFromURL(dominioPrincipal);
    const urlDominioDelivery = dominioDelivery
      ? getDominioFromURL(dominioDelivery)
      : opcaoPadraoDominioDelivery?.value;

    return {
      dominio: dominioPrincipal.split('.')[0],
      dominioDelivery: dominioDelivery?.split('.')[0] || '',
      urlDominio,
      urlDominioDelivery,
    };
  };

  const obterDataAssinaturaResponsavel = useCallback(async () => {
    setIsLoading(true);

    const response = await getClienteObterResponsavel(idCliente || '');

    if (response.sucesso) {
      const { dados } = response;
      const camposDominios = extrairDominios(dados.dominio, dados.dominios);

      setExisteDominioDelivery(!!camposDominios.dominioDelivery);

      reset({
        ...dados,
        ...camposDominios,
        telefoneResponsavel: dados.celular,
      });
    }
    setIsLoading(false);
  }, [getClienteObterResponsavel, idCliente]);

  const handleObterAssinaturas = useCallback(async () => {
    const response = await api.get<void, ResponseApi<AssinaturaProps>>(
      ConstantEnderecoWebservice.OBTER_ASSINATURA,
      {
        params: {
          id: idCliente,
        },
      }
    );

    if (response.sucesso) {
      const { dados } = response;
      console.log('Dados da resposta:', dados); // Log para depuração
      
      const {
        responsavel,
        loja,
        servicos,
        servicosContratados, // Verificar se esta propriedade existe na resposta
        tabelaPrecoId,
        dataCancelamento,
        motivoCancelamento,
      } = dados;
      
      console.log('Serviços contratados:', servicosContratados); // Log para depuração
      
      const siglaUf = loja.cidadeNome
        ? String(loja.cidadeNome)?.split('-')[1]?.trim()
        : null;

      const camposDominios = extrairDominios(
        responsavel.dominio,
        responsavel.dominios
      );

      setExisteDominioDelivery(!!camposDominios.dominioDelivery);

      reset({
        ...dados,
        ...responsavel,
        ...loja,
        ...camposDominios,
        cep: {
          value: loja?.cep,
          isGetCep: true,
        },
        telefoneResponsavel: responsavel.celular,
        celular: loja.celular,
        tabelaPrecoId,
        estado: {
          value: siglaUf,
          label: siglaUf ?? '',
        },
        cidadeNome: {
          value: loja.cidadeId,
          label: String(loja.cidadeNome),
        },
        paisNome: {
          value: loja.paisId,
          label: String(loja.paisNome),
        },
        diaVencimento: String(dados.diaVencimento), // Convertendo para string para o SelectDefault
        validacaoAutomatica: dados.validacaoAutomatica ?? true, // Valor padrão true se não vier do backend
      });
      setListServicosAdicionais(servicos);
      setListServicosContratados(servicosContratados || []);
      setListServicosRemovidos(servicos.map(({ servicoId }) => servicoId));
      setDadosCancelamento({ dataCancelamento, motivoCancelamento });
    }
  }, [idCliente, setListServicosAdicionais, setListServicosContratados, setListServicosRemovidos]);

  const servicosRemovidos = useCallback(() => {
    let removedItems = [] as string[];

    listServicoRemovidos.forEach((item) => {
      if (
        !listServicosAdicionais.some(
          (itemServico) => itemServico.servicoId === item
        )
      ) {
        removedItems.push(item);
      }
    });

    return removedItems;
  }, [listServicoRemovidos, listServicosAdicionais])();

  const handleSubmit = onSubmit(async (data: FormData) => {
    setIsLoading(true);
    const dadosAPi = dadosApiAssinatura({
      data,
      servicos: listServicosAdicionais,
      naoExibirResponsavel: true,
      id: idCliente || '',
      servicosExcluidos: servicosRemovidos,
    });

    // Certifique-se de que validacaoAutomatica está sendo enviado
    const response = await api.put<void, ResponseApi<AssinaturaProps>>(
      ConstantEnderecoWebservice.ALTERAR_ASSINATURA,
      {
        ...dadosAPi,
        validacaoAutomatica: data.validacaoAutomatica,
      }
    );
    if (response.sucesso) {
      toast.success('Cadastro alterado com sucesso.');
      navigate(ConstanteRotas.ASSINATURAS);
    }
    setIsLoading(false);
  });

  const alterarClienteResponsavel = async (data: FormData) => {
    setIsLoading(true);

    const {
      email,
      dominio,
      telefoneResponsavel,
      nome,
      bancoDados,
      urlDominio,
      dominioDelivery,
      urlDominioDelivery,
    } = data;

    const dominios = [
      {
        dominio: `${dominio}.${urlDominio}`,
        aplicativo: 0,
        principal: true,
        padrao: true,
      },
      dominioDelivery && {
        dominio: `${dominioDelivery}.${urlDominioDelivery}`,
        aplicativo: 1,
        principal: false,
        padrao: false,
      },
    ].filter(Boolean);

    const response = await api.put<void, ResponseApi>(
      ConstantEnderecoWebservice.ALTERAR_CLIENTE,
      {
        email: email,
        celular: telefoneResponsavel,
        nome: nome,
        id: idCliente,
        bancoDados: bancoDados,
        dominios,
      }
    );

    if (response.sucesso) {
      toast.success('Cadastro alterado com sucesso.');
      navigate(ConstanteRotas.ASSINATURAS);
    }

    setIsLoading(false);
  };

  const handleAlterarClienteResponsavel = onSubmit(async (data) => {
    const dominioDeliveryPreenchido = !!data.dominioDelivery;

    if (existeDominioDelivery && !dominioDeliveryPreenchido) {
      ModalRemoverDominio({
        aoConfirmar: async ({ onClose }) => {
          alterarClienteResponsavel(data);
          onClose();
        },
        aoCancelar: ({ onClose }) => onClose(),
      });

      return;
    }

    alterarClienteResponsavel(data);
  });

  const handleCancelarAssinatura = useCallback(async () => {
    ModalCancelar({
      isVisibleMotivo: podeCancelarAssinatura,
      title: 'Você tem certeza?',
      colorScheme: podeCancelarAssinatura ? 'red' : 'orange',
      description: podeCancelarAssinatura
        ? 'Antes de confirmar, informe o motivo do cancelamento.'
        : 'Deseja reativar a assinatura?',
      confirmButtonText: `Sim, ${
        podeCancelarAssinatura ? 'cancelar!' : 'reativar!'
      }`,
      cancelButtonText: 'Cancelar',
      onConfirm: async (motivo) => {
        const response = await api.put<void, ResponseApi>(
          `${
            ConstantEnderecoWebservice.ASSINATURA_CANCELAR
          }?assinaturaID=${idCliente}&motivoCancelamento=${
            podeCancelarAssinatura ? motivo : null
          }`
        );
        if (response.sucesso) {
          toast.success('A assinatura foi cancelada com sucesso');
          navigate(ConstanteRotas.ASSINATURAS);

          return true;
        }
        return true;
      },
    });
  }, [podeCancelarAssinatura, navigate, idCliente]);

  useEffect(() => {
    if (isUpdateFormDominio) {
      obterDataAssinaturaResponsavel();
    } else {
      handleObterAssinaturas();
    }
  }, [
    obterDataAssinaturaResponsavel,
    handleObterAssinaturas,
    isUpdateFormDominio,
  ]);

  return (
    <LayoutFormPage
      onSubmit={() => {
        if (isUpdateFormDominio) {
          handleAlterarClienteResponsavel();
        } else {
          handleSubmit();
        }
      }}
      isDisabledSubmit={!isPlanoAdicionado && !isUpdateFormDominio}
      isLoading={isLoading}
      button={
        auth.usuarioPossuiPermissao(
          ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_CANCELAR_REATIVAR
        ) && (
          <ButtonDefault
            width={['full', 'full', 'full', '220px']}
            color="gray"
            mb={['10px', '10px', '10px', '0']}
            colorScheme="gray"
            variant="outlinePill"
            onClick={() => handleCancelarAssinatura()}
            possuiFuncionalidade={auth.usuarioPossuiPermissao(
              ConstantFuncionalidades.AssinaturaAcao
                .ASSINATURA_CANCELAR_REATIVAR
            )}
          >
            {podeCancelarAssinatura ? 'Cancelar' : 'Reativar assinatura'}
          </ButtonDefault>
        )
      }
    >
      <FormProvider {...formMethods}>
        <FormAssinaturas isAlterar isDominio={isUpdateFormDominio} />
      </FormProvider>
    </LayoutFormPage>
  );
};
