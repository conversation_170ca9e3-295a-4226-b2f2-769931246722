import { useState } from 'react';
import { VStack } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';

import { ButtonDefault } from 'components/Button';
import { Paragraph } from 'components/Layout/Paragraph';
import { Title } from 'components/Layout/Title';
import { Header } from 'components/Layout/Header';

export const SenhaRedefinidaSucesso = () => {
  const [isLoading, setLoading] = useState(false);

  const navigate = useNavigate();

  const handleNavigateToLogin = () => {
    setLoading(true);
    navigate(ConstanteRotas.LOGIN);
    setLoading(false);
  };

  return (
    <VStack spacing={9}>
      <Header />
      <VStack spacing={2} alignItems="flex-start" w="full">
        <Title>Senha redefinida!</Title>
        <Paragraph color="white" fontSize="sm" letterSpacing="normal">
          Agora você pode usar sua nova senha para <br /> entrar na sua conta
        </Paragraph>
      </VStack>

      <ButtonDefault
        onClick={() => handleNavigateToLogin()}
        color="white"
        colorScheme="secondary"
        w="full"
        type="button"
        isDisabled={isLoading}
        isLoading={isLoading}
        possuiFuncionalidade={true}
      >
        Voltar para tela de acesso
      </ButtonDefault>
    </VStack>
  );
};
