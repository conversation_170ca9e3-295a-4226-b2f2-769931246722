import { Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { RegrasFiscaisUf } from 'pages/RegrasFiscaisUf/Listar';
import { CadastrarRegraFiscalUf } from 'pages/RegrasFiscaisUf/Formulario/Cadastrar';
import { AlterarRegraFiscalUf } from 'pages/RegrasFiscaisUf/Formulario/Alterar';

import LayoutGuard from './LayoutGuard';

export const RegrasFiscaisUfRoutes = [
  <Route
    key={ConstanteRotas.REGRAS_FISCAL_UF}
    path={ConstanteRotas.REGRAS_FISCAL_UF}
    element={
      <LayoutGuard
        key={ConstanteRotas.REGRAS_FISCAL_UF}
        breadcrumb={[
          { title: 'Fiscal' },
          { title: 'Regras fiscais Uf', path: ConstanteRotas.REGRAS_FISCAL_UF },
        ]}
        component={<RegrasFiscaisUf />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.AcessoMenu.MENU_FISCAL_REGRA_FISCAL)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.REGRAS_FISCAL_UF_CADASTRAR}
    path={ConstanteRotas.REGRAS_FISCAL_UF_CADASTRAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.REGRAS_FISCAL_UF_CADASTRAR}
        breadcrumb={[
          { title: 'Fiscal' },
          { title: 'Regras fiscais Uf', path: ConstanteRotas.REGRAS_FISCAL_UF },
          {
            title: 'Cadastrar',
          },
        ]}
        component={<CadastrarRegraFiscalUf />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.NotaFiscalRegraUrlAcao.CADASTRAR_REGRAS_FISCAIS)}
      />
    }
  />,
  <Route
    key={ConstanteRotas.REGRAS_FISCAL_UF_ALTERAR}
    path={ConstanteRotas.REGRAS_FISCAL_UF_ALTERAR}
    element={
      <LayoutGuard
        key={ConstanteRotas.REGRAS_FISCAL_UF_ALTERAR}
        breadcrumb={[
          { title: 'Fiscal' },
          { title: 'Regras fiscais Uf', path: ConstanteRotas.REGRAS_FISCAL_UF },
          {
            title: 'Alterar',
          },
        ]}
        component={<AlterarRegraFiscalUf />}
        possuiFuncionalidade={auth.usuarioPossuiPermissao(ConstantFuncionalidades.NotaFiscalRegraUrlAcao.ALTERAR_REGRAS_FISCAIS)}
      />
    }
  />,
];
