import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FiSearch } from 'react-icons/fi';
import { Box, Flex, Td, Tr, Text, Icon } from '@chakra-ui/react';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import api, { ResponseApi } from 'services/api';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import auth from 'modules/auth';

import { ActionsMenu } from 'components/ActionsMenu';
import { ButtonDefault } from 'components/Button';
import { ContainerListagem } from 'components/Grid/GridList';
import { Pagination } from 'components/Grid/Pagination';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { ModalWarning } from 'components/Modal/ModalWarning';

import {
  NotaFiscalValidacoesProps,
  formDefaultValuesListagem,
  FormData,
} from './validationForms';

export const NotaFiscalValidacoes = () => {
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [listaNotaFiscalValidacoes, setListaNotaFiscalValidacoes] = useState<
    NotaFiscalValidacoesProps[]
  >([]);
  const [currentFilters, setCurrentFilters] = useState<FormData>(
    formDefaultValuesListagem
  );

  const formMethods = useForm<FormData>({
    defaultValues: formDefaultValuesListagem,
  });

  const { watch } = formMethods;
  const descricaoNotaWatch = watch('descricao');

  const navigate = useNavigate();

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<NotaFiscalValidacoesProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_NF_VALIDACOES,
          gridPaginadaConsulta
        ),
        {
          params: currentFilters,
        }
      );

      if (response.sucesso) {
        setTotalRegistros(response.dados.total);
        setListaNotaFiscalValidacoes(response.dados.registros);
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const handleExcluirNotaFiscal = useCallback(
    (id: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'Essa ação vai excluir a Nota fiscal!',
        confirmButtonText: 'Sim, continuar!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            ConstantEnderecoWebservice.EXCLUIR_NF_VALIDACAO,
            { params: { id } }
          );

          if (response.sucesso) {
            toast.success(`Cadastro excluído com sucesso.`);

            setRecarregarListagem(!recarregarListagem);
            return true;
          }
          return false;
        },
      });
    },
    [recarregarListagem]
  );

  return (
    <Box>
      <FormProvider {...formMethods}>
        <ContainerListagem
          inputPesquisa={
            <InputDefault
              maxLength={100}
              placeholder="Buscar Nota fiscal"
              iconLeftElement={FiSearch}
              name="descricao"
              onEnterKeyPress={() => {
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  descricao: descricaoNotaWatch,
                }));
              }}
            />
          }
          buttonCadastrar={
            <ButtonDefault
              onClick={() =>
                navigate(ConstanteRotas.NOTA_FISCAL_VALIDACOES_CADASTRAR)
              }
              width={['full', 'full', 'full', '220px']}
              color="white"
              colorScheme="secondary"
              leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
              possuiFuncionalidade={auth.usuarioPossuiPermissao(
                ConstantFuncionalidades.NotaFicalValidacaoAcao
                  .CADASTRAR_NF_VALIDACOES
              )}
            >
              Cadastrar novo
            </ButtonDefault>
          }
        >
          <Pagination
            loadColumnsData={paginationHandle}
            nPages={totalRegistros}
            currentPage={page}
            setCurrentPage={setPage}
            isLoading={isLoading}
            defaultOrderDirection="asc"
            defaultKeyOrdered="Codigo"
            tableHeaders={[
              {
                content: 'Código',
                key: 'codigo',
                isOrderable: false,
                width: '160px',
              },
              {
                content: 'Descrição',
                key: 'Descricao',
                isOrderable: false,
                width: 'auto',
              },
              {
                content: 'Ações',
                key: 'Acoes',
                isOrderable: false,
                isNumeric: true,
                width: '10px',
              },
            ]}
            renderTableRows={listaNotaFiscalValidacoes.map((notaFiscal) => (
              <Tr key={notaFiscal.codigo}>
                <Td>{notaFiscal.codigo}</Td>
                <Td pb="3px" pt="3px">
                  <Flex w="full">
                    <Text maxW="full" whiteSpace="pre-line">
                      {notaFiscal.descricao}
                    </Text>
                  </Flex>
                </Td>
                <Td isNumeric>
                  <Flex justifyContent="right">
                    <ActionsMenu
                      id="notaFiscalAcoes"
                      items={[
                        {
                          content: 'Editar',
                          onClick: () => {
                            navigate(
                              SubstituirParametroRota(
                                ConstanteRotas.NOTA_FISCAL_VALIDACOES_ALTERAR,
                                'id',
                                notaFiscal.codigo
                              )
                            );
                          },
                          possuiFuncionalidade: auth.usuarioPossuiPermissao(
                            ConstantFuncionalidades.NotaFicalValidacaoAcao
                              .ALTERAR_NF_VALIDACOES
                          ),
                        },
                        {
                          content: 'Excluir',
                          onClick: () => {
                            handleExcluirNotaFiscal(notaFiscal.codigo);
                          },
                          possuiFuncionalidade: auth.usuarioPossuiPermissao(
                            ConstantFuncionalidades.NotaFicalValidacaoAcao
                              .EXCLUIR_NF_VALIDACOES
                          ),
                        },
                      ]}
                    />
                  </Flex>
                </Td>
              </Tr>
            ))}
          />
        </ContainerListagem>
      </FormProvider>
    </Box>
  );
};
