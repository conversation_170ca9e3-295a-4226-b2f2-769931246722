import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FiSearch } from 'react-icons/fi';
import { Box, Flex, Td, Tr, Text, Icon, Divider, Link } from '@chakra-ui/react';
import { IoIosAddCircleOutline } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  ConstanteRotas,
  SubstituirParametroRota,
} from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';
import { EnumStatusCadastros } from 'constants/Enum/enumStatusCadastros';
import { formatQueryPagedTable } from 'helpers/queryParamsFormat';
import { formatPhoneNumber } from 'helpers/format/fieldsMasks';
import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';

import { ActionsMenu } from 'components/ActionsMenu';
import { ButtonDefault } from 'components/Button';
import { ContainerListagem } from 'components/Grid/GridList';
import { Pagination } from 'components/Grid/Pagination';
import {
  GridPaginadaConsulta,
  GridPaginadaRetorno,
} from 'components/Grid/Pagination/Types/validationForms';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { SelectDefault } from 'components/Select/SelectDefault';
import { ModalWarning } from 'components/Modal/ModalWarning';
import { StatusCircle } from 'components/Grid/Pagination/StatusCircle';

type RevendasProps = {
  id: string;
  ativo: boolean;
  nomeFantasia: string;
  nomeResponsavel: string;
  telefone: string;
  celular: string;
  cidadeUF: string;
};

type RevendaAtivaProps = {
  label: string;
  value: boolean | null;
};

type FormData = {
  nome: string;
  ativo: RevendaAtivaProps;
};

export const Revendas = () => {
  const [page, setPage] = useState(1);
  const [recarregarListagem, setRecarregarListagem] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [listaRevendas, setListaRevendas] = useState<RevendasProps[]>([]);
  const [currentFilters, setCurrentFilters] = useState<FormData>(
    {} as FormData
  );

  const formMethods = useForm<FormData>({
    defaultValues: {
      ativo: EnumStatusCadastros.TODOS,
      nome: '',
    },
  });
  const { watch } = formMethods;
  const nomeDaRevendaWatch = watch('nome');

  const navigate = useNavigate();

  const possuiPermissaoAlterarRevenda = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.CadastroRevendaAcao.ALTERAR_REVENDAS
  );
  const possuiPermissaoLogarRevenda = auth.usuarioPossuiPermissao(
    ConstantFuncionalidades.CadastroRevendaAcao.LOGAR_REVENDAS
  );

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: GridPaginadaConsulta) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<RevendasProps>>
      >(
        formatQueryPagedTable(
          ConstantEnderecoWebservice.LISTAR_REVENDAS,
          gridPaginadaConsulta
        ),
        {
          params: currentFilters,
        }
      );

      if (response.sucesso) {
        setTotalRegistros(response.dados.total);
        setListaRevendas(response.dados.registros);
      }

      setIsLoading(false);
    },
    [currentFilters, recarregarListagem]
  );

  const handleExcluirRevenda = useCallback(
    async (id: string) => {
      ModalWarning({
        title: 'Você tem certeza?',
        description: 'A ação irá excluir a revenda!',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        onConfirm: async () => {
          const response = await api.delete<void, ResponseApi>(
            ConstantEnderecoWebservice.EXCLUIR_REVENDA,
            { params: { id } }
          );

          if (response.sucesso) {
            setRecarregarListagem(!recarregarListagem);
            toast.success('Cadastro excluído com sucesso.');
            return true;
          }

          return false;
        },
      });
    },
    [recarregarListagem]
  );

  const handleAlterarRevenda = useCallback(
    (revendaId: string) => {
      if (!possuiPermissaoAlterarRevenda) return;
      navigate(
        SubstituirParametroRota(ConstanteRotas.REVENDA_ALTERAR, 'id', revendaId)
      );
    },
    [possuiPermissaoAlterarRevenda]
  );

  const handleLogarRevenda = useCallback(
    (id: string) => {
      if (!possuiPermissaoLogarRevenda) return;
      return api
        .put<void, ResponseApi>(
          `${ConstantEnderecoWebservice.TROCAR_REVENDA}?revendaId=${id}`
        )
        .then(async (res) => {
          if (res.sucesso && res.dados) {
            auth.setToken(res.dados);
            const token = await auth.getToken();
            api.defaults.headers.common.Authorization = `Bearer ${token}`;
            navigate(ConstanteRotas.ASSINATURAS);
          } else {
            // refresh token inválido.
            auth.clearTokenAndRedirect();
          }
        });
    },
    [possuiPermissaoLogarRevenda]
  );

  return (
    <Box>
      <FormProvider {...formMethods}>
        <ContainerListagem
          inputPesquisa={
            <InputDefault
              maxLength={100}
              placeholder="Buscar por nome"
              iconLeftElement={FiSearch}
              name="nome"
              onEnterKeyPress={() => {
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  nome: nomeDaRevendaWatch,
                }));
              }}
            />
          }
          filtrosListagem={
            <SelectDefault
              name="ativo"
              placeholder="Selecione um filtro"
              filtrosAtivos
              onSelect={(optionSelecionada) =>
                setCurrentFilters((filtrosJaAdicionados) => ({
                  ...filtrosJaAdicionados,
                  ativo: optionSelecionada?.value,
                }))
              }
              options={EnumStatusCadastros.properties.map((status) => status)}
            />
          }
          buttonCadastrar={
            <ButtonDefault
              onClick={() => navigate(ConstanteRotas.REVENDA_CADASTRAR)}
              width={['full', 'full', 'full', '220px']}
              color="white"
              colorScheme="secondary"
              leftIcon={<Icon as={IoIosAddCircleOutline} boxSize="20px" />}
              possuiFuncionalidade={auth.usuarioPossuiPermissao(
                ConstantFuncionalidades.CadastroRevendaAcao.CADASTRAR_REVENDAS
              )}
            >
              Cadastrar novo
            </ButtonDefault>
          }
        >
          <Pagination
            loadColumnsData={paginationHandle}
            nPages={totalRegistros}
            currentPage={page}
            setCurrentPage={setPage}
            isLoading={isLoading}
            defaultOrderDirection="asc"
            defaultKeyOrdered="nomeFantasia"
            tableHeaders={[
              {
                content: <StatusCircle hasValue={false} />,
                key: 'ativo',
                isOrderable: false,
                width: '1px',
              },
              {
                content: 'Nome',
                key: 'nomeFantasia',
                isOrderable: true,
                width: 'auto',
              },
              {
                content: 'Responsável',
                key: 'nomeResponsavel',
                width: 'auto',
              },
              {
                content: 'Cidade',
                key: 'cidade',
                width: 'auto',
                isOrderable: true,
              },
              {
                content: 'Celular',
                key: 'celular',
                width: 'auto',
                isOrderable: false,
              },
              {
                content: 'Ações',
                key: 'Acoes',
                isOrderable: false,
                isNumeric: true,
                width: '10px',
              },
            ]}
            renderTableRows={listaRevendas.map((revenda) => (
              <Tr key={revenda.id}>
                <Td>
                  <StatusCircle isActive={revenda.ativo} />
                </Td>
                <Td pb="3px" pt="3px">
                  <Flex w="full">
                    <Link
                      lineHeight="12.5px"
                      maxW="full"
                      whiteSpace="pre-line"
                      cursor="pointer"
                      onClick={() => handleAlterarRevenda(revenda.id)}
                    >
                      {revenda.nomeFantasia}
                    </Link>
                  </Flex>
                </Td>
                <Td>{revenda.nomeResponsavel}</Td>
                <Td>
                  {revenda.cidadeUF ? (
                    <Text>{revenda.cidadeUF}</Text>
                  ) : (
                    <Divider h="1px" background="black" maxW="85px" />
                  )}
                </Td>
                <Td>
                  {revenda.celular ? (
                    <Text>{formatPhoneNumber(revenda.celular)}</Text>
                  ) : (
                    <Divider h="1px" background="black" maxW="85px" />
                  )}
                </Td>
                <Td>
                  <Flex justifyContent="right">
                    <ActionsMenu
                      id="revendaAcoes"
                      items={[
                        {
                          content: 'Logar',
                          onClick: () => {
                            handleLogarRevenda(revenda.id);
                          },
                          possuiFuncionalidade: possuiPermissaoLogarRevenda,
                        },
                        {
                          content: 'Editar',
                          onClick: () => {
                            handleAlterarRevenda(revenda.id);
                          },
                          possuiFuncionalidade: possuiPermissaoAlterarRevenda,
                        },
                        {
                          content: 'Excluir',
                          onClick: () => {
                            handleExcluirRevenda(revenda.id);
                          },
                          possuiFuncionalidade: auth.usuarioPossuiPermissao(
                            ConstantFuncionalidades.CadastroRevendaAcao
                              .EXCLUIR_REVENDAS
                          ),
                        },
                      ]}
                    />
                  </Flex>
                </Td>
              </Tr>
            ))}
          />
        </ContainerListagem>
      </FormProvider>
    </Box>
  );
};
