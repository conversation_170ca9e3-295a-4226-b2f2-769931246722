import React, { useEffect } from 'react';
import { PathRouteProps } from 'react-router-dom';

import { useLayoutContext } from 'store/Layout';
import ProtectedRoute from 'routes/ProtectedRoute';

import { LayoutMenu } from 'components/Layout/Menu';

export interface BreadcrumbInterface {
  title: string;
  path?: string;
}

export interface LayoutGuardInterface extends PathRouteProps {
  key: string;
  breadcrumb?: BreadcrumbInterface[];
  component: React.ReactNode;
  possuiFuncionalidade: boolean;
}

const LayoutGuard = ({
  breadcrumb,
  component,
  possuiFuncionalidade = false,
}: LayoutGuardInterface) => {
  const { setBreadcrumb } = useLayoutContext();

  useEffect(() => {
    if (breadcrumb) {
      setBreadcrumb(breadcrumb);
    }
  }, []);

  return (
    <ProtectedRoute
      component={<LayoutMenu>{component}</LayoutMenu>}
      possuiFuncionalidade={possuiFuncionalidade}
    />
  );
};

export default LayoutGuard;
