import { useCallback, useEffect, useState } from 'react';
import { create, InstanceProps } from 'react-modal-promise';
import {
  Flex,
  GridItem,
  Td,
  Tr,
  Text,
  Box,
  Table,
  Tbody,
  Th,
  Thead,
  useDisclosure,
  useMediaQuery,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalProps,
  Divider,
  Grid,
  SimpleGrid,
  Card,
  Link,
} from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { FiCopy } from 'react-icons/fi';

import api, { ResponseApi } from 'services/api';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { moneyMask } from 'helpers/format/fieldsMasks';
import formatUTCToLocateDate from 'helpers/format/formatUTCToLocateDate';
import { capitalize } from 'helpers/format/stringFormats';

import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { LoadingDefault } from 'components/Loading';
import { ButtonDefault } from 'components/Button';
import { IconTooltip } from 'components/IconTooltip';
import { ModalDefaultChakra } from 'components/Modal/ModalDefaultChakra';

import { FormData, defaultValues, FaturamentoProps } from './validationForm';

import auth from 'modules/auth';
import { EnumTipoUsuario } from 'constants/Enum/enumTipoUsuario';

type ModalFaturamentoAssinaturaProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> & {
  faturamentoId: string;
} & InstanceProps<ModalProps>;

export const ModalDetalhesFaturamento = create<
  ModalFaturamentoAssinaturaProps,
  ModalProps
>(({ onResolve, onReject, faturamentoId, ...rest }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [faturamento, setFaturamento] = useState<FaturamentoProps>(
    {} as FaturamentoProps
  );

  const isAdmin = auth.usuarioPossuiPermissao([EnumTipoUsuario.SISTEMA_ADMIN]);

  const formMethods = useForm<FormData>({
    defaultValues: defaultValues,
  });

  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  const getFaturamento = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<void, ResponseApi<FaturamentoProps>>(
      ConstantEnderecoWebservice.OBTER_DETALHES_FATURAMENTO_ASSINATURA,
      {
        params: {
          faturamentoId: faturamentoId,
        },
      }
    );

    if (response.sucesso) {
      // Use the data directly as returned from the API
      setFaturamento(response.dados);
    }
    setIsLoading(false);
  }, [faturamentoId]);

  useEffect(() => {
    getFaturamento();
  }, [getFaturamento]);

  return (
    <ModalDefaultChakra
      {...rest}
      isCentered
      size={isLargerThan900 ? '6xl' : 'full'}
      isOpen={isOpen}
      onClose={onClose}
      autoFocus={false}
    >
      <FormProvider {...formMethods}>
        <ModalContent
          h={['full', 'fit-content']}
          maxH="full"
          w={['full', '5xl', '8xl']}
          bg="gray.50"
        >
          {isLoading && <LoadingDefault />}
          <ModalHeader
            borderTopRadius="5px"
            bg="primary.500"
            pb="15px"
            borderBottom="1px solid"
            borderColor="gray.50"
          >
            <Flex
              color="white"
              pt="5px"
              justifyContent="space-between"
              alignItems="center"
            >
              <Text pl="2px" color="white">
                Detalhes faturamento
              </Text>
              <ModalCloseButton
                id="closeButton"
                mt="13px"
                mr="10px"
                color="gray.50"
              />
            </Flex>
          </ModalHeader>

          <ModalBody pt="20px" backgroundColor="white">
            <SimpleGridForm
              backgroundColor="primary.500"
              boxShadow="xs"
              rounded="md"
            >
              <GridItem colSpan={[12, 12, 8, 8]}>
                <Grid px={['24px', '24px', '24px', '48px']} py="18px">
                  <Text fontSize="md" fontWeight="normal" color="white">
                    Assinatura
                  </Text>
                  <Text
                    fontSize={['xl', '2xl']}
                    color="secondary.400"
                    fontWeight="semibold"
                  >
                    {faturamento?.fantasia && capitalize(faturamento?.fantasia)}
                  </Text>
                </Grid>
              </GridItem>  
              <GridItem colSpan={[12, 12, 4, 4]}>
                <Grid
                  gap="5px"
                  px={['24px', '24px', '24px', '48px']}
                  py="18px"
                  placeItems={['flex-start', 'flex-start', 'flex-end']}
                >
                  <Text fontSize="md" fontWeight="normal" color="white">
                    Total de Repasse
                  </Text>
                  <Text
                    fontSize={['xl', 'xl', 'xl', '2xl']}
                    color="secondary.400"
                    fontWeight="semibold"
                  >
                    {moneyMask(faturamento?.totalRepasse, true)}
                  </Text>
                </Grid>
              </GridItem>
            </SimpleGridForm>

            {/* Segunda linha com SubTotal e Acréscimo/Desconto */}
            {(faturamento?.valorAcrescimoDesconto != 0) && (
              <SimpleGridForm
                backgroundColor="primary.500"
                boxShadow="xs"
                rounded="md"
                mt="10px"
              >
                <GridItem colSpan={[12, 6, 2, 2]}>
                  <Grid
                    gap="5px"
                    px={['24px', '24px', '24px', '24px']}
                    py="18px"
                    placeItems={['flex-start', 'flex-start', 'flex-start']}
                  >
                    <Text fontSize="md" fontWeight="normal" color="white">
                      SubTotal
                    </Text>
                    <Text
                      fontSize={['xl', 'xl', 'xl', '2xl']}
                      color="secondary.400"
                      fontWeight="semibold"
                    >
                      {moneyMask(faturamento?.subTotal || 0, true)}
                    </Text>
                  </Grid>
                </GridItem>
                <GridItem colSpan={[12, 6, 2, 2]}>
                  <Grid
                    gap="5px"
                    px={['24px', '24px', '24px', '24px']}
                    py="18px"
                    placeItems={['flex-start', 'flex-start', 'flex-start']}
                  >
                    <Text fontSize="md" fontWeight="normal" color="white">
                      Acrésc/Desc
                    </Text>
                    <Text
                      fontSize={['xl', 'xl', 'xl', '2xl']}
                      color="secondary.400"
                      fontWeight="semibold"
                    >
                      {moneyMask(faturamento?.valorAcrescimoDesconto || 0, true)}
                    </Text>
                  </Grid>
                </GridItem>
                <GridItem colSpan={[12, 12, 8, 8]}>
                  <Grid
                    gap="5px"
                    px={['24px', '24px', '24px', '24px']}
                    py="18px"
                    placeItems={['flex-start', 'flex-start', 'flex-start']}
                  >
                    <Text fontSize="md" fontWeight="normal" color="white">
                      Motivo
                    </Text>
                    <Text
                      fontSize={['md', 'md', 'md', 'lg']}
                      color="secondary.400"
                      fontWeight="normal"
                      noOfLines={1}
                      title={faturamento?.motivoAcrescimoDesconto}
                    >
                      {faturamento?.motivoAcrescimoDesconto}
                    </Text>
                  </Grid>
                </GridItem>
              </SimpleGridForm>
            )}
            <SimpleGridForm mt="24px">              
              <GridItem colSpan={12}>
                {faturamento?.itensFatura?.length > 0 ? (
<Box
  w="full"
  overflow="auto"
  maxH="400px"
  boxShadow="0px 0px 6px #00000034"
  rounded="md"
>
  <Table
    sx={{
      '& th': {
        fontSize: '2xs',
        color: 'primary.500',
        border: 'none',
        backgroundColor: 'gray.50',
      },
      '& tr': {
        border: 'none',
      },
      '& td': {
        borderColor: 'gray.50',
      },
    }}
  >
    <Thead>
      <Tr>
        <Th fontSize="xs" w="20%">
          Serviço
        </Th>
        <Th w="15%">Qtde</Th>
        <Th w="15%" isNumeric>
          Vlr. Unitário Repasse
        </Th>
        <Th w="15%" isNumeric>
          Vlr. Total Repasse
        </Th>
      </Tr>
    </Thead>
    <Tbody>
      {faturamento.itensFatura.map((item, index) => (
        <Tr
          key={item.id}
          sx={{ marginTop: index === 0 ? '0' : '2px' }}
        >
          <Td minW="180px">{item?.servicoNome}</Td>
          <Td>{item.quantidade}</Td>
          <Td isNumeric>
            {moneyMask(item.valorUnitarioRepasse, true)}
          </Td>
          <Td isNumeric>
            {moneyMask(item.valorTotalRepasse, true)}
          </Td>
        </Tr>
      ))}
    </Tbody>
  </Table>
</Box>
                ) : (
                  <Text mt="20px" fontSize="xs">
                    Nenhum faturamento encontrado
                  </Text>
                )}
              </GridItem>
            </SimpleGridForm>
          </ModalBody>
          <ModalFooter backgroundColor="white">
            <Flex
              w="full"
              justifyContent="center"
              flexDirection={['column', 'row', 'row']}
            >
              <ButtonDefault
                width={['full', '240px', '120px', '120px']}
                mr="20px"
                mb={['20px', 'undefined', 'undefined']}
                variant="outlinePill"
                onClick={() => onClose()}
                possuiFuncionalidade={true}
                backgroundColor="white"
                border="1px solid"
                borderColor="gray.100"
              >
                Fechar
              </ButtonDefault>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </FormProvider>
    </ModalDefaultChakra>
  );
});
