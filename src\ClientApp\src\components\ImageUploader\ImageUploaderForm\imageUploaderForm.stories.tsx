import { Meta, StoryFn } from '@storybook/react';

import { ImageUploaderProps } from '.';
import { ImageUploaderForm as ImageUploader } from './exampleImageUploaderForm';

export default {
  title: 'Components/ImageUploaderForm',
  component: ImageUploader,
  args: {
    name: 'imageUploader',
    id: 'imageUploader',
    label: 'Imagem',
    isRequired: true,
    width: 250,
    isAlterarImagem: true,
    ratio: 16 / 9,
  },
} as Meta;

export const ImageUploaderForm: StoryFn<ImageUploaderProps> = (props) => {
  return (
    <ImageUploader
      {...props}
      name={props.name}
      id={props.id}
      label={props.label}
      maxBytes={props.maxBytes}
      maxResolucao={props.maxResolucao}
      isRequired={props.isRequired}
    />
  );
};
