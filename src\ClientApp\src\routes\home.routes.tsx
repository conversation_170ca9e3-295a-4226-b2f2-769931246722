import { Route } from 'react-router-dom';
import { Box } from '@chakra-ui/react';

import { ConstanteRotas } from 'constants/constantRotas';

import LayoutGuard from './LayoutGuard';

export const HomeRoutes = [
  <Route
    key={ConstanteRotas.HOME}
    path={ConstanteRotas.HOME}
    element={
      <LayoutGuard
        key={ConstanteRotas.HOME}
        breadcrumb={[{ title: 'Home' }]}
        component={
          <Box w="full" textAlign="center" fontSize="2xl">
            Bem vindo
          </Box>
        }
        possuiFuncionalidade={true}
      />
    }
  />,
];
