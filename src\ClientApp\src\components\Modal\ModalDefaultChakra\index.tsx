import React from 'react';
import { Box, Modal, ModalOverlay, ModalProps } from '@chakra-ui/react';

import PortalFullscreen from 'components/ActionsMenu/PortalFullscreen';

interface PropsModalPadraoChakra extends ModalProps {
  children: React.ReactNode;
  hasOverlay?: boolean;
}
export const ModalDefaultChakra = ({
  children,
  hasOverlay = true,
  ...props
}: PropsModalPadraoChakra) => {
  return (
    <PortalFullscreen>
      <Modal autoFocus allowPinchZoom {...props}>
        {hasOverlay && <ModalOverlay />}
        {children}
      </Modal>
    </PortalFullscreen>
  );
};
