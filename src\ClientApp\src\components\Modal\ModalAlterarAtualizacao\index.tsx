import {
  <PERSON>lex,
  <PERSON>rid<PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalProps,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create, InstanceProps } from 'react-modal-promise';
import { toast } from 'react-toastify';

import { useEffectDefault } from 'hook/useEffectDefault';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import api, { ResponseApi } from 'services/api';

import { ButtonDefault } from 'components/Button';
import { SimpleGridForm } from 'components/Grid/SimpleGridForm';
import { InputDefault } from 'components/Input/InputChakra/InputDefault';
import { LoadingDefault } from 'components/Loading';
import { SwitchDefault } from 'components/Switch';
import { TextAreaDefault } from 'components/TextArea';

import { ModalDefaultChakra } from '../ModalDefaultChakra';
import { formDefaultValues, yupResolver, FormData } from './validationForms';

interface AtualizacaoSistemaResponse {
  id: string;
  versao: string;
  exibirNorasVersao: boolean;
  notasVersao: string;
  possuiAtualizacaoBancoDados: boolean;
}

type ModalAlterarAtualizacaoProps = Omit<
  ModalProps,
  'children' | 'isOpen' | 'onClose'
> & {
  id: string;
  atualizarListagem: () => void;
} & InstanceProps<FormData>;

export const ModalAlterarAtualizacao = create<
  ModalAlterarAtualizacaoProps,
  FormData
>(({ id, atualizarListagem, ...rest }) => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, watch, reset } = formMethods;
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

  const [possuiAtualizacaoBancoDadosWatch, exibirNotasVersaoWatch] = watch([
    'possuiAtualizacaoBancoDados',
    'exibirNotasVersao',
  ]);

  const onSubmit = handleSubmit(async (data) => {
    setIsLoading(true);
    const response = await api.put<void, ResponseApi>(
      ConstantEnderecoWebservice.ALTERAR_ATUALIZACAO,
      {
        id,
        ...data,
      }
    );
    setIsLoading(false);
    if (response.sucesso) {
      toast.success('Cadastro alterado com sucesso.');
      atualizarListagem();
      onClose();
    }
  });

  const getAtualizacao = useCallback(async () => {
    setIsLoading(true);
    const response = await api.get<
      void,
      ResponseApi<AtualizacaoSistemaResponse>
    >(ConstantEnderecoWebservice.OBTER_ATUALIZACAO, {
      params: { id: id },
    });

    if (response.sucesso) {
      const { dados } = response;
      const valuesReset = {
        ...dados,
      };

      reset(valuesReset);
    }
    setIsLoading(false);
  }, [reset]);

  useEffectDefault(() => {
    getAtualizacao();
  }, [id]);

  return (
    <ModalDefaultChakra
      {...rest}
      isCentered
      size={['full', 'full', '5xl']}
      isOpen={isOpen}
      onClose={onClose}
      autoFocus={false}
    >
      <FormProvider {...formMethods}>
        <ModalContent
          bg="gray.50"
          w={['full', 'full', 'full']}
          h={['full', `${exibirNotasVersaoWatch ? '500px' : '300px'}`]}
        >
          {isLoading && <LoadingDefault />}
          <ModalHeader borderTopRadius="5px" bg="primary.600" pb="15px">
            <Flex
              color="white"
              pt="5px"
              justifyContent="space-between"
              alignItems="center"
            >
              <Text pl="2px">Cadastrar atualização</Text>
              <ModalCloseButton mt="13px" mr="10px" />
            </Flex>
          </ModalHeader>
          <ModalBody pt="0">
            <SimpleGridForm mt="25px" mb="24px">
              <GridItem colSpan={[12, 12, 3]}>
                <InputDefault
                  label="Versão"
                  fontLabel="sm"
                  width="32"
                  colorLabel="gray.600"
                  isRequired
                  name="versao"
                />
              </GridItem>
              <GridItem colSpan={[12, 12, 4]}>
                <SwitchDefault
                  name="possuiAtualizacaoBancoDados"
                  id="possuiAtualizacaoBancoDados"
                  label="Possui atualização no banco de dados"
                  isChecked={possuiAtualizacaoBancoDadosWatch}
                />
              </GridItem>
              <GridItem colSpan={[12, 12, 3]}>
                <SwitchDefault
                  name="exibirNotasVersao"
                  id="exibirNotasVersao"
                  label="Exibir notas da versão"
                  isChecked={exibirNotasVersaoWatch}
                />
              </GridItem>
            </SimpleGridForm>

            {exibirNotasVersaoWatch && (
              <TextAreaDefault
                name="notasVersao"
                id="notasVersao"
                label="Notas da versão"
                minHeight="56"
              />
            )}
          </ModalBody>
          <ModalFooter>
            <Flex
              w="full"
              justifyContent={['center', 'center', 'right']}
              flexDirection={['column', 'row', 'row']}
            >
              <ButtonDefault
                width={['full', '120px', '120px', '120px']}
                colorScheme="gray"
                mr="20px"
                mb={['20px', 'undefined', 'undefined']}
                variant="outlinePill"
                onClick={() => onClose()}
                possuiFuncionalidade={true}
              >
                Cancelar
              </ButtonDefault>
              <ButtonDefault
                width={['full', '120px', '120px', '120px']}
                color="white"
                colorScheme="secondary"
                onClick={() => onSubmit()}
                possuiFuncionalidade={true}
              >
                Salvar
              </ButtonDefault>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </FormProvider>
    </ModalDefaultChakra>
  );
});
