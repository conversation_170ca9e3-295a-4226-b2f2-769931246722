import { Navigate } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import auth from 'modules/auth';

export type ProtectedRouteProps = {
  component: JSX.Element;
  possuiFuncionalidade: boolean;
};

export default function ProtectedRoute({
  component,
  possuiFuncionalidade,
}: ProtectedRouteProps) {
  const isAuthenticated = (() => {
    return auth.isAuthenticated();
  })();

  if (auth.getExpSessao()) {
    auth.clearToken();
    return <Navigate to={ConstanteRotas.LOGIN} />;
  }
  if (isAuthenticated && possuiFuncionalidade) {
    return component;
  } else if (isAuthenticated) {
    return <Navigate to={ConstanteRotas.ASSINATURAS} />;
  } else {
    return <Navigate to={ConstanteRotas.LOGIN} />;
  }
}
