import React, { ChangeEvent, useCallback, useEffect } from 'react';
import { InputProps } from '@chakra-ui/react';
import { Controller, useFormContext } from 'react-hook-form';
import { toast } from 'react-toastify';

import { cepMask } from 'helpers/format/fieldsMasks';
import consultarViaCep, { CepResponse } from 'services/viacep';

import { InputChakra } from '..';

export const getFormattedValue = (value: string) => {
  const unformattedValue = value.replace(/\D/g, '');

  return cepMask(unformattedValue);
};

export type CepValue = {
  value: string | undefined;
  isGetCep: boolean;
};

export interface InputCepProps extends InputProps {
  name: string;
  label?: string;
  colorLabel?: string;
  paisId?: number;
  getInfoCep?: (data: CepResponse) => void;
}

export const InputCep = ({
  onChange,
  label,
  name,
  isDisabled,
  getInfoCep,
  colorLabel = 'black',
  isRequired,
  paisId = 1,
  placeholder,
  fontSize = 'sm',
  ...rest
}: InputCepProps) => {
  const { setError, watch, clearErrors } = useFormContext();

  const valueCepWatch = watch(`${name}`);

  const isUpdateCep = valueCepWatch?.value !== undefined ? false : true;

  const cepWatch =
    valueCepWatch?.value !== undefined ? valueCepWatch?.value : valueCepWatch;

  const getDataCep = useCallback(
    (sucesso: boolean, dados: CepResponse | string) => {
      if (!sucesso) {
        toast.warning(dados as string);
        return;
      }

      setError('cep', { message: '' });
      clearErrors('cep');

      if (getInfoCep) getInfoCep(dados as CepResponse);
    },
    [getInfoCep, setError]
  );

  const getCepInfo = useCallback(async (cep: string) => {
    if (!cep || paisId !== 1) return;

    consultarViaCep(cep, getDataCep);
  }, []);

  useEffect(() => {
    if (cepWatch?.length >= 9 && getInfoCep && isUpdateCep) {
      getCepInfo(cepWatch);
    }
  }, [getCepInfo, cepWatch, isUpdateCep]);

  return (
    <Controller
      name={name}
      render={({
        field: { onChange, onBlur, value: valueInput, name },
        fieldState: { error },
      }) => {
        const value =
          valueInput?.value !== undefined ? valueInput?.value : valueInput;

        return (
          <InputChakra
            onInput={(e: React.FormEvent<HTMLInputElement>) => {
              if (e.currentTarget.value)
                e.currentTarget.value = getFormattedValue(
                  e.currentTarget.value
                );
            }}
            onChange={(e: ChangeEvent<HTMLInputElement>) => {
              e.currentTarget.value = getFormattedValue(e.currentTarget.value);

              if (onChange) onChange(e);
            }}
            label={label}
            maxLength={9}
            placeholder={placeholder}
            name={name}
            isRequired={isRequired}
            value={getFormattedValue(value || '') as any}
            isDisabled={isDisabled}
            onBlur={onBlur}
            error={error}
            colorLabel={colorLabel}
            fontSize={fontSize}
            {...rest}
          />
        );
      }}
    />
  );
};
