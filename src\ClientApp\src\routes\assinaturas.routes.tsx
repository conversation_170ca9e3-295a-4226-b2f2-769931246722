import { Outlet, Route } from 'react-router-dom';

import { ConstanteRotas } from 'constants/constantRotas';
import auth from 'modules/auth';
import AssinaturaProvider from 'store/Assinaturas/AssinaturaContext';
import { ConstantFuncionalidades } from 'constants/constantFuncionalidades';

import { CadastrarAssinaturas } from 'pages/Assinaturas/Formulario/Cadastrar';
import { AlterarAssinaturas } from 'pages/Assinaturas/Formulario/Alterar';
import { ListarAssinatura } from 'pages/Assinaturas/Listar';
import { FaturamentoAssinatura } from 'pages/Faturamento';


import LayoutGuard from './LayoutGuard';

export const ContextAssinatura = () => {
  return (
    <AssinaturaProvider>
      <Outlet />
    </AssinaturaProvider>
  );
};

export const AssinaturasRoutes = (
  <Route element={<ContextAssinatura />}>
    <Route
      key={ConstanteRotas.ASSINATURAS}
      path={ConstanteRotas.ASSINATURAS}
      element={
        <LayoutGuard
          key={ConstanteRotas.ASSINATURAS}
          breadcrumb={[{ title: 'Assinaturas' }]}
          component={<ListarAssinatura />}
          possuiFuncionalidade={true}
        />
      }
    />
    <Route
      key={ConstanteRotas.ASSINATURAS_CADASTRAR}
      path={ConstanteRotas.ASSINATURAS_CADASTRAR}
      element={
        <LayoutGuard
          key={ConstanteRotas.ASSINATURAS_CADASTRAR}
          breadcrumb={[
            { title: 'Assinaturas', path: ConstanteRotas.ASSINATURAS },
            { title: 'Cadastrar' },
          ]}
          component={<CadastrarAssinaturas />}
          possuiFuncionalidade={auth.usuarioPossuiPermissao(
            ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_CADASTRAR
          )}
        />
      }
    />
    <Route
      key={ConstanteRotas.ASSINATURAS_ALTERAR}
      path={ConstanteRotas.ASSINATURAS_ALTERAR}
      element={
        <LayoutGuard
          key={ConstanteRotas.ASSINATURAS_ALTERAR}
          breadcrumb={[
            { title: 'Assinaturas', path: ConstanteRotas.ASSINATURAS },
            { title: 'Alterar' },
          ]}
          component={<AlterarAssinaturas />}
          possuiFuncionalidade={auth.usuarioPossuiPermissao(
            ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_ALTERAR &&
            ConstantFuncionalidades.AssinaturaAcao.CONTA_CLIENTE_ALTERAR
          )}
        />
      }
    />
    <Route
      key={ConstanteRotas.ASSINATURAS_FATURAMENTO}
      path={ConstanteRotas.ASSINATURAS_FATURAMENTO}
      element={
        <LayoutGuard
          key={ConstanteRotas.ASSINATURAS_FATURAMENTO}
          breadcrumb={[
            { title: 'Assinaturas', path: ConstanteRotas.ASSINATURAS },
            { title: 'Faturamento' },
          ]}
          component={<FaturamentoAssinatura />}
          possuiFuncionalidade={auth.usuarioPossuiPermissao(
            ConstantFuncionalidades.AssinaturaAcao.ASSINATURA_EXIBIR_FATURAMENTO
          )}
        />
      }
    />

  </Route>
);
