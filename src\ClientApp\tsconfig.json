{
  "compilerOptions": {
    "target": "ESNext",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "types": ["cypress", "node"],
    "baseUrl": "src",
    "noFallthroughCasesInSwitch": true,
  },
  "include": [
    "src",
    "vite.config.ts",
    "cypress.config.ts",
    "./cypress/**/*.ts",
    "cypress.d.ts"
  ],
  "exclude": ["**/node_modules", "**/.*/"]
}
