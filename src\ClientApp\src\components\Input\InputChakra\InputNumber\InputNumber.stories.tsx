import { Meta, StoryFn } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';

import { theme } from 'theme';

import { InputNumber as Input } from './exampleInput';
import { InputNumberProps } from '.';

export default {
  title: 'Components/Input',
  argTypes: getThemingArgTypes(theme as any, 'Input'),
  args: {
    name: 'inputNumber',
    label: 'Label',
    colorLabel: 'pink.500',
    fontSize: 'sm',
    leftElement: 'R$',
    leftElementColor: 'pink.500',
  },
} as Meta<InputNumberProps>;

export const InputNumber: StoryFn<InputNumberProps> = (props) => {
  return (
    <Input
      name={props.name}
      size={props.size}
      fontSize={props.fontSize}
      fontLabel={props.fontLabel}
      colorLabel={props.colorLabel}
      label={props.label}
      w={props.size}
      variant={props.variant}
      leftElement={props.leftElement}
      leftElementColor={props.leftElementColor}
    />
  );
};
