import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

export const formDefaultValues = {
  quantidade: 1,
  servico: null,
  valorUnitarioRepasse: 0,
  valorTotalRepasse: 0,
};

export type Servicos = {
  descricaoAdicional: string;
  quantitativo: boolean;
  servicoId: string;
  tipo: number;
  valorRepasse: number;
  valorUnitarioRepasse: number;
  valorTotalRepasse: number;
  nome: string;
  label?: string;
  value?: string;
};

const schema = yup.object().shape({
  quantidade: yup.number().when('servico', (servico: Servicos, field) => {
    return servico.quantitativo
      ? field.required(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
      : yup.number();
  }),
  servico: yup
    .object()
    .nullable()
    .default(null)
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO),
  valorUnitarioRepasse: yup.number(),
});

export const yupResolver = yupResolverInstance(schema);
