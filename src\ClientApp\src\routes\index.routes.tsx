import { Routes } from 'react-router-dom';

import SignalR from 'store/SignalR';

import { AtualizacaoRoutes } from './atualizacao.routes';
import { AutenticacaoRoutes } from './autenticacao.routes';
import { HomeRoutes } from './home.routes';
import { LogErrosRoutes } from './log-erros.routes';
import { NfceQrCodeRoutes } from './nfce-qrcode.routes';
import { NotaFiscalValidacoesRoutes } from './nota-fiscal-validacoes.routes';
import { ImportacoesNCMRoutes } from './importacoes-ncm.routes';
import { NotaFiscalRejeicoesRoutes } from './notas-fiscais-rejeicoes.routes';
import { RegrasFiscaisUfRoutes } from './regras-fiscais-uf.routes';
import { NotaFiscalServicoURLRoutes } from './nota-fiscal-servicos-url.routes';
import { RoutePaginaNaoExiste } from './rota-nao-existe.routes';
import { RevendasRoutes } from './revendas.routes';
import { ProdutosRoutes } from './cadastro-produtos.routes';
import { AssinaturasRoutes } from './assinaturas.routes';
import { ServicosRoutes } from './cadastro-servicos.routes';
import { GradesServicosRoutes } from './cadastro-grade-servicos.routes';
import { UsuariosRoutes } from './usuarios.routes';
import { ZenflixRoutes } from './zenflix.routes';
import { FaturamentoExibicaoRoutes } from './faturamento.roures';
import { FaturamentoConferenciaRoutes } from './faturamento.roures';

export const RoutesContent = () => {
  return (
    <SignalR>
      <Routes>
        {AutenticacaoRoutes}
        {HomeRoutes}
        {RoutePaginaNaoExiste}
        {AtualizacaoRoutes}
        {RegrasFiscaisUfRoutes}
        {NotaFiscalValidacoesRoutes}
        {NotaFiscalServicoURLRoutes}
        {NfceQrCodeRoutes}
        {LogErrosRoutes}
        {NotaFiscalRejeicoesRoutes}
        {NotaFiscalRejeicoesRoutes}
        {ImportacoesNCMRoutes}
        {RevendasRoutes}
        {ProdutosRoutes}
        {ServicosRoutes}
        {GradesServicosRoutes}
        {AssinaturasRoutes}
        {UsuariosRoutes}
        {ZenflixRoutes}
        {FaturamentoExibicaoRoutes}
        {FaturamentoConferenciaRoutes}
      </Routes>
    </SignalR>
  );
};
