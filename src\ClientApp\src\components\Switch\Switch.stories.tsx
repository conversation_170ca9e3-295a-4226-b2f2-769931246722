import { Meta, StoryFn } from '@storybook/react';

import { SwitchDefault } from './exampleSwitch';
import { SwitchDefaultProps } from '.';

export default {
  title: 'Components/Switch',
  component: SwitchDefault,
  args: {
    name: 'switch',
    id: 'switch',
    label: 'Label',
    isRequired: false,
    colorLabel: 'black',
    colorScheme: 'blue',
    size: 'md',
    fontLabel: 'sm',
    fontWeight: 'normal',
    isDisabled: false,
    textoAuxiliar: '',
  },
} as Meta;

export const Switch: StoryFn<SwitchDefaultProps> = (props) => {
  return (
    <SwitchDefault
      colorLabel={props.colorLabel}
      label={props.label}
      isRequired={props.isRequired}
      colorScheme={props.colorScheme}
      fontLabel={props.fontLabel}
      fontWeight={props.fontWeight}
      isDisabled={props.isDisabled}
      textoAuxiliar={props.textoAuxiliar}
      id={props.id}
      name={props.name}
      size={props.size}
    />
  );
};
