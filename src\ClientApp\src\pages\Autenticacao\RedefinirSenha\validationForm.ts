import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { EnumValidacoesSistema } from 'constants/Enum/enumValidacoesSistema';

const schema = yup.object().shape({
  senha: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .min(6, EnumValidacoesSistema.SENHA_MINIMO_SEIS_CARACTERES)
    .matches(/[0-9]/, EnumValidacoesSistema.SENHA_UM_CARACTER_NUMERICO)
    .matches(/[a-z]/, EnumValidacoesSistema.SENHA_UMA_LETRA_MINUSCULA)
    .matches(/[A-Z]/, EnumValidacoesSistema.SENHA_UMA_LETRA_MAIUSCULA)
    .matches(
      /([^a-zA-Z\d])+([a-zA-Z\d])+|([a-zA-Z\d])+([^a-zA-Z\d])+/,
      EnumValidacoesSistema.SENHA_UM_CARACTER_ESPECIAL
    ),
  confirmarSenha: yup
    .string()
    .required(EnumValidacoesSistema.CAMPO_OBRIGATORIO)
    .oneOf([yup.ref('senha'), ''], EnumValidacoesSistema.SENHA_NAO_COINCIDEM),
});

export const yupResolver = yupResolverInstance(schema);
