import { GroupHeadingProps, components } from 'chakra-react-select';
import { Text } from '@chakra-ui/react';

const groupStyles = {
  div: {
    color: 'white',
    fontWeight: 'bold',
    background: '#38B7BF',
    fontSize: '14px',
    padding: '5px 8px',
  },
};

export const GroupHeading = (props: GroupHeadingProps<any>) => (
  <Text {...props} sx={groupStyles}>
    <components.GroupHeading {...props} />
  </Text>
);
