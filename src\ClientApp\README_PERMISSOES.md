# 🔐 Sistema de Mapeamento de Permissões

## 📋 Visão Geral

Este sistema automatiza a geração de mapeamentos de ações e permissões a partir dos arquivos TypeScript da pasta `Funcionalidades`. O sistema analisa automaticamente todos os arquivos e gera planilhas Excel, arquivos CSV e páginas HTML interativas com as permissões atualizadas.

## 🚀 Como Usar

### Comando Principal (Recomendado)
```bash
node atualizar_permissoes.cjs
```

Este comando executa todo o processo automaticamente:
1. ✅ Analisa todos os arquivos TypeScript da pasta `Funcionalidades`
2. ✅ Extrai ações e permissões automaticamente
3. ✅ Gera arquivo Excel atualizado
4. ✅ Gera arquivo CSV atualizado
5. ✅ Gera página HTML interativa atualizada
6. ✅ Exibe estatísticas detalhadas

### Comandos Individuais
```bash
# Apenas análise dos arquivos
node analisar_funcionalidades.cjs

# Apenas Excel
node gerar_excel.cjs

# Apenas CSV
node gerar_csv_com_constantes.cjs

# Apenas HTML
node gerar_html.cjs
```

## 📁 Arquivos Gerados

### 📊 Excel: `Mapeamento_Acoes_Funcionalidades_com_Constantes.xlsx`
- Planilha completa com todas as ações e permissões
- Formato: SIM/NÃO para cada tipo de usuário
- Ordenação alfabética das ações
- Inclui nomes das constantes (ex: `AssinaturaAcao.CADASTRAR`)

### 📄 CSV: `Mapeamento_Acoes_Funcionalidades_com_Constantes.csv`
- Arquivo CSV para importação em outras ferramentas
- Mesmo formato da planilha Excel
- Compatível com Excel, Google Sheets, etc.

### 🌐 HTML: `Mapeamento_Acoes_Funcionalidades_com_Constantes.html`
- Página web interativa e responsiva
- Funcionalidades:
  - 🔍 Busca em tempo real
  - 📋 Copiar tabela para Excel
  - 💾 Download CSV
  - 📈 Estatísticas por tipo de usuário
  - 📱 Design responsivo

## 📊 Estatísticas Atuais

**Total de ações analisadas:** 124  
**Total de permissões:** 354  
**Arquivos TypeScript analisados:** 18

### Por Tipo de Usuário:
- **SISTEMA_ADMIN:** 124 permissões
- **DESENVOLVEDOR:** 124 permissões  
- **CANAIS_GERENTE:** 38 permissões
- **ANALISTA_CONTEUDO:** 25 permissões
- **SISTEMA_FINANCEIRO:** 20 permissões
- **REVENDA_ADMIN:** 15 permissões
- **SUPORTE_STI3:** 8 permissões
- **REVENDA_ASSISTENTE:** 0 permissões

## 🔧 Arquitetura do Sistema

### Scripts Principais:
1. **`atualizar_permissoes.cjs`** - Script principal que coordena todo o processo
2. **`analisar_funcionalidades.cjs`** - Analisa arquivos TypeScript e extrai dados
3. **`gerar_excel.cjs`** - Gera planilha Excel
4. **`gerar_csv_com_constantes.cjs`** - Gera arquivo CSV
5. **`gerar_html.cjs`** - Gera página HTML interativa

### Fluxo de Dados:
```
Arquivos TypeScript (Funcionalidades/*.ts)
    ↓
Análise Automática (analisar_funcionalidades.cjs)
    ↓
Extração de Ações e Permissões
    ↓
Geração de Arquivos (Excel, CSV, HTML)
    ↓
Arquivos Atualizados com Dados Mais Recentes
```

## 📝 Formato dos Arquivos de Funcionalidades

Os arquivos TypeScript devem seguir este padrão:

```typescript
import { EnumTipoUsuario } from 'constants/Enum/enumTipoUsuario';

export const MinhaConstanteAcao = {
  MINHA_ACAO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.DESENVOLVEDOR,
  ],
  OUTRA_ACAO: [
    EnumTipoUsuario.SISTEMA_ADMIN,
    EnumTipoUsuario.CANAIS_GERENTE,
  ],
};
```

## 🔄 Integração com GitHub Actions

O sistema está integrado com GitHub Actions através do workflow `.github/workflows/update-permissions.yml`:

- ✅ Execução automática quando arquivos da pasta `Funcionalidades` são modificados
- ✅ Execução manual através do GitHub interface
- ✅ Commit automático dos arquivos atualizados
- ✅ Upload de artefatos para download

## 🛠️ Dependências

### Node.js:
- `xlsx` - Para geração de arquivos Excel
- `fs` - Para manipulação de arquivos (nativo)
- `path` - Para manipulação de caminhos (nativo)

### Não há dependências Python - Sistema 100% Node.js

## 📋 Troubleshooting

### Problema: "Nenhuma ação foi encontrada"
**Solução:** Verifique se:
- A pasta `src/constants/Funcionalidades` existe
- Os arquivos TypeScript seguem o padrão correto
- As importações do `EnumTipoUsuario` estão corretas

### Problema: "Arquivo não foi gerado"
**Solução:** Verifique se:
- Você tem permissões de escrita no diretório
- O Node.js está instalado corretamente
- A dependência `xlsx` está instalada (`npm install xlsx`)

### Problema: "Erro ao analisar arquivo"
**Solução:** Verifique se:
- O arquivo TypeScript tem sintaxe válida
- A constante é exportada corretamente
- Os arrays de permissões estão no formato correto

## 🎯 Próximas Melhorias

- [ ] Cache de análise para melhor performance
- [ ] Validação de integridade dos dados
- [ ] Geração de relatórios de diferenças
- [ ] Interface web para visualização em tempo real
- [ ] Integração com sistema de notificações

## 📞 Suporte

Para dúvidas ou problemas:
1. Verifique este README
2. Execute `node atualizar_permissoes.cjs` para diagnóstico
3. Consulte os logs de erro detalhados
4. Entre em contato com a equipe de desenvolvimento

---

**Última atualização:** 25/06/2025  
**Versão do sistema:** 2.0 (Node.js apenas)  
**Compatibilidade:** Node.js 14+
