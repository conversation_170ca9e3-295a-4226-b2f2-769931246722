import { Meta } from '@storybook/react';
import { getThemingArgTypes } from '@chakra-ui/storybook-addon';
import { Flex } from '@chakra-ui/react';

import { theme } from 'theme';
import { valuesMenu } from 'services/dataActionsMenu';

import { ActionsMenu as Menu } from '.';

export default {
  title: 'Components/ActionsMenu',
  component: Menu,
  argTypes: getThemingArgTypes(theme as any, 'Menu'),
} as Meta;

export const ActionsMenu = () => {
  return (
    <Flex justifyContent="flex-start">
      <Menu id="menuButton" items={valuesMenu} />
    </Flex>
  );
};
