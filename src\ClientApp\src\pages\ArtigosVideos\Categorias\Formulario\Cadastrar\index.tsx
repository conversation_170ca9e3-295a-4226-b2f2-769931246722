import React, { useCallback, useState } from 'react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { FormProvider, useForm } from 'react-hook-form';

import api, { ResponseApi } from 'services/api';
import { ConstanteRotas } from 'constants/constantRotas';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';

import { LayoutFormPage } from 'components/Layout/FormPage';

import FormCategoriaTreinamento from '..';
import { formDefaultValues, yupResolver, FormData } from '../validationForm';

const CadastraCategoriaTreinamento = () => {
  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, reset } = formMethods;
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(false);

  const handleCadastrar = useCallback(
    async (data: any, resetAfterSuccess?: boolean) => {
      setIsLoading(true);
      const response = await api.post<void, ResponseApi<FormData>>(
        ConstantEnderecoWebservice.CADASTRAR_CATEGORIA,
        data
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }
        if (response.sucesso) {
          toast.success('Cadastro realizado com sucesso');
          if (resetAfterSuccess) {
            reset(formDefaultValues);
          } else {
            navigate(ConstanteRotas.CATEGORIAS_TREINAMENTO);
          }
        }
      }
      setIsLoading(false);
    },
    []
  );

  const handleCadastrarCategoria = handleSubmit((data) => {
    handleCadastrar(data);
  });

  const handleCadastrarInserirCategoria = handleSubmit((data) => {
    handleCadastrar(data, true);
  });

  return (
    <LayoutFormPage
      onResetSubmit={() => handleCadastrarInserirCategoria()}
      onSubmit={() => handleCadastrarCategoria()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormCategoriaTreinamento />
      </FormProvider>
    </LayoutFormPage>
  );
};

export default CadastraCategoriaTreinamento;
