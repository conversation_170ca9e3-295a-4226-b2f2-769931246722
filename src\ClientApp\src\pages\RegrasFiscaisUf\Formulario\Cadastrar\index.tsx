import { useCallback, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';
import { ConstantEnderecoWebservice } from 'constants/constantEnderecoWebservice';
import { ConstanteRotas } from 'constants/constantRotas';

import { LayoutFormPage } from 'components/Layout/FormPage';

import { FormRegraFiscalUf } from '..';
import { formDefaultValues, yupResolver, FormData } from '../validationForms';

export const CadastrarRegraFiscalUf = () => {
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });
  const { handleSubmit, reset } = formMethods;

  const navigate = useNavigate();

  const getResponseCadastroRegraFiscal = useCallback(async (data: FormData) => {
    let modeloDocSelecionada = '';
    const modeloDoc = data.modelosDocumento?.map((item) => item.value);

    modeloDoc?.forEach((value) => {
      modeloDocSelecionada = modeloDocSelecionada + `${value},`;
    });

    const response = await api.post<void, ResponseApi<FormData>>(
      ConstantEnderecoWebservice.CADASTRAR_REGRA_FISCAL_UF,
      {
        ...data,
        modelosDocumento: modeloDocSelecionada,
        estados: data?.estados?.map((estado) => estado.value).join(','),
        ativo: true,
      }
    );
    return response;
  }, []);

  const handleCadastrarRegraFiscalUf = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await getResponseCadastroRegraFiscal(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      navigate(ConstanteRotas.REGRAS_FISCAL_UF);
    }
    setIsLoading(false);
  });

  const handleCadastrarInserirNovoRegraFiscalUf = handleSubmit(async (data) => {
    setIsLoading(true);

    const response = await getResponseCadastroRegraFiscal(data);

    if (response.sucesso) {
      toast.success('Cadastro realizado com sucesso.');
      reset(formDefaultValues);
    }
    setIsLoading(false);
  });

  return (
    <LayoutFormPage
      onSubmit={() => handleCadastrarRegraFiscalUf()}
      onResetSubmit={() => handleCadastrarInserirNovoRegraFiscalUf()}
      isLoading={isLoading}
    >
      <FormProvider {...formMethods}>
        <FormRegraFiscalUf />
      </FormProvider>
    </LayoutFormPage>
  );
};
