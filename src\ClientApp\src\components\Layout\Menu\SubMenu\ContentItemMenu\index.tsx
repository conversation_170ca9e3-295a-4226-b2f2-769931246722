import {
  Flex,
  Icon,
  Text,
  IconProps,
  ComponentWithAs,
  Tooltip,
} from '@chakra-ui/react';
import { useEffect } from 'react';
import { IconType } from 'react-icons';

import { ContentItemProps, useLayoutContext } from 'store/Layout';

type ContentItemMenuProps = {
  children: React.ReactNode;
  handlePushNavigation?: () => void;
  hasDisplayIcon?: boolean;
  colorHover?: string;
  background?: string;
  hasItemContent?: boolean;
  valueItemContent?: ContentItemProps[];
  iconMenu?: IconType | ComponentWithAs<'svg', IconProps>;
  keyMenu: string;
  possuiFuncionalidade: boolean;
  color?: string;
};

export const ContentItemMenu = ({
  children,
  keyMenu = '',
  handlePushNavigation,
  colorHover = 'primary.400',
  background = 'primary.400',
  iconMenu,
  hasDisplayIcon = false,
  hasItemContent = false,
  valueItemContent = [],
  possuiFuncionalidade = false,
}: ContentItemMenuProps) => {
  const {
    setValueItemMenu,
    setIsOpenMenu,
    handleClickOutside,
    handleGetKeyName,
    isOpenMenu,
    nameItemMenu,
  } = useLayoutContext();

  const handleClick = () => {
    if (hasItemContent) {
      setIsOpenMenu((valorAnterior) => {
        if (valueItemContent.length > 0 && keyMenu !== nameItemMenu) {
          handleClickOutside(true);
        } else {
          handleClickOutside(false);
        }

        return !valorAnterior;
      });
      setValueItemMenu(valueItemContent);
    }
    if (handlePushNavigation) {
      handlePushNavigation();
      setValueItemMenu([]);
    }
  };

  useEffect(() => {
    if (isOpenMenu) {
      handleGetKeyName(keyMenu);
    } else {
      handleGetKeyName('');
    }
  }, [keyMenu, isOpenMenu, handleGetKeyName]);

  return possuiFuncionalidade ? (
    <Flex
      mb="5px"
      zIndex="9999"
      pl="15px"
      pb="7px"
      pt="7px"
      background={keyMenu === nameItemMenu ? background : undefined}
      onClick={() => handleClick()}
      _hover={{
        background: colorHover,
        transition: 'all ease 0.5s',
      }}
      justifyContent="center"
      alignItems="center"
    >
      {hasDisplayIcon ? (
        <Tooltip placement="right" hasArrow bg="secondary.600" label={children}>
          <Flex w="full" justifyContent="left" alignItems="center">
            <Icon cursor="pointer" as={iconMenu} color="white" boxSize="20px" />
          </Flex>
        </Tooltip>
      ) : (
        <>
          {iconMenu && (
            <Flex w="40px" justifyContent="left" alignItems="center">
              <Icon
                cursor="pointer"
                as={iconMenu}
                color="white"
                boxSize="20px"
              />
            </Flex>
          )}
          <Text
            cursor="pointer"
            w="200px"
            fontSize="14px"
            fontWeight="bold"
            textAlign="left"
          >
            {children}
          </Text>
        </>
      )}
    </Flex>
  ) : null;
};
